@echo off
REM WOW Bingo Game - Comprehensive Build Script
REM ===========================================
REM
REM This script tries multiple build approaches in order of preference:
REM 1. Simple Nuitka build (fastest, best performance)
REM 2. PyInstaller fallback (most reliable)
REM 3. Original build system (if others fail)

echo.
echo ================================================================================
echo WOW Bingo Game - Comprehensive Build System
echo ================================================================================
echo.
echo This script will try multiple build approaches to ensure success:
echo 1. Simple Nuitka build (best performance)
echo 2. PyInstaller fallback (most reliable)
echo 3. Original build system (if needed)
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

echo Python found. Starting comprehensive build process...
echo.

REM Attempt 1: Simple Nuitka Build
echo ================================================================================
echo ATTEMPT 1: Simple Nuitka Build (Best Performance)
echo ================================================================================
echo.

python nuitka_build_simple.py --verbose

if errorlevel 0 (
    echo.
    echo ================================================================================
    echo SUCCESS: Simple Nuitka build completed!
    echo ================================================================================
    echo.
    echo Your high-performance executable is ready in the 'dist' directory.
    echo This Nuitka build provides the best performance and smallest size.
    echo.
    goto :success
)

echo.
echo Simple Nuitka build failed. Trying PyInstaller fallback...
echo.

REM Attempt 2: PyInstaller Fallback
echo ================================================================================
echo ATTEMPT 2: PyInstaller Fallback Build (Most Reliable)
echo ================================================================================
echo.

python build_fallback.py --verbose

if errorlevel 0 (
    echo.
    echo ================================================================================
    echo SUCCESS: PyInstaller fallback build completed!
    echo ================================================================================
    echo.
    echo Your reliable executable is ready in the 'dist' directory.
    echo This PyInstaller build works on all Windows systems.
    echo.
    goto :success
)

echo.
echo PyInstaller build also failed. Trying original build system...
echo.

REM Attempt 3: Original Build System
echo ================================================================================
echo ATTEMPT 3: Original Build System
echo ================================================================================
echo.

python build_executable.py --install-deps

if errorlevel 0 (
    echo.
    echo ================================================================================
    echo SUCCESS: Original build system completed!
    echo ================================================================================
    echo.
    echo Your executable is ready in the 'dist' directory.
    echo.
    goto :success
)

REM All attempts failed
echo.
echo ================================================================================
echo ALL BUILD ATTEMPTS FAILED
echo ================================================================================
echo.
echo Unfortunately, all build methods failed. This could be due to:
echo.
echo 1. Missing Visual Studio Build Tools
echo    Solution: Install from https://visualstudio.microsoft.com/visual-cpp-build-tools/
echo.
echo 2. Missing Python dependencies
echo    Solution: pip install -r requirements.txt
echo.
echo 3. Insufficient system resources
echo    Solution: Close other applications, free up disk space
echo.
echo 4. System configuration issues
echo    Solution: Try building on a different Windows PC
echo.
echo Please check the error messages above and try the suggested solutions.
echo.
pause
exit /b 1

:success
echo Next steps:
echo 1. Test the executable by running it from the dist folder
echo 2. Copy the dist folder contents to distribute your application
echo 3. The executable includes all necessary assets and dependencies
echo.
echo The executable can run on any Windows PC without Python installation.
echo.
pause
exit /b 0
