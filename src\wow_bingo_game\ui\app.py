"""
WOW Bingo Game - Main Application UI
===================================

Main application class that manages the game UI and coordinates
between different components.

Features:
- Modern Flet-based UI
- Responsive design
- Component management
- State management
- Event handling
- Theme support
"""

import asyncio
from typing import Optional, Dict, Any
from pathlib import Path

import flet as ft
from loguru import logger

from ..core.config import WOWBingoConfig
from ..core.game_engine import BingoGameEngine
from ..audio.manager import AudioManager
from ..utils.logger import PerformanceLogger
from .components.board_selection import BoardSelectionView
from .components.game_view import GameView
from .components.settings_view import SettingsView
from .components.stats_view import StatsView


class WOWBingoApp:
    """Main application class."""
    
    def __init__(self, page: ft.Page, config: WOWBingoConfig, dev_mode: bool = False):
        """Initialize the application.
        
        Args:
            page: Flet page object
            config: Application configuration
            dev_mode: Development mode flag
        """
        self.page = page
        self.config = config
        self.dev_mode = dev_mode
        
        # Core components
        self.game_engine: Optional[BingoGameEngine] = None
        self.audio_manager: Optional[AudioManager] = None
        self.performance_logger = PerformanceLogger()
        
        # UI components
        self.board_selection: Optional[BoardSelectionView] = None
        self.game_view: Optional[GameView] = None
        self.settings_view: Optional[SettingsView] = None
        self.stats_view: Optional[StatsView] = None
        
        # UI state
        self.current_view = "board_selection"
        self.navigation_rail: Optional[ft.NavigationRail] = None
        self.content_area: Optional[ft.Container] = None
        self.main_container: Optional[ft.Container] = None
        
        # Application state
        self.initialized = False
        self.selected_cartellas = []
        
        logger.info("WOW Bingo App initialized")
    
    async def initialize(self) -> bool:
        """Initialize the application components.
        
        Returns:
            True if initialization successful
        """
        try:
            if self.initialized:
                return True
                
            logger.info("Initializing application components...")
            self.performance_logger.start_timer("app_initialization")
            
            # Initialize game engine
            self.game_engine = BingoGameEngine(self.config)
            if not await self.game_engine.initialize():
                logger.error("Failed to initialize game engine")
                return False
            
            # Initialize audio manager
            self.audio_manager = AudioManager(self.config.audio)
            if not self.audio_manager.initialize():
                logger.warning("Audio manager initialization failed")
            
            # Initialize UI components
            await self._initialize_ui_components()
            
            # Setup page properties
            self._setup_page()
            
            self.initialized = True
            elapsed = self.performance_logger.end_timer("app_initialization", "INFO")
            logger.info(f"Application initialization completed in {elapsed:.2f}s")
            
            return True
            
        except Exception as e:
            logger.error(f"Application initialization failed: {e}")
            return False
    
    async def _initialize_ui_components(self) -> None:
        """Initialize UI components."""
        try:
            # Initialize views
            self.board_selection = BoardSelectionView(
                page=self.page,
                config=self.config,
                game_engine=self.game_engine,
                on_cartellas_selected=self._on_cartellas_selected
            )
            
            self.game_view = GameView(
                page=self.page,
                config=self.config,
                game_engine=self.game_engine,
                audio_manager=self.audio_manager
            )
            
            self.settings_view = SettingsView(
                page=self.page,
                config=self.config,
                on_settings_changed=self._on_settings_changed
            )
            
            self.stats_view = StatsView(
                page=self.page,
                config=self.config,
                game_engine=self.game_engine
            )
            
            # Initialize all views
            await asyncio.gather(
                self.board_selection.initialize(),
                self.game_view.initialize(),
                self.settings_view.initialize(),
                self.stats_view.initialize()
            )
            
            logger.info("UI components initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize UI components: {e}")
            raise
    
    def _setup_page(self) -> None:
        """Setup page properties and theme."""
        try:
            # Set page properties
            self.page.title = f"{self.config.app_name} v{self.config.version}"
            self.page.window_width = self.config.display.window_width
            self.page.window_height = self.config.display.window_height
            self.page.window_min_width = self.config.display.min_width
            self.page.window_min_height = self.config.display.min_height
            
            # Set theme
            if self.config.display.theme_mode.value == "dark":
                self.page.theme_mode = ft.ThemeMode.DARK
            else:
                self.page.theme_mode = ft.ThemeMode.LIGHT
            
            # Set window to center
            self.page.window_center()
            
            # Handle window events
            self.page.on_window_event = self._on_window_event
            self.page.on_route_change = self._on_route_change
            
            logger.info("Page setup completed")
            
        except Exception as e:
            logger.error(f"Page setup failed: {e}")
    
    def _create_navigation_rail(self) -> ft.NavigationRail:
        """Create navigation rail for switching between views.
        
        Returns:
            Navigation rail component
        """
        destinations = [
            ft.NavigationRailDestination(
                icon=ft.icons.GRID_VIEW,
                selected_icon=ft.icons.GRID_VIEW,
                label="Board Selection"
            ),
            ft.NavigationRailDestination(
                icon=ft.icons.PLAY_CIRCLE,
                selected_icon=ft.icons.PLAY_CIRCLE_FILLED,
                label="Game"
            ),
            ft.NavigationRailDestination(
                icon=ft.icons.BAR_CHART,
                selected_icon=ft.icons.BAR_CHART,
                label="Statistics"
            ),
            ft.NavigationRailDestination(
                icon=ft.icons.SETTINGS,
                selected_icon=ft.icons.SETTINGS,
                label="Settings"
            )
        ]
        
        self.navigation_rail = ft.NavigationRail(
            selected_index=0,
            label_type=ft.NavigationRailLabelType.ALL,
            min_width=100,
            min_extended_width=200,
            destinations=destinations,
            on_change=self._on_navigation_change
        )
        
        return self.navigation_rail
    
    def _create_main_layout(self) -> ft.Container:
        """Create the main application layout.
        
        Returns:
            Main container with layout
        """
        # Create navigation rail
        nav_rail = self._create_navigation_rail()
        
        # Create content area
        self.content_area = ft.Container(
            content=self.board_selection.get_view() if self.board_selection else ft.Text("Loading..."),
            expand=True,
            padding=ft.padding.all(10)
        )
        
        # Create main layout
        self.main_container = ft.Container(
            content=ft.Row([
                nav_rail,
                ft.VerticalDivider(width=1),
                self.content_area
            ], expand=True),
            expand=True
        )
        
        return self.main_container
    
    async def show(self) -> None:
        """Show the application."""
        try:
            if not self.initialized:
                logger.error("Application not initialized")
                return
            
            logger.info("Showing application...")
            
            # Create and add main layout
            main_layout = self._create_main_layout()
            self.page.add(main_layout)
            
            # Update page
            await self.page.update_async()
            
            # Show initial view (board selection)
            await self._switch_to_view("board_selection")
            
            logger.info("Application shown successfully")
            
        except Exception as e:
            logger.error(f"Failed to show application: {e}")
            raise
    
    async def _switch_to_view(self, view_name: str) -> None:
        """Switch to a different view.
        
        Args:
            view_name: Name of the view to switch to
        """
        try:
            logger.debug(f"Switching to view: {view_name}")
            
            # Get the view component
            view_component = None
            nav_index = 0
            
            if view_name == "board_selection":
                view_component = self.board_selection.get_view()
                nav_index = 0
            elif view_name == "game":
                view_component = self.game_view.get_view()
                nav_index = 1
            elif view_name == "stats":
                view_component = self.stats_view.get_view()
                nav_index = 2
            elif view_name == "settings":
                view_component = self.settings_view.get_view()
                nav_index = 3
            else:
                logger.warning(f"Unknown view: {view_name}")
                return
            
            # Update content area
            if self.content_area and view_component:
                self.content_area.content = view_component
                
                # Update navigation rail selection
                if self.navigation_rail:
                    self.navigation_rail.selected_index = nav_index
                
                # Update current view
                self.current_view = view_name
                
                # Update page
                await self.page.update_async()
                
                logger.debug(f"View switched to: {view_name}")
            
        except Exception as e:
            logger.error(f"Failed to switch to view {view_name}: {e}")
    
    async def _on_navigation_change(self, e) -> None:
        """Handle navigation rail selection change."""
        try:
            index = e.control.selected_index
            view_names = ["board_selection", "game", "stats", "settings"]
            
            if 0 <= index < len(view_names):
                await self._switch_to_view(view_names[index])
            
        except Exception as e:
            logger.error(f"Navigation change error: {e}")
    
    async def _on_cartellas_selected(self, cartellas: list) -> None:
        """Handle cartella selection from board selection view.
        
        Args:
            cartellas: List of selected cartella numbers
        """
        try:
            logger.info(f"Cartellas selected: {cartellas}")
            self.selected_cartellas = cartellas
            
            # Update game engine with selected cartellas
            if self.game_engine:
                await self.game_engine.set_selected_cartellas(cartellas)
            
            # Switch to game view
            await self._switch_to_view("game")
            
        except Exception as e:
            logger.error(f"Error handling cartella selection: {e}")
    
    async def _on_settings_changed(self, settings: Dict[str, Any]) -> None:
        """Handle settings changes.
        
        Args:
            settings: Updated settings dictionary
        """
        try:
            logger.info("Settings changed, updating configuration...")
            
            # Update configuration
            # This would update the config object with new settings
            
            # Apply settings to components
            if self.audio_manager:
                # Update audio settings
                pass
            
            if self.game_engine:
                # Update game settings
                pass
            
            # Save settings
            self.config.save_to_file()
            
        except Exception as e:
            logger.error(f"Error handling settings change: {e}")
    
    async def _on_window_event(self, e) -> None:
        """Handle window events."""
        try:
            if e.data == "close":
                logger.info("Window close requested")
                await self.cleanup()
                
        except Exception as e:
            logger.error(f"Window event error: {e}")
    
    async def _on_route_change(self, e) -> None:
        """Handle route changes."""
        try:
            route = e.route
            logger.debug(f"Route changed to: {route}")
            
            # Handle route-based navigation if needed
            
        except Exception as e:
            logger.error(f"Route change error: {e}")
    
    async def cleanup(self) -> None:
        """Cleanup application resources."""
        try:
            logger.info("Cleaning up application...")
            
            # Cleanup components
            if self.game_engine:
                await self.game_engine.cleanup()
            
            if self.audio_manager:
                self.audio_manager.cleanup()
            
            # Cleanup UI components
            cleanup_tasks = []
            for component in [self.board_selection, self.game_view, self.settings_view, self.stats_view]:
                if component and hasattr(component, 'cleanup'):
                    cleanup_tasks.append(component.cleanup())
            
            if cleanup_tasks:
                await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            
            logger.info("Application cleanup completed")
            
        except Exception as e:
            logger.error(f"Application cleanup error: {e}")
