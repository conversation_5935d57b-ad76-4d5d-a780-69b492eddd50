"""
WOW Bingo Game - System Information Utilities
=============================================

System information detection and requirements checking.

Features:
- Hardware detection
- OS compatibility checking
- Performance capabilities assessment
- Requirements validation
- System optimization recommendations
"""

import platform
import sys
import os
from typing import Dict, List, Tuple, Optional
from pathlib import Path

from loguru import logger

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logger.warning("psutil not available - limited system info")


class SystemInfo:
    """System information and requirements checker."""
    
    def __init__(self):
        """Initialize system info collector."""
        self.info = self._collect_system_info()
        self.requirements = self._get_requirements()
        
    def _collect_system_info(self) -> Dict[str, any]:
        """Collect comprehensive system information.
        
        Returns:
            Dictionary with system information
        """
        info = {
            # Basic system info
            "platform": platform.system(),
            "platform_release": platform.release(),
            "platform_version": platform.version(),
            "architecture": platform.architecture(),
            "machine": platform.machine(),
            "processor": platform.processor(),
            "python_version": platform.python_version(),
            "python_implementation": platform.python_implementation(),
            
            # Environment
            "python_executable": sys.executable,
            "current_directory": os.getcwd(),
            "home_directory": str(Path.home()),
            
            # Default values for when psutil is not available
            "cpu_count": os.cpu_count() or 1,
            "memory_total_gb": 0.0,
            "memory_available_gb": 0.0,
            "disk_free_gb": 0.0,
        }
        
        # Enhanced info with psutil if available
        if PSUTIL_AVAILABLE:
            try:
                # CPU information
                info.update({
                    "cpu_count_physical": psutil.cpu_count(logical=False),
                    "cpu_count_logical": psutil.cpu_count(logical=True),
                    "cpu_freq_current": psutil.cpu_freq().current if psutil.cpu_freq() else 0,
                    "cpu_freq_max": psutil.cpu_freq().max if psutil.cpu_freq() else 0,
                })
                
                # Memory information
                memory = psutil.virtual_memory()
                info.update({
                    "memory_total_gb": memory.total / (1024**3),
                    "memory_available_gb": memory.available / (1024**3),
                    "memory_percent_used": memory.percent,
                })
                
                # Disk information
                disk = psutil.disk_usage('/')
                info.update({
                    "disk_total_gb": disk.total / (1024**3),
                    "disk_free_gb": disk.free / (1024**3),
                    "disk_percent_used": (disk.used / disk.total) * 100,
                })
                
                # GPU information (basic)
                info["gpu_info"] = self._get_gpu_info()
                
            except Exception as e:
                logger.warning(f"Error collecting enhanced system info: {e}")
        
        return info
    
    def _get_gpu_info(self) -> List[Dict[str, str]]:
        """Get basic GPU information.
        
        Returns:
            List of GPU information dictionaries
        """
        gpu_info = []
        
        try:
            # Try to get GPU info from different sources
            if platform.system() == "Windows":
                gpu_info = self._get_windows_gpu_info()
            elif platform.system() == "Linux":
                gpu_info = self._get_linux_gpu_info()
            elif platform.system() == "Darwin":  # macOS
                gpu_info = self._get_macos_gpu_info()
                
        except Exception as e:
            logger.debug(f"Could not detect GPU info: {e}")
            
        return gpu_info
    
    def _get_windows_gpu_info(self) -> List[Dict[str, str]]:
        """Get Windows GPU information."""
        import subprocess
        
        try:
            result = subprocess.run([
                "wmic", "path", "win32_VideoController", 
                "get", "name,adapterram", "/format:csv"
            ], capture_output=True, text=True, timeout=10)
            
            gpu_info = []
            for line in result.stdout.split('\n')[1:]:  # Skip header
                if line.strip() and ',' in line:
                    parts = line.split(',')
                    if len(parts) >= 3:
                        gpu_info.append({
                            "name": parts[1].strip(),
                            "memory": parts[2].strip() if parts[2].strip() else "Unknown"
                        })
            
            return gpu_info
            
        except Exception as e:
            logger.debug(f"Windows GPU detection failed: {e}")
            return []
    
    def _get_linux_gpu_info(self) -> List[Dict[str, str]]:
        """Get Linux GPU information."""
        import subprocess
        
        try:
            # Try lspci first
            result = subprocess.run([
                "lspci", "-nn"
            ], capture_output=True, text=True, timeout=10)
            
            gpu_info = []
            for line in result.stdout.split('\n'):
                if 'VGA' in line or 'Display' in line or '3D' in line:
                    gpu_info.append({
                        "name": line.split(': ', 1)[1] if ': ' in line else line,
                        "memory": "Unknown"
                    })
            
            return gpu_info
            
        except Exception as e:
            logger.debug(f"Linux GPU detection failed: {e}")
            return []
    
    def _get_macos_gpu_info(self) -> List[Dict[str, str]]:
        """Get macOS GPU information."""
        import subprocess
        
        try:
            result = subprocess.run([
                "system_profiler", "SPDisplaysDataType"
            ], capture_output=True, text=True, timeout=10)
            
            # Basic parsing - this could be improved
            gpu_info = []
            if "Chipset Model:" in result.stdout:
                gpu_info.append({
                    "name": "macOS GPU (detected)",
                    "memory": "Unknown"
                })
            
            return gpu_info
            
        except Exception as e:
            logger.debug(f"macOS GPU detection failed: {e}")
            return []
    
    def _get_requirements(self) -> Dict[str, any]:
        """Get system requirements for the application.
        
        Returns:
            Dictionary with minimum and recommended requirements
        """
        return {
            "minimum": {
                "python_version": (3, 9),
                "memory_gb": 2.0,
                "disk_space_gb": 1.0,
                "cpu_cores": 2,
                "supported_platforms": ["Windows", "Linux", "Darwin"]
            },
            "recommended": {
                "python_version": (3, 11),
                "memory_gb": 4.0,
                "disk_space_gb": 2.0,
                "cpu_cores": 4,
                "gpu_acceleration": True,
                "supported_platforms": ["Windows", "Linux", "Darwin"]
            }
        }
    
    def check_requirements(self, level: str = "minimum") -> bool:
        """Check if system meets requirements.
        
        Args:
            level: Requirements level ("minimum" or "recommended")
            
        Returns:
            True if requirements are met
        """
        if level not in self.requirements:
            logger.error(f"Unknown requirements level: {level}")
            return False
        
        reqs = self.requirements[level]
        issues = []
        
        # Check Python version
        current_python = sys.version_info[:2]
        required_python = reqs["python_version"]
        if current_python < required_python:
            issues.append(f"Python {required_python[0]}.{required_python[1]}+ required, found {current_python[0]}.{current_python[1]}")
        
        # Check platform
        if self.info["platform"] not in reqs["supported_platforms"]:
            issues.append(f"Platform {self.info['platform']} not officially supported")
        
        # Check memory
        if self.info["memory_total_gb"] < reqs["memory_gb"]:
            issues.append(f"Insufficient memory: {reqs['memory_gb']}GB required, {self.info['memory_total_gb']:.1f}GB available")
        
        # Check disk space
        if self.info["disk_free_gb"] < reqs["disk_space_gb"]:
            issues.append(f"Insufficient disk space: {reqs['disk_space_gb']}GB required, {self.info['disk_free_gb']:.1f}GB available")
        
        # Check CPU cores
        if self.info["cpu_count"] < reqs["cpu_cores"]:
            issues.append(f"Insufficient CPU cores: {reqs['cpu_cores']} required, {self.info['cpu_count']} available")
        
        # Log issues
        if issues:
            logger.warning(f"System requirements check ({level}) failed:")
            for issue in issues:
                logger.warning(f"  - {issue}")
            return False
        else:
            logger.info(f"System requirements check ({level}) passed")
            return True
    
    def get_performance_recommendations(self) -> List[str]:
        """Get performance optimization recommendations.
        
        Returns:
            List of recommendation strings
        """
        recommendations = []
        
        # Memory recommendations
        if self.info["memory_total_gb"] < 4:
            recommendations.append("Consider upgrading to at least 4GB RAM for better performance")
        elif PSUTIL_AVAILABLE and self.info.get("memory_percent_used", 0) > 80:
            recommendations.append("High memory usage detected - close other applications")
        
        # CPU recommendations
        if self.info["cpu_count"] < 4:
            recommendations.append("Multi-core CPU recommended for optimal performance")
        
        # Disk recommendations
        if self.info["disk_free_gb"] < 5:
            recommendations.append("Low disk space - consider freeing up space")
        
        # Platform-specific recommendations
        if self.info["platform"] == "Windows":
            recommendations.append("Enable hardware acceleration in Windows settings")
        elif self.info["platform"] == "Linux":
            recommendations.append("Ensure graphics drivers are up to date")
        
        # Python recommendations
        if sys.version_info < (3, 11):
            recommendations.append("Consider upgrading to Python 3.11+ for better performance")
        
        return recommendations
    
    def log_system_info(self) -> None:
        """Log comprehensive system information."""
        logger.info("=== System Information ===")
        logger.info(f"Platform: {self.info['platform']} {self.info['platform_release']}")
        logger.info(f"Architecture: {self.info['architecture'][0]}")
        logger.info(f"Python: {self.info['python_version']} ({self.info['python_implementation']})")
        logger.info(f"CPU: {self.info['processor']} ({self.info['cpu_count']} cores)")
        logger.info(f"Memory: {self.info['memory_total_gb']:.1f}GB total, {self.info['memory_available_gb']:.1f}GB available")
        logger.info(f"Disk: {self.info['disk_free_gb']:.1f}GB free")
        
        if self.info.get("gpu_info"):
            logger.info("GPU(s):")
            for gpu in self.info["gpu_info"]:
                logger.info(f"  - {gpu['name']}")
        
        # Log recommendations
        recommendations = self.get_performance_recommendations()
        if recommendations:
            logger.info("Performance Recommendations:")
            for rec in recommendations:
                logger.info(f"  - {rec}")
    
    def get_system_summary(self) -> Dict[str, str]:
        """Get a summary of system information for display.
        
        Returns:
            Dictionary with formatted system information
        """
        return {
            "Platform": f"{self.info['platform']} {self.info['platform_release']}",
            "Python": f"{self.info['python_version']} ({self.info['python_implementation']})",
            "CPU": f"{self.info['cpu_count']} cores",
            "Memory": f"{self.info['memory_total_gb']:.1f}GB",
            "Disk Free": f"{self.info['disk_free_gb']:.1f}GB",
            "Architecture": self.info['architecture'][0],
        }
