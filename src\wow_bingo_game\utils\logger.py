"""
WOW Bingo Game - Logging Utilities
==================================

Centralized logging configuration using Loguru for better
logging experience with colors, formatting, and file rotation.

Features:
- Colored console output
- File logging with rotation
- Performance logging
- Error tracking
- Debug mode support
- Log filtering and formatting
"""

import sys
from pathlib import Path
from typing import Optional, Union
import os

from loguru import logger


def setup_logging(
    level: str = "INFO",
    log_file: Optional[Union[str, Path]] = None,
    console_output: bool = True,
    file_rotation: str = "10 MB",
    file_retention: str = "1 week",
    format_string: Optional[str] = None
) -> None:
    """Setup logging configuration.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file (optional)
        console_output: Enable console output
        file_rotation: File rotation size/time
        file_retention: How long to keep old log files
        format_string: Custom format string
    """
    # Remove default handler
    logger.remove()
    
    # Default format with colors for console
    if format_string is None:
        console_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
        file_format = (
            "{time:YYYY-MM-DD HH:mm:ss} | "
            "{level: <8} | "
            "{name}:{function}:{line} | "
            "{message}"
        )
    else:
        console_format = file_format = format_string
    
    # Add console handler if enabled
    if console_output:
        logger.add(
            sys.stderr,
            format=console_format,
            level=level,
            colorize=True,
            backtrace=True,
            diagnose=True
        )
    
    # Add file handler if specified
    if log_file:
        log_file = Path(log_file)
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            str(log_file),
            format=file_format,
            level=level,
            rotation=file_rotation,
            retention=file_retention,
            compression="zip",
            backtrace=True,
            diagnose=True
        )
    
    # Log the setup
    logger.info(f"Logging initialized - Level: {level}, File: {log_file}")


def get_logger(name: str) -> "logger":
    """Get a logger instance with the specified name.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Logger instance
    """
    return logger.bind(name=name)


class PerformanceLogger:
    """Performance logging utilities."""
    
    def __init__(self, logger_instance=None):
        """Initialize performance logger.
        
        Args:
            logger_instance: Logger to use (uses default if None)
        """
        self.logger = logger_instance or logger
        self._timers = {}
    
    def start_timer(self, name: str) -> None:
        """Start a performance timer.
        
        Args:
            name: Timer name
        """
        import time
        self._timers[name] = time.perf_counter()
        self.logger.debug(f"Performance timer started: {name}")
    
    def end_timer(self, name: str, log_level: str = "DEBUG") -> float:
        """End a performance timer and log the duration.
        
        Args:
            name: Timer name
            log_level: Log level for the result
            
        Returns:
            Elapsed time in seconds
        """
        import time
        if name not in self._timers:
            self.logger.warning(f"Timer '{name}' was not started")
            return 0.0
        
        elapsed = time.perf_counter() - self._timers[name]
        del self._timers[name]
        
        # Log with appropriate level
        log_func = getattr(self.logger, log_level.lower(), self.logger.debug)
        log_func(f"Performance: {name} took {elapsed:.4f}s")
        
        return elapsed
    
    def log_memory_usage(self, context: str = "") -> None:
        """Log current memory usage.
        
        Args:
            context: Context description
        """
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            self.logger.debug(f"Memory usage{' (' + context + ')' if context else ''}: {memory_mb:.2f} MB")
        except ImportError:
            self.logger.debug("psutil not available for memory monitoring")
    
    def log_system_info(self) -> None:
        """Log system information."""
        try:
            import platform
            import psutil
            
            self.logger.info(f"System: {platform.system()} {platform.release()}")
            self.logger.info(f"Python: {platform.python_version()}")
            self.logger.info(f"CPU cores: {psutil.cpu_count()}")
            self.logger.info(f"Memory: {psutil.virtual_memory().total / 1024 / 1024 / 1024:.2f} GB")
            
        except ImportError:
            self.logger.info("System info logging requires psutil")


class ErrorTracker:
    """Error tracking and reporting utilities."""
    
    def __init__(self, logger_instance=None):
        """Initialize error tracker.
        
        Args:
            logger_instance: Logger to use (uses default if None)
        """
        self.logger = logger_instance or logger
        self.error_counts = {}
    
    def track_error(self, error: Exception, context: str = "") -> None:
        """Track an error occurrence.
        
        Args:
            error: Exception that occurred
            context: Context where error occurred
        """
        error_type = type(error).__name__
        error_key = f"{error_type}:{str(error)}"
        
        # Increment error count
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # Log the error
        context_str = f" in {context}" if context else ""
        self.logger.error(
            f"Error{context_str}: {error_type}: {error} "
            f"(occurrence #{self.error_counts[error_key]})"
        )
    
    def get_error_summary(self) -> dict:
        """Get summary of tracked errors.
        
        Returns:
            Dictionary with error statistics
        """
        return {
            "total_errors": sum(self.error_counts.values()),
            "unique_errors": len(self.error_counts),
            "error_details": dict(self.error_counts)
        }
    
    def log_error_summary(self) -> None:
        """Log error summary."""
        summary = self.get_error_summary()
        if summary["total_errors"] > 0:
            self.logger.warning(
                f"Error Summary: {summary['total_errors']} total errors, "
                f"{summary['unique_errors']} unique types"
            )
            for error, count in summary["error_details"].items():
                self.logger.warning(f"  {error}: {count} occurrences")
        else:
            self.logger.info("No errors tracked")


class LogFilter:
    """Custom log filtering utilities."""
    
    @staticmethod
    def filter_by_module(record, allowed_modules):
        """Filter logs by module name.
        
        Args:
            record: Log record
            allowed_modules: List of allowed module names
            
        Returns:
            True if log should be processed
        """
        module_name = record.get("name", "")
        return any(module in module_name for module in allowed_modules)
    
    @staticmethod
    def filter_performance_logs(record):
        """Filter performance-related logs.
        
        Args:
            record: Log record
            
        Returns:
            True if log is performance-related
        """
        message = record.get("message", "").lower()
        return any(keyword in message for keyword in ["performance", "timer", "memory", "fps"])


# Global instances for convenience
performance_logger = PerformanceLogger()
error_tracker = ErrorTracker()


def log_function_call(func):
    """Decorator to log function calls.
    
    Args:
        func: Function to decorate
        
    Returns:
        Decorated function
    """
    def wrapper(*args, **kwargs):
        func_name = f"{func.__module__}.{func.__name__}"
        logger.debug(f"Calling function: {func_name}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"Function completed: {func_name}")
            return result
        except Exception as e:
            logger.error(f"Function failed: {func_name} - {e}")
            raise
    
    return wrapper


def log_async_function_call(func):
    """Decorator to log async function calls.
    
    Args:
        func: Async function to decorate
        
    Returns:
        Decorated async function
    """
    async def wrapper(*args, **kwargs):
        func_name = f"{func.__module__}.{func.__name__}"
        logger.debug(f"Calling async function: {func_name}")
        
        try:
            result = await func(*args, **kwargs)
            logger.debug(f"Async function completed: {func_name}")
            return result
        except Exception as e:
            logger.error(f"Async function failed: {func_name} - {e}")
            raise
    
    return wrapper
