/* Generated code for Python module 'rethinkdb$ql2_pb2'
 * created by Nuitka version 2.7.3
 *
 * This code is in part copyright 2025 Kay <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "nuitka/prelude.h"

#include "nuitka/unfreezing.h"

#include "__helpers.h"

/* The "module_rethinkdb$ql2_pb2" is a Python object pointer of module type.
 *
 * Note: For full compatibility with CPython, every module variable access
 * needs to go through it except for cases where the module cannot possibly
 * have changed in the mean time.
 */

PyObject *module_rethinkdb$ql2_pb2;
PyDictObject *moduledict_rethinkdb$ql2_pb2;

/* The declarations of module constants used, if any. */
static PyObject *mod_consts[472];
#ifndef __NUITKA_NO_ASSERT__
static Py_hash_t mod_consts_hash[472];
#endif

static PyObject *module_filename_obj = NULL;

/* Indicator if this modules private constants were created yet. */
static bool constants_created = false;

/* Function to create module private constants. */
static void createModuleConstants(PyThreadState *tstate) {
    if (constants_created == false) {
        loadConstantsBlob(tstate, &mod_consts[0], UN_TRANSLATE("rethinkdb.ql2_pb2"));
        constants_created = true;

#ifndef __NUITKA_NO_ASSERT__
        for (int i = 0; i < 472; i++) {
            mod_consts_hash[i] = DEEP_HASH(tstate, mod_consts[i]);
        }
#endif
    }
}

// We want to be able to initialize the "__main__" constants in any case.
#if 0
void createMainModuleConstants(PyThreadState *tstate) {
    createModuleConstants(tstate);
}
#endif

/* Function to verify module private constants for non-corruption. */
#ifndef __NUITKA_NO_ASSERT__
void checkModuleConstants_rethinkdb$ql2_pb2(PyThreadState *tstate) {
    // The module may not have been used at all, then ignore this.
    if (constants_created == false) return;

    for (int i = 0; i < 472; i++) {
        assert(mod_consts_hash[i] == DEEP_HASH(tstate, mod_consts[i]));
        CHECK_OBJECT_DEEP(mod_consts[i]);
    }
}
#endif

// Helper to preserving module variables for Python3.11+
#if 1
#if PYTHON_VERSION >= 0x3c0
NUITKA_MAY_BE_UNUSED static uint32_t _Nuitka_PyDictKeys_GetVersionForCurrentState(PyInterpreterState *interp, PyDictKeysObject *dk)
{
    if (dk->dk_version != 0) {
        return dk->dk_version;
    }
    uint32_t result = interp->dict_state.next_keys_version++;
    dk->dk_version = result;
    return result;
}
#elif PYTHON_VERSION >= 0x3b0
static uint32_t _Nuitka_next_dict_keys_version = 2;

NUITKA_MAY_BE_UNUSED static uint32_t _Nuitka_PyDictKeys_GetVersionForCurrentState(PyDictKeysObject *dk)
{
    if (dk->dk_version != 0) {
        return dk->dk_version;
    }
    uint32_t result = _Nuitka_next_dict_keys_version++;
    dk->dk_version = result;
    return result;
}
#endif
#endif

// Accessors to module variables.
static PyObject *module_var_accessor_rethinkdb$$36$ql2_pb2$__spec__(PyThreadState *tstate) {
#if 0
    PyObject *result;

#if PYTHON_VERSION < 0x3b0
    static uint64_t dict_version = 0;
    static PyObject *cache_value = NULL;

    if (moduledict_rethinkdb$ql2_pb2->ma_version_tag == dict_version) {
        CHECK_OBJECT_X(cache_value);
        result = cache_value;
    } else {
        dict_version = moduledict_rethinkdb$ql2_pb2->ma_version_tag;

        result = GET_STRING_DICT_VALUE(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)mod_consts[471]);
        cache_value = result;
    }
#else
    static uint32_t dict_keys_version = 0xFFFFFFFF;
    static Py_ssize_t cache_dk_index = 0;

    PyDictKeysObject *dk = moduledict_rethinkdb$ql2_pb2->ma_keys;
    if (likely(DK_IS_UNICODE(dk))) {

#if PYTHON_VERSION >= 0x3c0
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(tstate->interp, dk);
#else
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(dk);
#endif

        if (current_dk_version != dict_keys_version) {
            dict_keys_version = current_dk_version;
            Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[471]);
            assert(hash != -1);

            cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[471], hash);
        }

        if (cache_dk_index >= 0) {
            assert(dk->dk_kind != DICT_KEYS_SPLIT);

            PyDictUnicodeEntry *entries = DK_UNICODE_ENTRIES(dk);

            result = entries[cache_dk_index].me_value;

            if (unlikely(result == NULL)) {
                Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[471]);
                assert(hash != -1);

                cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[471], hash);

                if (cache_dk_index >= 0) {
                    result = entries[cache_dk_index].me_value;
                }
            }
        } else {
            result = NULL;
        }
    } else {
        result = GET_STRING_DICT_VALUE(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)mod_consts[471]);
    }
#endif

#else
    PyObject *result = GET_STRING_DICT_VALUE(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)mod_consts[471]);
#endif

    if (unlikely(result == NULL)) {
        result = GET_STRING_DICT_VALUE(dict_builtin, (Nuitka_StringObject *)mod_consts[471]);
    }

    return result;
}


#if !defined(_NUITKA_EXPERIMENTAL_NEW_CODE_OBJECTS)
// The module code objects.
static PyCodeObject *code_objects_a5d1b6ba996a78e7793edb548453bc07;
static PyCodeObject *code_objects_db9f95713d954ad2fb4562671f6edc73;
static PyCodeObject *code_objects_3c58b5c3d1afd5951d8b1583051bd145;
static PyCodeObject *code_objects_4b282383968fc134d63d1b24d9239626;
static PyCodeObject *code_objects_1270a1d483b75694b27debd6f1c56c87;
static PyCodeObject *code_objects_c8770b9feff63c62b48461a01db850f3;
static PyCodeObject *code_objects_24c9054ee7ca6589c14c045170e65e26;

static void createModuleCodeObjects(void) {
    module_filename_obj = MAKE_RELATIVE_PATH(mod_consts[467]); CHECK_OBJECT(module_filename_obj);
    code_objects_a5d1b6ba996a78e7793edb548453bc07 = MAKE_CODE_OBJECT(module_filename_obj, 1, CO_NOFREE, mod_consts[468], mod_consts[468], NULL, NULL, 0, 0, 0);
    code_objects_db9f95713d954ad2fb4562671f6edc73 = MAKE_CODE_OBJECT(module_filename_obj, 63, CO_NOFREE, mod_consts[85], mod_consts[85], mod_consts[469], NULL, 0, 0, 0);
    code_objects_3c58b5c3d1afd5951d8b1583051bd145 = MAKE_CODE_OBJECT(module_filename_obj, 27, CO_NOFREE, mod_consts[41], mod_consts[41], mod_consts[469], NULL, 0, 0, 0);
    code_objects_4b282383968fc134d63d1b24d9239626 = MAKE_CODE_OBJECT(module_filename_obj, 16, CO_NOFREE, mod_consts[27], mod_consts[27], mod_consts[469], NULL, 0, 0, 0);
    code_objects_1270a1d483b75694b27debd6f1c56c87 = MAKE_CODE_OBJECT(module_filename_obj, 35, CO_NOFREE, mod_consts[47], mod_consts[47], mod_consts[469], NULL, 0, 0, 0);
    code_objects_c8770b9feff63c62b48461a01db850f3 = MAKE_CODE_OBJECT(module_filename_obj, 76, CO_NOFREE, mod_consts[98], mod_consts[98], mod_consts[469], NULL, 0, 0, 0);
    code_objects_24c9054ee7ca6589c14c045170e65e26 = MAKE_CODE_OBJECT(module_filename_obj, 4, CO_NOFREE, mod_consts[7], mod_consts[7], mod_consts[469], NULL, 0, 0, 0);
}
#endif

// The module function declarations.


// The module function definitions.


extern void _initCompiledCellType();
extern void _initCompiledGeneratorType();
extern void _initCompiledFunctionType();
extern void _initCompiledMethodType();
extern void _initCompiledFrameType();

extern PyTypeObject Nuitka_Loader_Type;

#ifdef _NUITKA_PLUGIN_DILL_ENABLED
// Provide a way to create find a function via its C code and create it back
// in another process, useful for multiprocessing extensions like dill
extern void registerDillPluginTables(PyThreadState *tstate, char const *module_name, PyMethodDef *reduce_compiled_function, PyMethodDef *create_compiled_function);

static function_impl_code const function_table_rethinkdb$ql2_pb2[] = {

    NULL
};

static PyObject *_reduce_compiled_function(PyObject *self, PyObject *args, PyObject *kwds) {
    PyObject *func;

    if (!PyArg_ParseTuple(args, "O:reduce_compiled_function", &func, NULL)) {
        return NULL;
    }

    if (Nuitka_Function_Check(func) == false) {
        PyThreadState *tstate = PyThreadState_GET();

        SET_CURRENT_EXCEPTION_TYPE0_STR(tstate, PyExc_TypeError, "not a compiled function");
        return NULL;
    }

    struct Nuitka_FunctionObject *function = (struct Nuitka_FunctionObject *)func;

    return Nuitka_Function_GetFunctionState(function, function_table_rethinkdb$ql2_pb2);
}

static PyMethodDef _method_def_reduce_compiled_function = {"reduce_compiled_function", (PyCFunction)_reduce_compiled_function,
                                                           METH_VARARGS, NULL};


static PyObject *_create_compiled_function(PyObject *self, PyObject *args, PyObject *kwds) {
    CHECK_OBJECT_DEEP(args);

    PyObject *function_index;
    PyObject *code_object_desc;
    PyObject *defaults;
    PyObject *kw_defaults;
    PyObject *doc;
    PyObject *constant_return_value;
    PyObject *function_qualname;
    PyObject *closure;
    PyObject *annotations;
    PyObject *func_dict;

    if (!PyArg_ParseTuple(args, "OOOOOOOOOO:create_compiled_function", &function_index, &code_object_desc, &defaults, &kw_defaults, &doc, &constant_return_value, &function_qualname, &closure, &annotations, &func_dict, NULL)) {
        return NULL;
    }

    return (PyObject *)Nuitka_Function_CreateFunctionViaCodeIndex(
        module_rethinkdb$ql2_pb2,
        function_qualname,
        function_index,
        code_object_desc,
        constant_return_value,
        defaults,
        kw_defaults,
        doc,
        closure,
        annotations,
        func_dict,
        function_table_rethinkdb$ql2_pb2,
        sizeof(function_table_rethinkdb$ql2_pb2) / sizeof(function_impl_code)
    );
}

static PyMethodDef _method_def_create_compiled_function = {
    "create_compiled_function",
    (PyCFunction)_create_compiled_function,
    METH_VARARGS, NULL
};


#endif

// Actual name might be different when loaded as a package.
#if _NUITKA_MODULE_MODE && 0
static char const *module_full_name = "rethinkdb.ql2_pb2";
#endif

// Internal entry point for module code.
PyObject *modulecode_rethinkdb$ql2_pb2(PyThreadState *tstate, PyObject *module, struct Nuitka_MetaPathBasedLoaderEntry const *loader_entry) {
    // Report entry to PGO.
    PGO_onModuleEntered("rethinkdb$ql2_pb2");

    // Store the module for future use.
    module_rethinkdb$ql2_pb2 = module;

    moduledict_rethinkdb$ql2_pb2 = MODULE_DICT(module_rethinkdb$ql2_pb2);

    // Modules can be loaded again in case of errors, avoid the init being done again.
    static bool init_done = false;

    if (init_done == false) {
#if _NUITKA_MODULE_MODE && 0
        // In case of an extension module loaded into a process, we need to call
        // initialization here because that's the first and potentially only time
        // we are going called.
#if PYTHON_VERSION > 0x350 && !defined(_NUITKA_EXPERIMENTAL_DISABLE_ALLOCATORS)
        initNuitkaAllocators();
#endif
        // Initialize the constant values used.
        _initBuiltinModule();

        PyObject *real_module_name = PyObject_GetAttrString(module, "__name__");
        CHECK_OBJECT(real_module_name);
        module_full_name = strdup(Nuitka_String_AsString(real_module_name));

        createGlobalConstants(tstate, real_module_name);

        /* Initialize the compiled types of Nuitka. */
        _initCompiledCellType();
        _initCompiledGeneratorType();
        _initCompiledFunctionType();
        _initCompiledMethodType();
        _initCompiledFrameType();

        _initSlotCompare();
#if PYTHON_VERSION >= 0x270
        _initSlotIterNext();
#endif

        patchTypeComparison();

        // Enable meta path based loader if not already done.
#ifdef _NUITKA_TRACE
        PRINT_STRING("rethinkdb$ql2_pb2: Calling setupMetaPathBasedLoader().\n");
#endif
        setupMetaPathBasedLoader(tstate);
#if 0 >= 0
#ifdef _NUITKA_TRACE
        PRINT_STRING("rethinkdb$ql2_pb2: Calling updateMetaPathBasedLoaderModuleRoot().\n");
#endif
        updateMetaPathBasedLoaderModuleRoot(module_full_name);
#endif


#if PYTHON_VERSION >= 0x300
        patchInspectModule(tstate);
#endif

#endif

        /* The constants only used by this module are created now. */
        NUITKA_PRINT_TRACE("rethinkdb$ql2_pb2: Calling createModuleConstants().\n");
        createModuleConstants(tstate);

#if !defined(_NUITKA_EXPERIMENTAL_NEW_CODE_OBJECTS)
        createModuleCodeObjects();
#endif
        init_done = true;
    }

#if _NUITKA_MODULE_MODE && 0
    PyObject *pre_load = IMPORT_EMBEDDED_MODULE(tstate, "rethinkdb.ql2_pb2" "-preLoad");
    if (pre_load == NULL) {
        return NULL;
    }
#endif

    // PRINT_STRING("in initrethinkdb$ql2_pb2\n");

#ifdef _NUITKA_PLUGIN_DILL_ENABLED
    {
        char const *module_name_c;
        if (loader_entry != NULL) {
            module_name_c = loader_entry->name;
        } else {
            PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)const_str_plain___name__);
            module_name_c = Nuitka_String_AsString(module_name);
        }

        registerDillPluginTables(tstate, module_name_c, &_method_def_reduce_compiled_function, &_method_def_create_compiled_function);
    }
#endif

    // Set "__compiled__" to what version information we have.
    UPDATE_STRING_DICT0(
        moduledict_rethinkdb$ql2_pb2,
        (Nuitka_StringObject *)const_str_plain___compiled__,
        Nuitka_dunder_compiled_value
    );

    // Update "__package__" value to what it ought to be.
    {
#if 0
        UPDATE_STRING_DICT0(
            moduledict_rethinkdb$ql2_pb2,
            (Nuitka_StringObject *)const_str_plain___package__,
            mod_consts[470]
        );
#elif 0
        PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)const_str_plain___name__);

        UPDATE_STRING_DICT0(
            moduledict_rethinkdb$ql2_pb2,
            (Nuitka_StringObject *)const_str_plain___package__,
            module_name
        );
#else

#if PYTHON_VERSION < 0x300
        PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)const_str_plain___name__);
        char const *module_name_cstr = PyString_AS_STRING(module_name);

        char const *last_dot = strrchr(module_name_cstr, '.');

        if (last_dot != NULL) {
            UPDATE_STRING_DICT1(
                moduledict_rethinkdb$ql2_pb2,
                (Nuitka_StringObject *)const_str_plain___package__,
                PyString_FromStringAndSize(module_name_cstr, last_dot - module_name_cstr)
            );
        }
#else
        PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)const_str_plain___name__);
        Py_ssize_t dot_index = PyUnicode_Find(module_name, const_str_dot, 0, PyUnicode_GetLength(module_name), -1);

        if (dot_index != -1) {
            UPDATE_STRING_DICT1(
                moduledict_rethinkdb$ql2_pb2,
                (Nuitka_StringObject *)const_str_plain___package__,
                PyUnicode_Substring(module_name, 0, dot_index)
            );
        }
#endif
#endif
    }

    CHECK_OBJECT(module_rethinkdb$ql2_pb2);

    // For deep importing of a module we need to have "__builtins__", so we set
    // it ourselves in the same way than CPython does. Note: This must be done
    // before the frame object is allocated, or else it may fail.

    if (GET_STRING_DICT_VALUE(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)const_str_plain___builtins__) == NULL) {
        PyObject *value = (PyObject *)builtin_module;

        // Check if main module, not a dict then but the module itself.
#if _NUITKA_MODULE_MODE || !0
        value = PyModule_GetDict(value);
#endif

        UPDATE_STRING_DICT0(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)const_str_plain___builtins__, value);
    }

    PyObject *module_loader = Nuitka_Loader_New(loader_entry);
    UPDATE_STRING_DICT0(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)const_str_plain___loader__, module_loader);

#if PYTHON_VERSION >= 0x300
// Set the "__spec__" value

#if 0
    // Main modules just get "None" as spec.
    UPDATE_STRING_DICT0(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)const_str_plain___spec__, Py_None);
#else
    // Other modules get a "ModuleSpec" from the standard mechanism.
    {
        PyObject *bootstrap_module = getImportLibBootstrapModule();
        CHECK_OBJECT(bootstrap_module);

        PyObject *_spec_from_module = PyObject_GetAttrString(bootstrap_module, "_spec_from_module");
        CHECK_OBJECT(_spec_from_module);

        PyObject *spec_value = CALL_FUNCTION_WITH_SINGLE_ARG(tstate, _spec_from_module, module_rethinkdb$ql2_pb2);
        Py_DECREF(_spec_from_module);

        // We can assume this to never fail, or else we are in trouble anyway.
        // CHECK_OBJECT(spec_value);

        if (spec_value == NULL) {
            PyErr_PrintEx(0);
            abort();
        }

        // Mark the execution in the "__spec__" value.
        SET_ATTRIBUTE(tstate, spec_value, const_str_plain__initializing, Py_True);

#if _NUITKA_MODULE_MODE && 0 && 0 >= 0
        // Set our loader object in the "__spec__" value.
        SET_ATTRIBUTE(tstate, spec_value, const_str_plain_loader, module_loader);
#endif

        UPDATE_STRING_DICT1(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)const_str_plain___spec__, spec_value);
    }
#endif
#endif

    // Temp variables if any
    PyObject *outline_0_var___class__ = NULL;
    PyObject *outline_1_var___class__ = NULL;
    PyObject *outline_2_var___class__ = NULL;
    PyObject *outline_3_var___class__ = NULL;
    PyObject *outline_4_var___class__ = NULL;
    PyObject *outline_5_var___class__ = NULL;
    PyObject *outline_6_var___class__ = NULL;
    PyObject *outline_7_var___class__ = NULL;
    PyObject *outline_8_var___class__ = NULL;
    PyObject *outline_9_var___class__ = NULL;
    PyObject *outline_10_var___class__ = NULL;
    PyObject *outline_11_var___class__ = NULL;
    PyObject *outline_12_var___class__ = NULL;
    PyObject *outline_13_var___class__ = NULL;
    PyObject *outline_14_var___class__ = NULL;
    PyObject *outline_15_var___class__ = NULL;
    PyObject *outline_16_var___class__ = NULL;
    PyObject *outline_17_var___class__ = NULL;
    PyObject *outline_18_var___class__ = NULL;
    PyObject *tmp_Datum$$36$class_creation_1__class_decl_dict = NULL;
    PyObject *tmp_Datum$$36$class_creation_1__prepared = NULL;
    PyObject *tmp_Datum$$36$class_creation_2__class_decl_dict = NULL;
    PyObject *tmp_Datum$$36$class_creation_2__prepared = NULL;
    PyObject *tmp_Frame$$36$class_creation_1__class_decl_dict = NULL;
    PyObject *tmp_Frame$$36$class_creation_1__prepared = NULL;
    PyObject *tmp_Query$$36$class_creation_1__class_decl_dict = NULL;
    PyObject *tmp_Query$$36$class_creation_1__prepared = NULL;
    PyObject *tmp_Query$$36$class_creation_2__class_decl_dict = NULL;
    PyObject *tmp_Query$$36$class_creation_2__prepared = NULL;
    PyObject *tmp_Response$$36$class_creation_1__class_decl_dict = NULL;
    PyObject *tmp_Response$$36$class_creation_1__prepared = NULL;
    PyObject *tmp_Response$$36$class_creation_2__class_decl_dict = NULL;
    PyObject *tmp_Response$$36$class_creation_2__prepared = NULL;
    PyObject *tmp_Response$$36$class_creation_3__class_decl_dict = NULL;
    PyObject *tmp_Response$$36$class_creation_3__prepared = NULL;
    PyObject *tmp_Term$$36$class_creation_1__class_decl_dict = NULL;
    PyObject *tmp_Term$$36$class_creation_1__prepared = NULL;
    PyObject *tmp_Term$$36$class_creation_2__class_decl_dict = NULL;
    PyObject *tmp_Term$$36$class_creation_2__prepared = NULL;
    PyObject *tmp_VersionDummy$$36$class_creation_1__class_decl_dict = NULL;
    PyObject *tmp_VersionDummy$$36$class_creation_1__prepared = NULL;
    PyObject *tmp_VersionDummy$$36$class_creation_2__class_decl_dict = NULL;
    PyObject *tmp_VersionDummy$$36$class_creation_2__prepared = NULL;
    PyObject *tmp_class_creation_1__class_decl_dict = NULL;
    PyObject *tmp_class_creation_1__prepared = NULL;
    PyObject *tmp_class_creation_2__class_decl_dict = NULL;
    PyObject *tmp_class_creation_2__prepared = NULL;
    PyObject *tmp_class_creation_3__class_decl_dict = NULL;
    PyObject *tmp_class_creation_3__prepared = NULL;
    PyObject *tmp_class_creation_4__class_decl_dict = NULL;
    PyObject *tmp_class_creation_4__prepared = NULL;
    PyObject *tmp_class_creation_5__class_decl_dict = NULL;
    PyObject *tmp_class_creation_5__prepared = NULL;
    PyObject *tmp_class_creation_6__class_decl_dict = NULL;
    PyObject *tmp_class_creation_6__prepared = NULL;
    PyObject *tmp_class_creation_7__class_decl_dict = NULL;
    PyObject *tmp_class_creation_7__prepared = NULL;
    struct Nuitka_FrameObject *frame_frame_rethinkdb$ql2_pb2;
    NUITKA_MAY_BE_UNUSED char const *type_description_1 = NULL;
    bool tmp_result;
    struct Nuitka_ExceptionPreservationItem exception_state = Empty_Nuitka_ExceptionPreservationItem;
    NUITKA_MAY_BE_UNUSED int exception_lineno = 0;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__1_VersionDummy_4 = NULL;
    PyObject *tmp_dictset_value;
    struct Nuitka_FrameObject *frame_frame_rethinkdb$ql2_pb2$$$class__1_VersionDummy_2;
    NUITKA_MAY_BE_UNUSED char const *type_description_2 = NULL;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__2_Version_5 = NULL;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_1;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_1;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_2;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_2;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_3;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_3;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__3_Protocol_12 = NULL;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_4;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_4;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_5;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_5;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_6;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_6;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_7;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_7;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_8;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_8;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_9;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_9;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__4_Query_16 = NULL;
    struct Nuitka_FrameObject *frame_frame_rethinkdb$ql2_pb2$$$class__4_Query_3;
    NUITKA_MAY_BE_UNUSED char const *type_description_3 = NULL;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__5_QueryType_17 = NULL;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_10;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_10;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_11;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_11;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_12;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_12;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__6_AssocPair_24 = NULL;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_13;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_13;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_14;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_14;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_15;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_15;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_16;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_16;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_17;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_17;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_18;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_18;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__7_Frame_27 = NULL;
    struct Nuitka_FrameObject *frame_frame_rethinkdb$ql2_pb2$$$class__7_Frame_4;
    NUITKA_MAY_BE_UNUSED char const *type_description_4 = NULL;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__8_FrameType_28 = NULL;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_19;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_19;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_20;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_20;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_21;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_21;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_22;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_22;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_23;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_23;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_24;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_24;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__9_Backtrace_32 = NULL;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_25;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_25;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_26;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_26;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_27;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_27;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__10_Response_35 = NULL;
    struct Nuitka_FrameObject *frame_frame_rethinkdb$ql2_pb2$$$class__10_Response_5;
    NUITKA_MAY_BE_UNUSED char const *type_description_5 = NULL;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__11_ResponseType_36 = NULL;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_28;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_28;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_29;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_29;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_30;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_30;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__12_ErrorType_46 = NULL;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_31;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_31;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_32;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_32;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_33;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_33;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__13_ResponseNote_56 = NULL;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_34;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_34;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_35;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_35;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_36;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_36;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_37;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_37;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_38;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_38;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_39;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_39;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__14_Datum_63 = NULL;
    struct Nuitka_FrameObject *frame_frame_rethinkdb$ql2_pb2$$$class__14_Datum_6;
    NUITKA_MAY_BE_UNUSED char const *type_description_6 = NULL;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__15_DatumType_64 = NULL;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_40;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_40;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_41;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_41;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_42;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_42;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__16_AssocPair_73 = NULL;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_43;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_43;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_44;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_44;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_45;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_45;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_46;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_46;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_47;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_47;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_48;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_48;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__17_Term_76 = NULL;
    struct Nuitka_FrameObject *frame_frame_rethinkdb$ql2_pb2$$$class__17_Term_7;
    NUITKA_MAY_BE_UNUSED char const *type_description_7 = NULL;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__18_TermType_77 = NULL;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_49;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_49;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_50;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_50;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_51;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_51;
    PyObject *locals_rethinkdb$ql2_pb2$$$class__19_AssocPair_265 = NULL;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_52;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_52;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_53;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_53;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_54;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_54;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_55;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_55;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_56;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_56;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_57;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_57;

    // Module init code if any


    // Module code.
    {
        PyObject *tmp_assign_source_1;
        tmp_assign_source_1 = Py_None;
        UPDATE_STRING_DICT0(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)mod_consts[0], tmp_assign_source_1);
    }
    {
        PyObject *tmp_assign_source_2;
        tmp_assign_source_2 = module_filename_obj;
        UPDATE_STRING_DICT0(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)mod_consts[1], tmp_assign_source_2);
    }
    frame_frame_rethinkdb$ql2_pb2 = MAKE_MODULE_FRAME(code_objects_a5d1b6ba996a78e7793edb548453bc07, module_rethinkdb$ql2_pb2);

    // Push the new frame as the currently active one, and we should be exclusively
    // owning it.
    pushFrameStackCompiledFrame(tstate, frame_frame_rethinkdb$ql2_pb2);
    assert(Py_REFCNT(frame_frame_rethinkdb$ql2_pb2) == 2);

    // Framed code:
    {
        PyObject *tmp_assattr_value_1;
        PyObject *tmp_assattr_target_1;
        tmp_assattr_value_1 = module_filename_obj;
        tmp_assattr_target_1 = module_var_accessor_rethinkdb$$36$ql2_pb2$__spec__(tstate);
        assert(!(tmp_assattr_target_1 == NULL));
        tmp_result = SET_ATTRIBUTE(tstate, tmp_assattr_target_1, mod_consts[2], tmp_assattr_value_1);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 1;

            goto frame_exception_exit_1;
        }
    }
    {
        PyObject *tmp_assattr_value_2;
        PyObject *tmp_assattr_target_2;
        tmp_assattr_value_2 = Py_True;
        tmp_assattr_target_2 = module_var_accessor_rethinkdb$$36$ql2_pb2$__spec__(tstate);
        assert(!(tmp_assattr_target_2 == NULL));
        tmp_result = SET_ATTRIBUTE(tstate, tmp_assattr_target_2, mod_consts[3], tmp_assattr_value_2);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 1;

            goto frame_exception_exit_1;
        }
    }
    {
        PyObject *tmp_assign_source_3;
        tmp_assign_source_3 = Py_None;
        UPDATE_STRING_DICT0(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)mod_consts[4], tmp_assign_source_3);
    }
    {
        PyObject *tmp_assign_source_4;
        tmp_assign_source_4 = MAKE_DICT_EMPTY(tstate);
        assert(tmp_class_creation_1__class_decl_dict == NULL);
        tmp_class_creation_1__class_decl_dict = tmp_assign_source_4;
    }
    {
        PyObject *tmp_assign_source_5;
        tmp_assign_source_5 = MAKE_DICT_EMPTY(tstate);
        assert(tmp_class_creation_1__prepared == NULL);
        tmp_class_creation_1__prepared = tmp_assign_source_5;
    }
    // Tried code:
    {
        PyObject *tmp_assign_source_6;
        {
            PyObject *tmp_set_locals_1;
            CHECK_OBJECT(tmp_class_creation_1__prepared);
            tmp_set_locals_1 = tmp_class_creation_1__prepared;
            locals_rethinkdb$ql2_pb2$$$class__1_VersionDummy_4 = tmp_set_locals_1;
            Py_INCREF(tmp_set_locals_1);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__1_VersionDummy_4, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[7];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__1_VersionDummy_4, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        {
            PyObject *tmp_assign_source_7;
            tmp_assign_source_7 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_VersionDummy$$36$class_creation_1__class_decl_dict == NULL);
            tmp_VersionDummy$$36$class_creation_1__class_decl_dict = tmp_assign_source_7;
        }
        {
            PyObject *tmp_assign_source_8;
            tmp_assign_source_8 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_VersionDummy$$36$class_creation_1__prepared == NULL);
            tmp_VersionDummy$$36$class_creation_1__prepared = tmp_assign_source_8;
        }
        // Tried code:
        // Tried code:
        frame_frame_rethinkdb$ql2_pb2$$$class__1_VersionDummy_2 = MAKE_CLASS_FRAME(tstate, code_objects_24c9054ee7ca6589c14c045170e65e26, module_rethinkdb$ql2_pb2, NULL, sizeof(void *));

        // Push the new frame as the currently active one, and we should be exclusively
        // owning it.
        pushFrameStackCompiledFrame(tstate, frame_frame_rethinkdb$ql2_pb2$$$class__1_VersionDummy_2);
        assert(Py_REFCNT(frame_frame_rethinkdb$ql2_pb2$$$class__1_VersionDummy_2) == 2);

        // Framed code:
        // Tried code:
        {
            PyObject *tmp_set_locals_2;
            CHECK_OBJECT(tmp_VersionDummy$$36$class_creation_1__prepared);
            tmp_set_locals_2 = tmp_VersionDummy$$36$class_creation_1__prepared;
            locals_rethinkdb$ql2_pb2$$$class__2_Version_5 = tmp_set_locals_2;
            Py_INCREF(tmp_set_locals_2);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__2_Version_5, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[9];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__2_Version_5, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[10];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__2_Version_5, mod_consts[11], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[12];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__2_Version_5, mod_consts[13], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[14];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__2_Version_5, mod_consts[15], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[16];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__2_Version_5, mod_consts[17], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[18];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__2_Version_5, mod_consts[19], tmp_dictset_value);
        assert(!(tmp_result == false));
        // Tried code:
        // Tried code:
        {
            PyObject *tmp_assign_source_9;
            PyObject *tmp_called_value_1;
            PyObject *tmp_args_value_1;
            PyObject *tmp_tuple_element_1;
            PyObject *tmp_kwargs_value_1;
            tmp_called_value_1 = (PyObject *)&PyType_Type;
            tmp_tuple_element_1 = mod_consts[20];
            tmp_args_value_1 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_1, 0, tmp_tuple_element_1);
            tmp_tuple_element_1 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_1, 1, tmp_tuple_element_1);
            tmp_tuple_element_1 = locals_rethinkdb$ql2_pb2$$$class__2_Version_5;
            PyTuple_SET_ITEM0(tmp_args_value_1, 2, tmp_tuple_element_1);
            CHECK_OBJECT(tmp_VersionDummy$$36$class_creation_1__class_decl_dict);
            tmp_kwargs_value_1 = tmp_VersionDummy$$36$class_creation_1__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2$$$class__1_VersionDummy_2->m_frame.f_lineno = 5;
            tmp_assign_source_9 = CALL_FUNCTION(tstate, tmp_called_value_1, tmp_args_value_1, tmp_kwargs_value_1);
            Py_DECREF(tmp_args_value_1);
            if (tmp_assign_source_9 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 5;
                type_description_2 = "o";
                goto try_except_handler_6;
            }
            assert(outline_1_var___class__ == NULL);
            outline_1_var___class__ = tmp_assign_source_9;
        }
        CHECK_OBJECT(outline_1_var___class__);
        tmp_dictset_value = outline_1_var___class__;
        Py_INCREF(tmp_dictset_value);
        goto try_return_handler_6;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_6:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__2_Version_5);
        locals_rethinkdb$ql2_pb2$$$class__2_Version_5 = NULL;
        goto try_return_handler_5;
        // Exception handler code:
        try_except_handler_6:;
        exception_keeper_lineno_1 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_1 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__2_Version_5);
        locals_rethinkdb$ql2_pb2$$$class__2_Version_5 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_1;
        exception_lineno = exception_keeper_lineno_1;

        goto try_except_handler_5;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_5:;
        CHECK_OBJECT(outline_1_var___class__);
        Py_DECREF(outline_1_var___class__);
        outline_1_var___class__ = NULL;
        goto outline_result_2;
        // Exception handler code:
        try_except_handler_5:;
        exception_keeper_lineno_2 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_2 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_2;
        exception_lineno = exception_keeper_lineno_2;

        goto outline_exception_2;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_2:;
        exception_lineno = 5;
        goto try_except_handler_4;
        outline_result_2:;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__1_VersionDummy_4, mod_consts[20], tmp_dictset_value);
        Py_DECREF(tmp_dictset_value);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 5;
            type_description_2 = "o";
            goto try_except_handler_4;
        }
        goto try_end_1;
        // Exception handler code:
        try_except_handler_4:;
        exception_keeper_lineno_3 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_3 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        CHECK_OBJECT(tmp_VersionDummy$$36$class_creation_1__class_decl_dict);
        Py_DECREF(tmp_VersionDummy$$36$class_creation_1__class_decl_dict);
        tmp_VersionDummy$$36$class_creation_1__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_VersionDummy$$36$class_creation_1__prepared);
        Py_DECREF(tmp_VersionDummy$$36$class_creation_1__prepared);
        tmp_VersionDummy$$36$class_creation_1__prepared = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_3;
        exception_lineno = exception_keeper_lineno_3;

        goto frame_exception_exit_2;
        // End of try:
        try_end_1:;
        CHECK_OBJECT(tmp_VersionDummy$$36$class_creation_1__class_decl_dict);
        Py_DECREF(tmp_VersionDummy$$36$class_creation_1__class_decl_dict);
        tmp_VersionDummy$$36$class_creation_1__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_VersionDummy$$36$class_creation_1__prepared);
        Py_DECREF(tmp_VersionDummy$$36$class_creation_1__prepared);
        tmp_VersionDummy$$36$class_creation_1__prepared = NULL;
        {
            PyObject *tmp_assign_source_10;
            tmp_assign_source_10 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_VersionDummy$$36$class_creation_2__class_decl_dict == NULL);
            tmp_VersionDummy$$36$class_creation_2__class_decl_dict = tmp_assign_source_10;
        }
        {
            PyObject *tmp_assign_source_11;
            tmp_assign_source_11 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_VersionDummy$$36$class_creation_2__prepared == NULL);
            tmp_VersionDummy$$36$class_creation_2__prepared = tmp_assign_source_11;
        }
        // Tried code:
        {
            PyObject *tmp_set_locals_3;
            CHECK_OBJECT(tmp_VersionDummy$$36$class_creation_2__prepared);
            tmp_set_locals_3 = tmp_VersionDummy$$36$class_creation_2__prepared;
            locals_rethinkdb$ql2_pb2$$$class__3_Protocol_12 = tmp_set_locals_3;
            Py_INCREF(tmp_set_locals_3);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__3_Protocol_12, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[21];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__3_Protocol_12, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[22];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__3_Protocol_12, mod_consts[23], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[24];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__3_Protocol_12, mod_consts[25], tmp_dictset_value);
        assert(!(tmp_result == false));
        // Tried code:
        // Tried code:
        {
            PyObject *tmp_assign_source_12;
            PyObject *tmp_called_value_2;
            PyObject *tmp_args_value_2;
            PyObject *tmp_tuple_element_2;
            PyObject *tmp_kwargs_value_2;
            tmp_called_value_2 = (PyObject *)&PyType_Type;
            tmp_tuple_element_2 = mod_consts[26];
            tmp_args_value_2 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_2, 0, tmp_tuple_element_2);
            tmp_tuple_element_2 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_2, 1, tmp_tuple_element_2);
            tmp_tuple_element_2 = locals_rethinkdb$ql2_pb2$$$class__3_Protocol_12;
            PyTuple_SET_ITEM0(tmp_args_value_2, 2, tmp_tuple_element_2);
            CHECK_OBJECT(tmp_VersionDummy$$36$class_creation_2__class_decl_dict);
            tmp_kwargs_value_2 = tmp_VersionDummy$$36$class_creation_2__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2$$$class__1_VersionDummy_2->m_frame.f_lineno = 12;
            tmp_assign_source_12 = CALL_FUNCTION(tstate, tmp_called_value_2, tmp_args_value_2, tmp_kwargs_value_2);
            Py_DECREF(tmp_args_value_2);
            if (tmp_assign_source_12 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 12;
                type_description_2 = "o";
                goto try_except_handler_9;
            }
            assert(outline_2_var___class__ == NULL);
            outline_2_var___class__ = tmp_assign_source_12;
        }
        CHECK_OBJECT(outline_2_var___class__);
        tmp_dictset_value = outline_2_var___class__;
        Py_INCREF(tmp_dictset_value);
        goto try_return_handler_9;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_9:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__3_Protocol_12);
        locals_rethinkdb$ql2_pb2$$$class__3_Protocol_12 = NULL;
        goto try_return_handler_8;
        // Exception handler code:
        try_except_handler_9:;
        exception_keeper_lineno_4 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_4 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__3_Protocol_12);
        locals_rethinkdb$ql2_pb2$$$class__3_Protocol_12 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_4;
        exception_lineno = exception_keeper_lineno_4;

        goto try_except_handler_8;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_8:;
        CHECK_OBJECT(outline_2_var___class__);
        Py_DECREF(outline_2_var___class__);
        outline_2_var___class__ = NULL;
        goto outline_result_3;
        // Exception handler code:
        try_except_handler_8:;
        exception_keeper_lineno_5 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_5 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_5;
        exception_lineno = exception_keeper_lineno_5;

        goto outline_exception_3;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_3:;
        exception_lineno = 12;
        goto try_except_handler_7;
        outline_result_3:;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__1_VersionDummy_4, mod_consts[26], tmp_dictset_value);
        Py_DECREF(tmp_dictset_value);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 12;
            type_description_2 = "o";
            goto try_except_handler_7;
        }
        goto try_end_2;
        // Exception handler code:
        try_except_handler_7:;
        exception_keeper_lineno_6 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_6 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        CHECK_OBJECT(tmp_VersionDummy$$36$class_creation_2__class_decl_dict);
        Py_DECREF(tmp_VersionDummy$$36$class_creation_2__class_decl_dict);
        tmp_VersionDummy$$36$class_creation_2__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_VersionDummy$$36$class_creation_2__prepared);
        Py_DECREF(tmp_VersionDummy$$36$class_creation_2__prepared);
        tmp_VersionDummy$$36$class_creation_2__prepared = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_6;
        exception_lineno = exception_keeper_lineno_6;

        goto frame_exception_exit_2;
        // End of try:
        try_end_2:;


        // Put the previous frame back on top.
        popFrameStack(tstate);

        goto frame_no_exception_1;
        frame_exception_exit_2:


        {
            PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
            if (exception_tb == NULL) {
                exception_tb = MAKE_TRACEBACK(frame_frame_rethinkdb$ql2_pb2$$$class__1_VersionDummy_2, exception_lineno);
                SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
            } else if (exception_tb->tb_frame != &frame_frame_rethinkdb$ql2_pb2$$$class__1_VersionDummy_2->m_frame) {
                exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_rethinkdb$ql2_pb2$$$class__1_VersionDummy_2, exception_lineno);
                SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
            }
        }

        // Attaches locals to frame if any.
        Nuitka_Frame_AttachLocals(
            frame_frame_rethinkdb$ql2_pb2$$$class__1_VersionDummy_2,
            type_description_2,
            outline_0_var___class__
        );



        assertFrameObject(frame_frame_rethinkdb$ql2_pb2$$$class__1_VersionDummy_2);

        // Put the previous frame back on top.
        popFrameStack(tstate);

        // Return the error.
        goto nested_frame_exit_1;
        frame_no_exception_1:;
        goto skip_nested_handling_1;
        nested_frame_exit_1:;

        goto try_except_handler_3;
        skip_nested_handling_1:;
        CHECK_OBJECT(tmp_VersionDummy$$36$class_creation_2__class_decl_dict);
        Py_DECREF(tmp_VersionDummy$$36$class_creation_2__class_decl_dict);
        tmp_VersionDummy$$36$class_creation_2__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_VersionDummy$$36$class_creation_2__prepared);
        Py_DECREF(tmp_VersionDummy$$36$class_creation_2__prepared);
        tmp_VersionDummy$$36$class_creation_2__prepared = NULL;
        {
            PyObject *tmp_assign_source_13;
            PyObject *tmp_called_value_3;
            PyObject *tmp_args_value_3;
            PyObject *tmp_tuple_element_3;
            PyObject *tmp_kwargs_value_3;
            tmp_called_value_3 = (PyObject *)&PyType_Type;
            tmp_tuple_element_3 = mod_consts[7];
            tmp_args_value_3 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_3, 0, tmp_tuple_element_3);
            tmp_tuple_element_3 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_3, 1, tmp_tuple_element_3);
            tmp_tuple_element_3 = locals_rethinkdb$ql2_pb2$$$class__1_VersionDummy_4;
            PyTuple_SET_ITEM0(tmp_args_value_3, 2, tmp_tuple_element_3);
            CHECK_OBJECT(tmp_class_creation_1__class_decl_dict);
            tmp_kwargs_value_3 = tmp_class_creation_1__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2->m_frame.f_lineno = 4;
            tmp_assign_source_13 = CALL_FUNCTION(tstate, tmp_called_value_3, tmp_args_value_3, tmp_kwargs_value_3);
            Py_DECREF(tmp_args_value_3);
            if (tmp_assign_source_13 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 4;

                goto try_except_handler_3;
            }
            assert(outline_0_var___class__ == NULL);
            outline_0_var___class__ = tmp_assign_source_13;
        }
        CHECK_OBJECT(outline_0_var___class__);
        tmp_assign_source_6 = outline_0_var___class__;
        Py_INCREF(tmp_assign_source_6);
        goto try_return_handler_3;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_3:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__1_VersionDummy_4);
        locals_rethinkdb$ql2_pb2$$$class__1_VersionDummy_4 = NULL;
        goto try_return_handler_2;
        // Exception handler code:
        try_except_handler_3:;
        exception_keeper_lineno_7 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_7 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__1_VersionDummy_4);
        locals_rethinkdb$ql2_pb2$$$class__1_VersionDummy_4 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_7;
        exception_lineno = exception_keeper_lineno_7;

        goto try_except_handler_2;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_2:;
        CHECK_OBJECT(outline_0_var___class__);
        Py_DECREF(outline_0_var___class__);
        outline_0_var___class__ = NULL;
        goto outline_result_1;
        // Exception handler code:
        try_except_handler_2:;
        exception_keeper_lineno_8 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_8 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_8;
        exception_lineno = exception_keeper_lineno_8;

        goto outline_exception_1;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_1:;
        exception_lineno = 4;
        goto try_except_handler_1;
        outline_result_1:;
        UPDATE_STRING_DICT1(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)mod_consts[7], tmp_assign_source_6);
    }
    goto try_end_3;
    // Exception handler code:
    try_except_handler_1:;
    exception_keeper_lineno_9 = exception_lineno;
    exception_lineno = 0;
    exception_keeper_name_9 = exception_state;
    INIT_ERROR_OCCURRED_STATE(&exception_state);

    CHECK_OBJECT(tmp_class_creation_1__class_decl_dict);
    Py_DECREF(tmp_class_creation_1__class_decl_dict);
    tmp_class_creation_1__class_decl_dict = NULL;
    CHECK_OBJECT(tmp_class_creation_1__prepared);
    Py_DECREF(tmp_class_creation_1__prepared);
    tmp_class_creation_1__prepared = NULL;
    // Re-raise.
    exception_state = exception_keeper_name_9;
    exception_lineno = exception_keeper_lineno_9;

    goto frame_exception_exit_1;
    // End of try:
    try_end_3:;
    CHECK_OBJECT(tmp_class_creation_1__class_decl_dict);
    Py_DECREF(tmp_class_creation_1__class_decl_dict);
    tmp_class_creation_1__class_decl_dict = NULL;
    CHECK_OBJECT(tmp_class_creation_1__prepared);
    Py_DECREF(tmp_class_creation_1__prepared);
    tmp_class_creation_1__prepared = NULL;
    {
        PyObject *tmp_assign_source_14;
        tmp_assign_source_14 = MAKE_DICT_EMPTY(tstate);
        assert(tmp_class_creation_2__class_decl_dict == NULL);
        tmp_class_creation_2__class_decl_dict = tmp_assign_source_14;
    }
    {
        PyObject *tmp_assign_source_15;
        tmp_assign_source_15 = MAKE_DICT_EMPTY(tstate);
        assert(tmp_class_creation_2__prepared == NULL);
        tmp_class_creation_2__prepared = tmp_assign_source_15;
    }
    // Tried code:
    {
        PyObject *tmp_assign_source_16;
        {
            PyObject *tmp_set_locals_4;
            CHECK_OBJECT(tmp_class_creation_2__prepared);
            tmp_set_locals_4 = tmp_class_creation_2__prepared;
            locals_rethinkdb$ql2_pb2$$$class__4_Query_16 = tmp_set_locals_4;
            Py_INCREF(tmp_set_locals_4);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__4_Query_16, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[27];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__4_Query_16, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        {
            PyObject *tmp_assign_source_17;
            tmp_assign_source_17 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Query$$36$class_creation_1__class_decl_dict == NULL);
            tmp_Query$$36$class_creation_1__class_decl_dict = tmp_assign_source_17;
        }
        {
            PyObject *tmp_assign_source_18;
            tmp_assign_source_18 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Query$$36$class_creation_1__prepared == NULL);
            tmp_Query$$36$class_creation_1__prepared = tmp_assign_source_18;
        }
        // Tried code:
        // Tried code:
        frame_frame_rethinkdb$ql2_pb2$$$class__4_Query_3 = MAKE_CLASS_FRAME(tstate, code_objects_4b282383968fc134d63d1b24d9239626, module_rethinkdb$ql2_pb2, NULL, sizeof(void *));

        // Push the new frame as the currently active one, and we should be exclusively
        // owning it.
        pushFrameStackCompiledFrame(tstate, frame_frame_rethinkdb$ql2_pb2$$$class__4_Query_3);
        assert(Py_REFCNT(frame_frame_rethinkdb$ql2_pb2$$$class__4_Query_3) == 2);

        // Framed code:
        // Tried code:
        {
            PyObject *tmp_set_locals_5;
            CHECK_OBJECT(tmp_Query$$36$class_creation_1__prepared);
            tmp_set_locals_5 = tmp_Query$$36$class_creation_1__prepared;
            locals_rethinkdb$ql2_pb2$$$class__5_QueryType_17 = tmp_set_locals_5;
            Py_INCREF(tmp_set_locals_5);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__5_QueryType_17, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[28];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__5_QueryType_17, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = const_int_pos_1;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__5_QueryType_17, mod_consts[29], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[30];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__5_QueryType_17, mod_consts[31], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[32];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__5_QueryType_17, mod_consts[33], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[34];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__5_QueryType_17, mod_consts[35], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[36];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__5_QueryType_17, mod_consts[37], tmp_dictset_value);
        assert(!(tmp_result == false));
        // Tried code:
        // Tried code:
        {
            PyObject *tmp_assign_source_19;
            PyObject *tmp_called_value_4;
            PyObject *tmp_args_value_4;
            PyObject *tmp_tuple_element_4;
            PyObject *tmp_kwargs_value_4;
            tmp_called_value_4 = (PyObject *)&PyType_Type;
            tmp_tuple_element_4 = mod_consts[38];
            tmp_args_value_4 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_4, 0, tmp_tuple_element_4);
            tmp_tuple_element_4 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_4, 1, tmp_tuple_element_4);
            tmp_tuple_element_4 = locals_rethinkdb$ql2_pb2$$$class__5_QueryType_17;
            PyTuple_SET_ITEM0(tmp_args_value_4, 2, tmp_tuple_element_4);
            CHECK_OBJECT(tmp_Query$$36$class_creation_1__class_decl_dict);
            tmp_kwargs_value_4 = tmp_Query$$36$class_creation_1__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2$$$class__4_Query_3->m_frame.f_lineno = 17;
            tmp_assign_source_19 = CALL_FUNCTION(tstate, tmp_called_value_4, tmp_args_value_4, tmp_kwargs_value_4);
            Py_DECREF(tmp_args_value_4);
            if (tmp_assign_source_19 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 17;
                type_description_2 = "o";
                goto try_except_handler_15;
            }
            assert(outline_4_var___class__ == NULL);
            outline_4_var___class__ = tmp_assign_source_19;
        }
        CHECK_OBJECT(outline_4_var___class__);
        tmp_dictset_value = outline_4_var___class__;
        Py_INCREF(tmp_dictset_value);
        goto try_return_handler_15;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_15:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__5_QueryType_17);
        locals_rethinkdb$ql2_pb2$$$class__5_QueryType_17 = NULL;
        goto try_return_handler_14;
        // Exception handler code:
        try_except_handler_15:;
        exception_keeper_lineno_10 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_10 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__5_QueryType_17);
        locals_rethinkdb$ql2_pb2$$$class__5_QueryType_17 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_10;
        exception_lineno = exception_keeper_lineno_10;

        goto try_except_handler_14;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_14:;
        CHECK_OBJECT(outline_4_var___class__);
        Py_DECREF(outline_4_var___class__);
        outline_4_var___class__ = NULL;
        goto outline_result_5;
        // Exception handler code:
        try_except_handler_14:;
        exception_keeper_lineno_11 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_11 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_11;
        exception_lineno = exception_keeper_lineno_11;

        goto outline_exception_5;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_5:;
        exception_lineno = 17;
        goto try_except_handler_13;
        outline_result_5:;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__4_Query_16, mod_consts[38], tmp_dictset_value);
        Py_DECREF(tmp_dictset_value);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 17;
            type_description_2 = "o";
            goto try_except_handler_13;
        }
        goto try_end_4;
        // Exception handler code:
        try_except_handler_13:;
        exception_keeper_lineno_12 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_12 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        CHECK_OBJECT(tmp_Query$$36$class_creation_1__class_decl_dict);
        Py_DECREF(tmp_Query$$36$class_creation_1__class_decl_dict);
        tmp_Query$$36$class_creation_1__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Query$$36$class_creation_1__prepared);
        Py_DECREF(tmp_Query$$36$class_creation_1__prepared);
        tmp_Query$$36$class_creation_1__prepared = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_12;
        exception_lineno = exception_keeper_lineno_12;

        goto frame_exception_exit_3;
        // End of try:
        try_end_4:;
        CHECK_OBJECT(tmp_Query$$36$class_creation_1__class_decl_dict);
        Py_DECREF(tmp_Query$$36$class_creation_1__class_decl_dict);
        tmp_Query$$36$class_creation_1__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Query$$36$class_creation_1__prepared);
        Py_DECREF(tmp_Query$$36$class_creation_1__prepared);
        tmp_Query$$36$class_creation_1__prepared = NULL;
        {
            PyObject *tmp_assign_source_20;
            tmp_assign_source_20 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Query$$36$class_creation_2__class_decl_dict == NULL);
            tmp_Query$$36$class_creation_2__class_decl_dict = tmp_assign_source_20;
        }
        {
            PyObject *tmp_assign_source_21;
            tmp_assign_source_21 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Query$$36$class_creation_2__prepared == NULL);
            tmp_Query$$36$class_creation_2__prepared = tmp_assign_source_21;
        }
        // Tried code:
        {
            PyObject *tmp_set_locals_6;
            CHECK_OBJECT(tmp_Query$$36$class_creation_2__prepared);
            tmp_set_locals_6 = tmp_Query$$36$class_creation_2__prepared;
            locals_rethinkdb$ql2_pb2$$$class__6_AssocPair_24 = tmp_set_locals_6;
            Py_INCREF(tmp_set_locals_6);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__6_AssocPair_24, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[39];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__6_AssocPair_24, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        // Tried code:
        // Tried code:
        {
            PyObject *tmp_assign_source_22;
            PyObject *tmp_called_value_5;
            PyObject *tmp_args_value_5;
            PyObject *tmp_tuple_element_5;
            PyObject *tmp_kwargs_value_5;
            tmp_called_value_5 = (PyObject *)&PyType_Type;
            tmp_tuple_element_5 = mod_consts[40];
            tmp_args_value_5 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_5, 0, tmp_tuple_element_5);
            tmp_tuple_element_5 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_5, 1, tmp_tuple_element_5);
            tmp_tuple_element_5 = locals_rethinkdb$ql2_pb2$$$class__6_AssocPair_24;
            PyTuple_SET_ITEM0(tmp_args_value_5, 2, tmp_tuple_element_5);
            CHECK_OBJECT(tmp_Query$$36$class_creation_2__class_decl_dict);
            tmp_kwargs_value_5 = tmp_Query$$36$class_creation_2__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2$$$class__4_Query_3->m_frame.f_lineno = 24;
            tmp_assign_source_22 = CALL_FUNCTION(tstate, tmp_called_value_5, tmp_args_value_5, tmp_kwargs_value_5);
            Py_DECREF(tmp_args_value_5);
            if (tmp_assign_source_22 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 24;
                type_description_2 = "o";
                goto try_except_handler_18;
            }
            assert(outline_5_var___class__ == NULL);
            outline_5_var___class__ = tmp_assign_source_22;
        }
        CHECK_OBJECT(outline_5_var___class__);
        tmp_dictset_value = outline_5_var___class__;
        Py_INCREF(tmp_dictset_value);
        goto try_return_handler_18;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_18:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__6_AssocPair_24);
        locals_rethinkdb$ql2_pb2$$$class__6_AssocPair_24 = NULL;
        goto try_return_handler_17;
        // Exception handler code:
        try_except_handler_18:;
        exception_keeper_lineno_13 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_13 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__6_AssocPair_24);
        locals_rethinkdb$ql2_pb2$$$class__6_AssocPair_24 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_13;
        exception_lineno = exception_keeper_lineno_13;

        goto try_except_handler_17;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_17:;
        CHECK_OBJECT(outline_5_var___class__);
        Py_DECREF(outline_5_var___class__);
        outline_5_var___class__ = NULL;
        goto outline_result_6;
        // Exception handler code:
        try_except_handler_17:;
        exception_keeper_lineno_14 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_14 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_14;
        exception_lineno = exception_keeper_lineno_14;

        goto outline_exception_6;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_6:;
        exception_lineno = 24;
        goto try_except_handler_16;
        outline_result_6:;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__4_Query_16, mod_consts[40], tmp_dictset_value);
        Py_DECREF(tmp_dictset_value);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 24;
            type_description_2 = "o";
            goto try_except_handler_16;
        }
        goto try_end_5;
        // Exception handler code:
        try_except_handler_16:;
        exception_keeper_lineno_15 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_15 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        CHECK_OBJECT(tmp_Query$$36$class_creation_2__class_decl_dict);
        Py_DECREF(tmp_Query$$36$class_creation_2__class_decl_dict);
        tmp_Query$$36$class_creation_2__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Query$$36$class_creation_2__prepared);
        Py_DECREF(tmp_Query$$36$class_creation_2__prepared);
        tmp_Query$$36$class_creation_2__prepared = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_15;
        exception_lineno = exception_keeper_lineno_15;

        goto frame_exception_exit_3;
        // End of try:
        try_end_5:;


        // Put the previous frame back on top.
        popFrameStack(tstate);

        goto frame_no_exception_2;
        frame_exception_exit_3:


        {
            PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
            if (exception_tb == NULL) {
                exception_tb = MAKE_TRACEBACK(frame_frame_rethinkdb$ql2_pb2$$$class__4_Query_3, exception_lineno);
                SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
            } else if (exception_tb->tb_frame != &frame_frame_rethinkdb$ql2_pb2$$$class__4_Query_3->m_frame) {
                exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_rethinkdb$ql2_pb2$$$class__4_Query_3, exception_lineno);
                SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
            }
        }

        // Attaches locals to frame if any.
        Nuitka_Frame_AttachLocals(
            frame_frame_rethinkdb$ql2_pb2$$$class__4_Query_3,
            type_description_2,
            outline_3_var___class__
        );



        assertFrameObject(frame_frame_rethinkdb$ql2_pb2$$$class__4_Query_3);

        // Put the previous frame back on top.
        popFrameStack(tstate);

        // Return the error.
        goto nested_frame_exit_2;
        frame_no_exception_2:;
        goto skip_nested_handling_2;
        nested_frame_exit_2:;

        goto try_except_handler_12;
        skip_nested_handling_2:;
        CHECK_OBJECT(tmp_Query$$36$class_creation_2__class_decl_dict);
        Py_DECREF(tmp_Query$$36$class_creation_2__class_decl_dict);
        tmp_Query$$36$class_creation_2__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Query$$36$class_creation_2__prepared);
        Py_DECREF(tmp_Query$$36$class_creation_2__prepared);
        tmp_Query$$36$class_creation_2__prepared = NULL;
        {
            PyObject *tmp_assign_source_23;
            PyObject *tmp_called_value_6;
            PyObject *tmp_args_value_6;
            PyObject *tmp_tuple_element_6;
            PyObject *tmp_kwargs_value_6;
            tmp_called_value_6 = (PyObject *)&PyType_Type;
            tmp_tuple_element_6 = mod_consts[27];
            tmp_args_value_6 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_6, 0, tmp_tuple_element_6);
            tmp_tuple_element_6 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_6, 1, tmp_tuple_element_6);
            tmp_tuple_element_6 = locals_rethinkdb$ql2_pb2$$$class__4_Query_16;
            PyTuple_SET_ITEM0(tmp_args_value_6, 2, tmp_tuple_element_6);
            CHECK_OBJECT(tmp_class_creation_2__class_decl_dict);
            tmp_kwargs_value_6 = tmp_class_creation_2__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2->m_frame.f_lineno = 16;
            tmp_assign_source_23 = CALL_FUNCTION(tstate, tmp_called_value_6, tmp_args_value_6, tmp_kwargs_value_6);
            Py_DECREF(tmp_args_value_6);
            if (tmp_assign_source_23 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 16;

                goto try_except_handler_12;
            }
            assert(outline_3_var___class__ == NULL);
            outline_3_var___class__ = tmp_assign_source_23;
        }
        CHECK_OBJECT(outline_3_var___class__);
        tmp_assign_source_16 = outline_3_var___class__;
        Py_INCREF(tmp_assign_source_16);
        goto try_return_handler_12;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_12:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__4_Query_16);
        locals_rethinkdb$ql2_pb2$$$class__4_Query_16 = NULL;
        goto try_return_handler_11;
        // Exception handler code:
        try_except_handler_12:;
        exception_keeper_lineno_16 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_16 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__4_Query_16);
        locals_rethinkdb$ql2_pb2$$$class__4_Query_16 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_16;
        exception_lineno = exception_keeper_lineno_16;

        goto try_except_handler_11;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_11:;
        CHECK_OBJECT(outline_3_var___class__);
        Py_DECREF(outline_3_var___class__);
        outline_3_var___class__ = NULL;
        goto outline_result_4;
        // Exception handler code:
        try_except_handler_11:;
        exception_keeper_lineno_17 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_17 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_17;
        exception_lineno = exception_keeper_lineno_17;

        goto outline_exception_4;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_4:;
        exception_lineno = 16;
        goto try_except_handler_10;
        outline_result_4:;
        UPDATE_STRING_DICT1(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)mod_consts[27], tmp_assign_source_16);
    }
    goto try_end_6;
    // Exception handler code:
    try_except_handler_10:;
    exception_keeper_lineno_18 = exception_lineno;
    exception_lineno = 0;
    exception_keeper_name_18 = exception_state;
    INIT_ERROR_OCCURRED_STATE(&exception_state);

    CHECK_OBJECT(tmp_class_creation_2__class_decl_dict);
    Py_DECREF(tmp_class_creation_2__class_decl_dict);
    tmp_class_creation_2__class_decl_dict = NULL;
    CHECK_OBJECT(tmp_class_creation_2__prepared);
    Py_DECREF(tmp_class_creation_2__prepared);
    tmp_class_creation_2__prepared = NULL;
    // Re-raise.
    exception_state = exception_keeper_name_18;
    exception_lineno = exception_keeper_lineno_18;

    goto frame_exception_exit_1;
    // End of try:
    try_end_6:;
    CHECK_OBJECT(tmp_class_creation_2__class_decl_dict);
    Py_DECREF(tmp_class_creation_2__class_decl_dict);
    tmp_class_creation_2__class_decl_dict = NULL;
    CHECK_OBJECT(tmp_class_creation_2__prepared);
    Py_DECREF(tmp_class_creation_2__prepared);
    tmp_class_creation_2__prepared = NULL;
    {
        PyObject *tmp_assign_source_24;
        tmp_assign_source_24 = MAKE_DICT_EMPTY(tstate);
        assert(tmp_class_creation_3__class_decl_dict == NULL);
        tmp_class_creation_3__class_decl_dict = tmp_assign_source_24;
    }
    {
        PyObject *tmp_assign_source_25;
        tmp_assign_source_25 = MAKE_DICT_EMPTY(tstate);
        assert(tmp_class_creation_3__prepared == NULL);
        tmp_class_creation_3__prepared = tmp_assign_source_25;
    }
    // Tried code:
    {
        PyObject *tmp_assign_source_26;
        {
            PyObject *tmp_set_locals_7;
            CHECK_OBJECT(tmp_class_creation_3__prepared);
            tmp_set_locals_7 = tmp_class_creation_3__prepared;
            locals_rethinkdb$ql2_pb2$$$class__7_Frame_27 = tmp_set_locals_7;
            Py_INCREF(tmp_set_locals_7);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__7_Frame_27, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[41];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__7_Frame_27, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        {
            PyObject *tmp_assign_source_27;
            tmp_assign_source_27 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Frame$$36$class_creation_1__class_decl_dict == NULL);
            tmp_Frame$$36$class_creation_1__class_decl_dict = tmp_assign_source_27;
        }
        {
            PyObject *tmp_assign_source_28;
            tmp_assign_source_28 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Frame$$36$class_creation_1__prepared == NULL);
            tmp_Frame$$36$class_creation_1__prepared = tmp_assign_source_28;
        }
        // Tried code:
        // Tried code:
        frame_frame_rethinkdb$ql2_pb2$$$class__7_Frame_4 = MAKE_CLASS_FRAME(tstate, code_objects_3c58b5c3d1afd5951d8b1583051bd145, module_rethinkdb$ql2_pb2, NULL, sizeof(void *));

        // Push the new frame as the currently active one, and we should be exclusively
        // owning it.
        pushFrameStackCompiledFrame(tstate, frame_frame_rethinkdb$ql2_pb2$$$class__7_Frame_4);
        assert(Py_REFCNT(frame_frame_rethinkdb$ql2_pb2$$$class__7_Frame_4) == 2);

        // Framed code:
        // Tried code:
        {
            PyObject *tmp_set_locals_8;
            CHECK_OBJECT(tmp_Frame$$36$class_creation_1__prepared);
            tmp_set_locals_8 = tmp_Frame$$36$class_creation_1__prepared;
            locals_rethinkdb$ql2_pb2$$$class__8_FrameType_28 = tmp_set_locals_8;
            Py_INCREF(tmp_set_locals_8);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__8_FrameType_28, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[42];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__8_FrameType_28, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = const_int_pos_1;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__8_FrameType_28, mod_consts[43], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[30];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__8_FrameType_28, mod_consts[44], tmp_dictset_value);
        assert(!(tmp_result == false));
        // Tried code:
        // Tried code:
        {
            PyObject *tmp_assign_source_29;
            PyObject *tmp_called_value_7;
            PyObject *tmp_args_value_7;
            PyObject *tmp_tuple_element_7;
            PyObject *tmp_kwargs_value_7;
            tmp_called_value_7 = (PyObject *)&PyType_Type;
            tmp_tuple_element_7 = mod_consts[45];
            tmp_args_value_7 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_7, 0, tmp_tuple_element_7);
            tmp_tuple_element_7 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_7, 1, tmp_tuple_element_7);
            tmp_tuple_element_7 = locals_rethinkdb$ql2_pb2$$$class__8_FrameType_28;
            PyTuple_SET_ITEM0(tmp_args_value_7, 2, tmp_tuple_element_7);
            CHECK_OBJECT(tmp_Frame$$36$class_creation_1__class_decl_dict);
            tmp_kwargs_value_7 = tmp_Frame$$36$class_creation_1__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2$$$class__7_Frame_4->m_frame.f_lineno = 28;
            tmp_assign_source_29 = CALL_FUNCTION(tstate, tmp_called_value_7, tmp_args_value_7, tmp_kwargs_value_7);
            Py_DECREF(tmp_args_value_7);
            if (tmp_assign_source_29 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 28;
                type_description_2 = "o";
                goto try_except_handler_24;
            }
            assert(outline_7_var___class__ == NULL);
            outline_7_var___class__ = tmp_assign_source_29;
        }
        CHECK_OBJECT(outline_7_var___class__);
        tmp_dictset_value = outline_7_var___class__;
        Py_INCREF(tmp_dictset_value);
        goto try_return_handler_24;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_24:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__8_FrameType_28);
        locals_rethinkdb$ql2_pb2$$$class__8_FrameType_28 = NULL;
        goto try_return_handler_23;
        // Exception handler code:
        try_except_handler_24:;
        exception_keeper_lineno_19 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_19 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__8_FrameType_28);
        locals_rethinkdb$ql2_pb2$$$class__8_FrameType_28 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_19;
        exception_lineno = exception_keeper_lineno_19;

        goto try_except_handler_23;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_23:;
        CHECK_OBJECT(outline_7_var___class__);
        Py_DECREF(outline_7_var___class__);
        outline_7_var___class__ = NULL;
        goto outline_result_8;
        // Exception handler code:
        try_except_handler_23:;
        exception_keeper_lineno_20 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_20 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_20;
        exception_lineno = exception_keeper_lineno_20;

        goto outline_exception_8;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_8:;
        exception_lineno = 28;
        goto try_except_handler_22;
        outline_result_8:;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__7_Frame_27, mod_consts[45], tmp_dictset_value);
        Py_DECREF(tmp_dictset_value);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 28;
            type_description_2 = "o";
            goto try_except_handler_22;
        }
        goto try_end_7;
        // Exception handler code:
        try_except_handler_22:;
        exception_keeper_lineno_21 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_21 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        CHECK_OBJECT(tmp_Frame$$36$class_creation_1__class_decl_dict);
        Py_DECREF(tmp_Frame$$36$class_creation_1__class_decl_dict);
        tmp_Frame$$36$class_creation_1__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Frame$$36$class_creation_1__prepared);
        Py_DECREF(tmp_Frame$$36$class_creation_1__prepared);
        tmp_Frame$$36$class_creation_1__prepared = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_21;
        exception_lineno = exception_keeper_lineno_21;

        goto frame_exception_exit_4;
        // End of try:
        try_end_7:;


        // Put the previous frame back on top.
        popFrameStack(tstate);

        goto frame_no_exception_3;
        frame_exception_exit_4:


        {
            PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
            if (exception_tb == NULL) {
                exception_tb = MAKE_TRACEBACK(frame_frame_rethinkdb$ql2_pb2$$$class__7_Frame_4, exception_lineno);
                SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
            } else if (exception_tb->tb_frame != &frame_frame_rethinkdb$ql2_pb2$$$class__7_Frame_4->m_frame) {
                exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_rethinkdb$ql2_pb2$$$class__7_Frame_4, exception_lineno);
                SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
            }
        }

        // Attaches locals to frame if any.
        Nuitka_Frame_AttachLocals(
            frame_frame_rethinkdb$ql2_pb2$$$class__7_Frame_4,
            type_description_2,
            outline_6_var___class__
        );



        assertFrameObject(frame_frame_rethinkdb$ql2_pb2$$$class__7_Frame_4);

        // Put the previous frame back on top.
        popFrameStack(tstate);

        // Return the error.
        goto nested_frame_exit_3;
        frame_no_exception_3:;
        goto skip_nested_handling_3;
        nested_frame_exit_3:;

        goto try_except_handler_21;
        skip_nested_handling_3:;
        CHECK_OBJECT(tmp_Frame$$36$class_creation_1__class_decl_dict);
        Py_DECREF(tmp_Frame$$36$class_creation_1__class_decl_dict);
        tmp_Frame$$36$class_creation_1__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Frame$$36$class_creation_1__prepared);
        Py_DECREF(tmp_Frame$$36$class_creation_1__prepared);
        tmp_Frame$$36$class_creation_1__prepared = NULL;
        {
            PyObject *tmp_assign_source_30;
            PyObject *tmp_called_value_8;
            PyObject *tmp_args_value_8;
            PyObject *tmp_tuple_element_8;
            PyObject *tmp_kwargs_value_8;
            tmp_called_value_8 = (PyObject *)&PyType_Type;
            tmp_tuple_element_8 = mod_consts[41];
            tmp_args_value_8 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_8, 0, tmp_tuple_element_8);
            tmp_tuple_element_8 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_8, 1, tmp_tuple_element_8);
            tmp_tuple_element_8 = locals_rethinkdb$ql2_pb2$$$class__7_Frame_27;
            PyTuple_SET_ITEM0(tmp_args_value_8, 2, tmp_tuple_element_8);
            CHECK_OBJECT(tmp_class_creation_3__class_decl_dict);
            tmp_kwargs_value_8 = tmp_class_creation_3__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2->m_frame.f_lineno = 27;
            tmp_assign_source_30 = CALL_FUNCTION(tstate, tmp_called_value_8, tmp_args_value_8, tmp_kwargs_value_8);
            Py_DECREF(tmp_args_value_8);
            if (tmp_assign_source_30 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 27;

                goto try_except_handler_21;
            }
            assert(outline_6_var___class__ == NULL);
            outline_6_var___class__ = tmp_assign_source_30;
        }
        CHECK_OBJECT(outline_6_var___class__);
        tmp_assign_source_26 = outline_6_var___class__;
        Py_INCREF(tmp_assign_source_26);
        goto try_return_handler_21;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_21:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__7_Frame_27);
        locals_rethinkdb$ql2_pb2$$$class__7_Frame_27 = NULL;
        goto try_return_handler_20;
        // Exception handler code:
        try_except_handler_21:;
        exception_keeper_lineno_22 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_22 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__7_Frame_27);
        locals_rethinkdb$ql2_pb2$$$class__7_Frame_27 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_22;
        exception_lineno = exception_keeper_lineno_22;

        goto try_except_handler_20;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_20:;
        CHECK_OBJECT(outline_6_var___class__);
        Py_DECREF(outline_6_var___class__);
        outline_6_var___class__ = NULL;
        goto outline_result_7;
        // Exception handler code:
        try_except_handler_20:;
        exception_keeper_lineno_23 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_23 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_23;
        exception_lineno = exception_keeper_lineno_23;

        goto outline_exception_7;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_7:;
        exception_lineno = 27;
        goto try_except_handler_19;
        outline_result_7:;
        UPDATE_STRING_DICT1(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)mod_consts[41], tmp_assign_source_26);
    }
    goto try_end_8;
    // Exception handler code:
    try_except_handler_19:;
    exception_keeper_lineno_24 = exception_lineno;
    exception_lineno = 0;
    exception_keeper_name_24 = exception_state;
    INIT_ERROR_OCCURRED_STATE(&exception_state);

    CHECK_OBJECT(tmp_class_creation_3__class_decl_dict);
    Py_DECREF(tmp_class_creation_3__class_decl_dict);
    tmp_class_creation_3__class_decl_dict = NULL;
    CHECK_OBJECT(tmp_class_creation_3__prepared);
    Py_DECREF(tmp_class_creation_3__prepared);
    tmp_class_creation_3__prepared = NULL;
    // Re-raise.
    exception_state = exception_keeper_name_24;
    exception_lineno = exception_keeper_lineno_24;

    goto frame_exception_exit_1;
    // End of try:
    try_end_8:;
    CHECK_OBJECT(tmp_class_creation_3__class_decl_dict);
    Py_DECREF(tmp_class_creation_3__class_decl_dict);
    tmp_class_creation_3__class_decl_dict = NULL;
    CHECK_OBJECT(tmp_class_creation_3__prepared);
    Py_DECREF(tmp_class_creation_3__prepared);
    tmp_class_creation_3__prepared = NULL;
    {
        PyObject *tmp_assign_source_31;
        tmp_assign_source_31 = MAKE_DICT_EMPTY(tstate);
        assert(tmp_class_creation_4__class_decl_dict == NULL);
        tmp_class_creation_4__class_decl_dict = tmp_assign_source_31;
    }
    {
        PyObject *tmp_assign_source_32;
        tmp_assign_source_32 = MAKE_DICT_EMPTY(tstate);
        assert(tmp_class_creation_4__prepared == NULL);
        tmp_class_creation_4__prepared = tmp_assign_source_32;
    }
    // Tried code:
    {
        PyObject *tmp_assign_source_33;
        {
            PyObject *tmp_set_locals_9;
            CHECK_OBJECT(tmp_class_creation_4__prepared);
            tmp_set_locals_9 = tmp_class_creation_4__prepared;
            locals_rethinkdb$ql2_pb2$$$class__9_Backtrace_32 = tmp_set_locals_9;
            Py_INCREF(tmp_set_locals_9);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__9_Backtrace_32, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[46];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__9_Backtrace_32, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        // Tried code:
        // Tried code:
        {
            PyObject *tmp_assign_source_34;
            PyObject *tmp_called_value_9;
            PyObject *tmp_args_value_9;
            PyObject *tmp_tuple_element_9;
            PyObject *tmp_kwargs_value_9;
            tmp_called_value_9 = (PyObject *)&PyType_Type;
            tmp_tuple_element_9 = mod_consts[46];
            tmp_args_value_9 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_9, 0, tmp_tuple_element_9);
            tmp_tuple_element_9 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_9, 1, tmp_tuple_element_9);
            tmp_tuple_element_9 = locals_rethinkdb$ql2_pb2$$$class__9_Backtrace_32;
            PyTuple_SET_ITEM0(tmp_args_value_9, 2, tmp_tuple_element_9);
            CHECK_OBJECT(tmp_class_creation_4__class_decl_dict);
            tmp_kwargs_value_9 = tmp_class_creation_4__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2->m_frame.f_lineno = 32;
            tmp_assign_source_34 = CALL_FUNCTION(tstate, tmp_called_value_9, tmp_args_value_9, tmp_kwargs_value_9);
            Py_DECREF(tmp_args_value_9);
            if (tmp_assign_source_34 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 32;

                goto try_except_handler_27;
            }
            assert(outline_8_var___class__ == NULL);
            outline_8_var___class__ = tmp_assign_source_34;
        }
        CHECK_OBJECT(outline_8_var___class__);
        tmp_assign_source_33 = outline_8_var___class__;
        Py_INCREF(tmp_assign_source_33);
        goto try_return_handler_27;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_27:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__9_Backtrace_32);
        locals_rethinkdb$ql2_pb2$$$class__9_Backtrace_32 = NULL;
        goto try_return_handler_26;
        // Exception handler code:
        try_except_handler_27:;
        exception_keeper_lineno_25 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_25 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__9_Backtrace_32);
        locals_rethinkdb$ql2_pb2$$$class__9_Backtrace_32 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_25;
        exception_lineno = exception_keeper_lineno_25;

        goto try_except_handler_26;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_26:;
        CHECK_OBJECT(outline_8_var___class__);
        Py_DECREF(outline_8_var___class__);
        outline_8_var___class__ = NULL;
        goto outline_result_9;
        // Exception handler code:
        try_except_handler_26:;
        exception_keeper_lineno_26 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_26 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_26;
        exception_lineno = exception_keeper_lineno_26;

        goto outline_exception_9;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_9:;
        exception_lineno = 32;
        goto try_except_handler_25;
        outline_result_9:;
        UPDATE_STRING_DICT1(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)mod_consts[46], tmp_assign_source_33);
    }
    goto try_end_9;
    // Exception handler code:
    try_except_handler_25:;
    exception_keeper_lineno_27 = exception_lineno;
    exception_lineno = 0;
    exception_keeper_name_27 = exception_state;
    INIT_ERROR_OCCURRED_STATE(&exception_state);

    CHECK_OBJECT(tmp_class_creation_4__class_decl_dict);
    Py_DECREF(tmp_class_creation_4__class_decl_dict);
    tmp_class_creation_4__class_decl_dict = NULL;
    CHECK_OBJECT(tmp_class_creation_4__prepared);
    Py_DECREF(tmp_class_creation_4__prepared);
    tmp_class_creation_4__prepared = NULL;
    // Re-raise.
    exception_state = exception_keeper_name_27;
    exception_lineno = exception_keeper_lineno_27;

    goto frame_exception_exit_1;
    // End of try:
    try_end_9:;
    CHECK_OBJECT(tmp_class_creation_4__class_decl_dict);
    Py_DECREF(tmp_class_creation_4__class_decl_dict);
    tmp_class_creation_4__class_decl_dict = NULL;
    CHECK_OBJECT(tmp_class_creation_4__prepared);
    Py_DECREF(tmp_class_creation_4__prepared);
    tmp_class_creation_4__prepared = NULL;
    {
        PyObject *tmp_assign_source_35;
        tmp_assign_source_35 = MAKE_DICT_EMPTY(tstate);
        assert(tmp_class_creation_5__class_decl_dict == NULL);
        tmp_class_creation_5__class_decl_dict = tmp_assign_source_35;
    }
    {
        PyObject *tmp_assign_source_36;
        tmp_assign_source_36 = MAKE_DICT_EMPTY(tstate);
        assert(tmp_class_creation_5__prepared == NULL);
        tmp_class_creation_5__prepared = tmp_assign_source_36;
    }
    // Tried code:
    {
        PyObject *tmp_assign_source_37;
        {
            PyObject *tmp_set_locals_10;
            CHECK_OBJECT(tmp_class_creation_5__prepared);
            tmp_set_locals_10 = tmp_class_creation_5__prepared;
            locals_rethinkdb$ql2_pb2$$$class__10_Response_35 = tmp_set_locals_10;
            Py_INCREF(tmp_set_locals_10);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__10_Response_35, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[47];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__10_Response_35, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        {
            PyObject *tmp_assign_source_38;
            tmp_assign_source_38 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Response$$36$class_creation_1__class_decl_dict == NULL);
            tmp_Response$$36$class_creation_1__class_decl_dict = tmp_assign_source_38;
        }
        {
            PyObject *tmp_assign_source_39;
            tmp_assign_source_39 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Response$$36$class_creation_1__prepared == NULL);
            tmp_Response$$36$class_creation_1__prepared = tmp_assign_source_39;
        }
        // Tried code:
        // Tried code:
        frame_frame_rethinkdb$ql2_pb2$$$class__10_Response_5 = MAKE_CLASS_FRAME(tstate, code_objects_1270a1d483b75694b27debd6f1c56c87, module_rethinkdb$ql2_pb2, NULL, sizeof(void *));

        // Push the new frame as the currently active one, and we should be exclusively
        // owning it.
        pushFrameStackCompiledFrame(tstate, frame_frame_rethinkdb$ql2_pb2$$$class__10_Response_5);
        assert(Py_REFCNT(frame_frame_rethinkdb$ql2_pb2$$$class__10_Response_5) == 2);

        // Framed code:
        // Tried code:
        {
            PyObject *tmp_set_locals_11;
            CHECK_OBJECT(tmp_Response$$36$class_creation_1__prepared);
            tmp_set_locals_11 = tmp_Response$$36$class_creation_1__prepared;
            locals_rethinkdb$ql2_pb2$$$class__11_ResponseType_36 = tmp_set_locals_11;
            Py_INCREF(tmp_set_locals_11);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__11_ResponseType_36, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[48];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__11_ResponseType_36, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = const_int_pos_1;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__11_ResponseType_36, mod_consts[49], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[30];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__11_ResponseType_36, mod_consts[50], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[32];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__11_ResponseType_36, mod_consts[51], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[34];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__11_ResponseType_36, mod_consts[52], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[36];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__11_ResponseType_36, mod_consts[37], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[53];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__11_ResponseType_36, mod_consts[54], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[55];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__11_ResponseType_36, mod_consts[56], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[57];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__11_ResponseType_36, mod_consts[58], tmp_dictset_value);
        assert(!(tmp_result == false));
        // Tried code:
        // Tried code:
        {
            PyObject *tmp_assign_source_40;
            PyObject *tmp_called_value_10;
            PyObject *tmp_args_value_10;
            PyObject *tmp_tuple_element_10;
            PyObject *tmp_kwargs_value_10;
            tmp_called_value_10 = (PyObject *)&PyType_Type;
            tmp_tuple_element_10 = mod_consts[59];
            tmp_args_value_10 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_10, 0, tmp_tuple_element_10);
            tmp_tuple_element_10 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_10, 1, tmp_tuple_element_10);
            tmp_tuple_element_10 = locals_rethinkdb$ql2_pb2$$$class__11_ResponseType_36;
            PyTuple_SET_ITEM0(tmp_args_value_10, 2, tmp_tuple_element_10);
            CHECK_OBJECT(tmp_Response$$36$class_creation_1__class_decl_dict);
            tmp_kwargs_value_10 = tmp_Response$$36$class_creation_1__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2$$$class__10_Response_5->m_frame.f_lineno = 36;
            tmp_assign_source_40 = CALL_FUNCTION(tstate, tmp_called_value_10, tmp_args_value_10, tmp_kwargs_value_10);
            Py_DECREF(tmp_args_value_10);
            if (tmp_assign_source_40 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 36;
                type_description_2 = "o";
                goto try_except_handler_33;
            }
            assert(outline_10_var___class__ == NULL);
            outline_10_var___class__ = tmp_assign_source_40;
        }
        CHECK_OBJECT(outline_10_var___class__);
        tmp_dictset_value = outline_10_var___class__;
        Py_INCREF(tmp_dictset_value);
        goto try_return_handler_33;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_33:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__11_ResponseType_36);
        locals_rethinkdb$ql2_pb2$$$class__11_ResponseType_36 = NULL;
        goto try_return_handler_32;
        // Exception handler code:
        try_except_handler_33:;
        exception_keeper_lineno_28 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_28 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__11_ResponseType_36);
        locals_rethinkdb$ql2_pb2$$$class__11_ResponseType_36 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_28;
        exception_lineno = exception_keeper_lineno_28;

        goto try_except_handler_32;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_32:;
        CHECK_OBJECT(outline_10_var___class__);
        Py_DECREF(outline_10_var___class__);
        outline_10_var___class__ = NULL;
        goto outline_result_11;
        // Exception handler code:
        try_except_handler_32:;
        exception_keeper_lineno_29 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_29 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_29;
        exception_lineno = exception_keeper_lineno_29;

        goto outline_exception_11;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_11:;
        exception_lineno = 36;
        goto try_except_handler_31;
        outline_result_11:;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__10_Response_35, mod_consts[59], tmp_dictset_value);
        Py_DECREF(tmp_dictset_value);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 36;
            type_description_2 = "o";
            goto try_except_handler_31;
        }
        goto try_end_10;
        // Exception handler code:
        try_except_handler_31:;
        exception_keeper_lineno_30 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_30 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        CHECK_OBJECT(tmp_Response$$36$class_creation_1__class_decl_dict);
        Py_DECREF(tmp_Response$$36$class_creation_1__class_decl_dict);
        tmp_Response$$36$class_creation_1__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Response$$36$class_creation_1__prepared);
        Py_DECREF(tmp_Response$$36$class_creation_1__prepared);
        tmp_Response$$36$class_creation_1__prepared = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_30;
        exception_lineno = exception_keeper_lineno_30;

        goto frame_exception_exit_5;
        // End of try:
        try_end_10:;
        CHECK_OBJECT(tmp_Response$$36$class_creation_1__class_decl_dict);
        Py_DECREF(tmp_Response$$36$class_creation_1__class_decl_dict);
        tmp_Response$$36$class_creation_1__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Response$$36$class_creation_1__prepared);
        Py_DECREF(tmp_Response$$36$class_creation_1__prepared);
        tmp_Response$$36$class_creation_1__prepared = NULL;
        {
            PyObject *tmp_assign_source_41;
            tmp_assign_source_41 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Response$$36$class_creation_2__class_decl_dict == NULL);
            tmp_Response$$36$class_creation_2__class_decl_dict = tmp_assign_source_41;
        }
        {
            PyObject *tmp_assign_source_42;
            tmp_assign_source_42 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Response$$36$class_creation_2__prepared == NULL);
            tmp_Response$$36$class_creation_2__prepared = tmp_assign_source_42;
        }
        // Tried code:
        {
            PyObject *tmp_set_locals_12;
            CHECK_OBJECT(tmp_Response$$36$class_creation_2__prepared);
            tmp_set_locals_12 = tmp_Response$$36$class_creation_2__prepared;
            locals_rethinkdb$ql2_pb2$$$class__12_ErrorType_46 = tmp_set_locals_12;
            Py_INCREF(tmp_set_locals_12);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__12_ErrorType_46, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[60];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__12_ErrorType_46, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[61];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__12_ErrorType_46, mod_consts[62], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[63];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__12_ErrorType_46, mod_consts[64], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[65];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__12_ErrorType_46, mod_consts[66], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[67];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__12_ErrorType_46, mod_consts[68], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[69];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__12_ErrorType_46, mod_consts[70], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[71];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__12_ErrorType_46, mod_consts[72], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[73];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__12_ErrorType_46, mod_consts[74], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[75];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__12_ErrorType_46, mod_consts[76], tmp_dictset_value);
        assert(!(tmp_result == false));
        // Tried code:
        // Tried code:
        {
            PyObject *tmp_assign_source_43;
            PyObject *tmp_called_value_11;
            PyObject *tmp_args_value_11;
            PyObject *tmp_tuple_element_11;
            PyObject *tmp_kwargs_value_11;
            tmp_called_value_11 = (PyObject *)&PyType_Type;
            tmp_tuple_element_11 = mod_consts[77];
            tmp_args_value_11 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_11, 0, tmp_tuple_element_11);
            tmp_tuple_element_11 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_11, 1, tmp_tuple_element_11);
            tmp_tuple_element_11 = locals_rethinkdb$ql2_pb2$$$class__12_ErrorType_46;
            PyTuple_SET_ITEM0(tmp_args_value_11, 2, tmp_tuple_element_11);
            CHECK_OBJECT(tmp_Response$$36$class_creation_2__class_decl_dict);
            tmp_kwargs_value_11 = tmp_Response$$36$class_creation_2__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2$$$class__10_Response_5->m_frame.f_lineno = 46;
            tmp_assign_source_43 = CALL_FUNCTION(tstate, tmp_called_value_11, tmp_args_value_11, tmp_kwargs_value_11);
            Py_DECREF(tmp_args_value_11);
            if (tmp_assign_source_43 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 46;
                type_description_2 = "o";
                goto try_except_handler_36;
            }
            assert(outline_11_var___class__ == NULL);
            outline_11_var___class__ = tmp_assign_source_43;
        }
        CHECK_OBJECT(outline_11_var___class__);
        tmp_dictset_value = outline_11_var___class__;
        Py_INCREF(tmp_dictset_value);
        goto try_return_handler_36;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_36:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__12_ErrorType_46);
        locals_rethinkdb$ql2_pb2$$$class__12_ErrorType_46 = NULL;
        goto try_return_handler_35;
        // Exception handler code:
        try_except_handler_36:;
        exception_keeper_lineno_31 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_31 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__12_ErrorType_46);
        locals_rethinkdb$ql2_pb2$$$class__12_ErrorType_46 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_31;
        exception_lineno = exception_keeper_lineno_31;

        goto try_except_handler_35;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_35:;
        CHECK_OBJECT(outline_11_var___class__);
        Py_DECREF(outline_11_var___class__);
        outline_11_var___class__ = NULL;
        goto outline_result_12;
        // Exception handler code:
        try_except_handler_35:;
        exception_keeper_lineno_32 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_32 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_32;
        exception_lineno = exception_keeper_lineno_32;

        goto outline_exception_12;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_12:;
        exception_lineno = 46;
        goto try_except_handler_34;
        outline_result_12:;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__10_Response_35, mod_consts[77], tmp_dictset_value);
        Py_DECREF(tmp_dictset_value);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 46;
            type_description_2 = "o";
            goto try_except_handler_34;
        }
        goto try_end_11;
        // Exception handler code:
        try_except_handler_34:;
        exception_keeper_lineno_33 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_33 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        CHECK_OBJECT(tmp_Response$$36$class_creation_2__class_decl_dict);
        Py_DECREF(tmp_Response$$36$class_creation_2__class_decl_dict);
        tmp_Response$$36$class_creation_2__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Response$$36$class_creation_2__prepared);
        Py_DECREF(tmp_Response$$36$class_creation_2__prepared);
        tmp_Response$$36$class_creation_2__prepared = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_33;
        exception_lineno = exception_keeper_lineno_33;

        goto frame_exception_exit_5;
        // End of try:
        try_end_11:;
        CHECK_OBJECT(tmp_Response$$36$class_creation_2__class_decl_dict);
        Py_DECREF(tmp_Response$$36$class_creation_2__class_decl_dict);
        tmp_Response$$36$class_creation_2__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Response$$36$class_creation_2__prepared);
        Py_DECREF(tmp_Response$$36$class_creation_2__prepared);
        tmp_Response$$36$class_creation_2__prepared = NULL;
        {
            PyObject *tmp_assign_source_44;
            tmp_assign_source_44 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Response$$36$class_creation_3__class_decl_dict == NULL);
            tmp_Response$$36$class_creation_3__class_decl_dict = tmp_assign_source_44;
        }
        {
            PyObject *tmp_assign_source_45;
            tmp_assign_source_45 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Response$$36$class_creation_3__prepared == NULL);
            tmp_Response$$36$class_creation_3__prepared = tmp_assign_source_45;
        }
        // Tried code:
        {
            PyObject *tmp_set_locals_13;
            CHECK_OBJECT(tmp_Response$$36$class_creation_3__prepared);
            tmp_set_locals_13 = tmp_Response$$36$class_creation_3__prepared;
            locals_rethinkdb$ql2_pb2$$$class__13_ResponseNote_56 = tmp_set_locals_13;
            Py_INCREF(tmp_set_locals_13);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__13_ResponseNote_56, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[78];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__13_ResponseNote_56, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = const_int_pos_1;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__13_ResponseNote_56, mod_consts[79], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[30];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__13_ResponseNote_56, mod_consts[80], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[32];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__13_ResponseNote_56, mod_consts[81], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[34];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__13_ResponseNote_56, mod_consts[82], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[36];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__13_ResponseNote_56, mod_consts[83], tmp_dictset_value);
        assert(!(tmp_result == false));
        // Tried code:
        // Tried code:
        {
            PyObject *tmp_assign_source_46;
            PyObject *tmp_called_value_12;
            PyObject *tmp_args_value_12;
            PyObject *tmp_tuple_element_12;
            PyObject *tmp_kwargs_value_12;
            tmp_called_value_12 = (PyObject *)&PyType_Type;
            tmp_tuple_element_12 = mod_consts[84];
            tmp_args_value_12 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_12, 0, tmp_tuple_element_12);
            tmp_tuple_element_12 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_12, 1, tmp_tuple_element_12);
            tmp_tuple_element_12 = locals_rethinkdb$ql2_pb2$$$class__13_ResponseNote_56;
            PyTuple_SET_ITEM0(tmp_args_value_12, 2, tmp_tuple_element_12);
            CHECK_OBJECT(tmp_Response$$36$class_creation_3__class_decl_dict);
            tmp_kwargs_value_12 = tmp_Response$$36$class_creation_3__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2$$$class__10_Response_5->m_frame.f_lineno = 56;
            tmp_assign_source_46 = CALL_FUNCTION(tstate, tmp_called_value_12, tmp_args_value_12, tmp_kwargs_value_12);
            Py_DECREF(tmp_args_value_12);
            if (tmp_assign_source_46 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 56;
                type_description_2 = "o";
                goto try_except_handler_39;
            }
            assert(outline_12_var___class__ == NULL);
            outline_12_var___class__ = tmp_assign_source_46;
        }
        CHECK_OBJECT(outline_12_var___class__);
        tmp_dictset_value = outline_12_var___class__;
        Py_INCREF(tmp_dictset_value);
        goto try_return_handler_39;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_39:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__13_ResponseNote_56);
        locals_rethinkdb$ql2_pb2$$$class__13_ResponseNote_56 = NULL;
        goto try_return_handler_38;
        // Exception handler code:
        try_except_handler_39:;
        exception_keeper_lineno_34 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_34 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__13_ResponseNote_56);
        locals_rethinkdb$ql2_pb2$$$class__13_ResponseNote_56 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_34;
        exception_lineno = exception_keeper_lineno_34;

        goto try_except_handler_38;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_38:;
        CHECK_OBJECT(outline_12_var___class__);
        Py_DECREF(outline_12_var___class__);
        outline_12_var___class__ = NULL;
        goto outline_result_13;
        // Exception handler code:
        try_except_handler_38:;
        exception_keeper_lineno_35 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_35 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_35;
        exception_lineno = exception_keeper_lineno_35;

        goto outline_exception_13;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_13:;
        exception_lineno = 56;
        goto try_except_handler_37;
        outline_result_13:;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__10_Response_35, mod_consts[84], tmp_dictset_value);
        Py_DECREF(tmp_dictset_value);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 56;
            type_description_2 = "o";
            goto try_except_handler_37;
        }
        goto try_end_12;
        // Exception handler code:
        try_except_handler_37:;
        exception_keeper_lineno_36 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_36 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        CHECK_OBJECT(tmp_Response$$36$class_creation_3__class_decl_dict);
        Py_DECREF(tmp_Response$$36$class_creation_3__class_decl_dict);
        tmp_Response$$36$class_creation_3__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Response$$36$class_creation_3__prepared);
        Py_DECREF(tmp_Response$$36$class_creation_3__prepared);
        tmp_Response$$36$class_creation_3__prepared = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_36;
        exception_lineno = exception_keeper_lineno_36;

        goto frame_exception_exit_5;
        // End of try:
        try_end_12:;


        // Put the previous frame back on top.
        popFrameStack(tstate);

        goto frame_no_exception_4;
        frame_exception_exit_5:


        {
            PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
            if (exception_tb == NULL) {
                exception_tb = MAKE_TRACEBACK(frame_frame_rethinkdb$ql2_pb2$$$class__10_Response_5, exception_lineno);
                SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
            } else if (exception_tb->tb_frame != &frame_frame_rethinkdb$ql2_pb2$$$class__10_Response_5->m_frame) {
                exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_rethinkdb$ql2_pb2$$$class__10_Response_5, exception_lineno);
                SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
            }
        }

        // Attaches locals to frame if any.
        Nuitka_Frame_AttachLocals(
            frame_frame_rethinkdb$ql2_pb2$$$class__10_Response_5,
            type_description_2,
            outline_9_var___class__
        );



        assertFrameObject(frame_frame_rethinkdb$ql2_pb2$$$class__10_Response_5);

        // Put the previous frame back on top.
        popFrameStack(tstate);

        // Return the error.
        goto nested_frame_exit_4;
        frame_no_exception_4:;
        goto skip_nested_handling_4;
        nested_frame_exit_4:;

        goto try_except_handler_30;
        skip_nested_handling_4:;
        CHECK_OBJECT(tmp_Response$$36$class_creation_3__class_decl_dict);
        Py_DECREF(tmp_Response$$36$class_creation_3__class_decl_dict);
        tmp_Response$$36$class_creation_3__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Response$$36$class_creation_3__prepared);
        Py_DECREF(tmp_Response$$36$class_creation_3__prepared);
        tmp_Response$$36$class_creation_3__prepared = NULL;
        {
            PyObject *tmp_assign_source_47;
            PyObject *tmp_called_value_13;
            PyObject *tmp_args_value_13;
            PyObject *tmp_tuple_element_13;
            PyObject *tmp_kwargs_value_13;
            tmp_called_value_13 = (PyObject *)&PyType_Type;
            tmp_tuple_element_13 = mod_consts[47];
            tmp_args_value_13 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_13, 0, tmp_tuple_element_13);
            tmp_tuple_element_13 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_13, 1, tmp_tuple_element_13);
            tmp_tuple_element_13 = locals_rethinkdb$ql2_pb2$$$class__10_Response_35;
            PyTuple_SET_ITEM0(tmp_args_value_13, 2, tmp_tuple_element_13);
            CHECK_OBJECT(tmp_class_creation_5__class_decl_dict);
            tmp_kwargs_value_13 = tmp_class_creation_5__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2->m_frame.f_lineno = 35;
            tmp_assign_source_47 = CALL_FUNCTION(tstate, tmp_called_value_13, tmp_args_value_13, tmp_kwargs_value_13);
            Py_DECREF(tmp_args_value_13);
            if (tmp_assign_source_47 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 35;

                goto try_except_handler_30;
            }
            assert(outline_9_var___class__ == NULL);
            outline_9_var___class__ = tmp_assign_source_47;
        }
        CHECK_OBJECT(outline_9_var___class__);
        tmp_assign_source_37 = outline_9_var___class__;
        Py_INCREF(tmp_assign_source_37);
        goto try_return_handler_30;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_30:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__10_Response_35);
        locals_rethinkdb$ql2_pb2$$$class__10_Response_35 = NULL;
        goto try_return_handler_29;
        // Exception handler code:
        try_except_handler_30:;
        exception_keeper_lineno_37 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_37 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__10_Response_35);
        locals_rethinkdb$ql2_pb2$$$class__10_Response_35 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_37;
        exception_lineno = exception_keeper_lineno_37;

        goto try_except_handler_29;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_29:;
        CHECK_OBJECT(outline_9_var___class__);
        Py_DECREF(outline_9_var___class__);
        outline_9_var___class__ = NULL;
        goto outline_result_10;
        // Exception handler code:
        try_except_handler_29:;
        exception_keeper_lineno_38 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_38 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_38;
        exception_lineno = exception_keeper_lineno_38;

        goto outline_exception_10;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_10:;
        exception_lineno = 35;
        goto try_except_handler_28;
        outline_result_10:;
        UPDATE_STRING_DICT1(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)mod_consts[47], tmp_assign_source_37);
    }
    goto try_end_13;
    // Exception handler code:
    try_except_handler_28:;
    exception_keeper_lineno_39 = exception_lineno;
    exception_lineno = 0;
    exception_keeper_name_39 = exception_state;
    INIT_ERROR_OCCURRED_STATE(&exception_state);

    CHECK_OBJECT(tmp_class_creation_5__class_decl_dict);
    Py_DECREF(tmp_class_creation_5__class_decl_dict);
    tmp_class_creation_5__class_decl_dict = NULL;
    CHECK_OBJECT(tmp_class_creation_5__prepared);
    Py_DECREF(tmp_class_creation_5__prepared);
    tmp_class_creation_5__prepared = NULL;
    // Re-raise.
    exception_state = exception_keeper_name_39;
    exception_lineno = exception_keeper_lineno_39;

    goto frame_exception_exit_1;
    // End of try:
    try_end_13:;
    CHECK_OBJECT(tmp_class_creation_5__class_decl_dict);
    Py_DECREF(tmp_class_creation_5__class_decl_dict);
    tmp_class_creation_5__class_decl_dict = NULL;
    CHECK_OBJECT(tmp_class_creation_5__prepared);
    Py_DECREF(tmp_class_creation_5__prepared);
    tmp_class_creation_5__prepared = NULL;
    {
        PyObject *tmp_assign_source_48;
        tmp_assign_source_48 = MAKE_DICT_EMPTY(tstate);
        assert(tmp_class_creation_6__class_decl_dict == NULL);
        tmp_class_creation_6__class_decl_dict = tmp_assign_source_48;
    }
    {
        PyObject *tmp_assign_source_49;
        tmp_assign_source_49 = MAKE_DICT_EMPTY(tstate);
        assert(tmp_class_creation_6__prepared == NULL);
        tmp_class_creation_6__prepared = tmp_assign_source_49;
    }
    // Tried code:
    {
        PyObject *tmp_assign_source_50;
        {
            PyObject *tmp_set_locals_14;
            CHECK_OBJECT(tmp_class_creation_6__prepared);
            tmp_set_locals_14 = tmp_class_creation_6__prepared;
            locals_rethinkdb$ql2_pb2$$$class__14_Datum_63 = tmp_set_locals_14;
            Py_INCREF(tmp_set_locals_14);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__14_Datum_63, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[85];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__14_Datum_63, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        {
            PyObject *tmp_assign_source_51;
            tmp_assign_source_51 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Datum$$36$class_creation_1__class_decl_dict == NULL);
            tmp_Datum$$36$class_creation_1__class_decl_dict = tmp_assign_source_51;
        }
        {
            PyObject *tmp_assign_source_52;
            tmp_assign_source_52 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Datum$$36$class_creation_1__prepared == NULL);
            tmp_Datum$$36$class_creation_1__prepared = tmp_assign_source_52;
        }
        // Tried code:
        // Tried code:
        frame_frame_rethinkdb$ql2_pb2$$$class__14_Datum_6 = MAKE_CLASS_FRAME(tstate, code_objects_db9f95713d954ad2fb4562671f6edc73, module_rethinkdb$ql2_pb2, NULL, sizeof(void *));

        // Push the new frame as the currently active one, and we should be exclusively
        // owning it.
        pushFrameStackCompiledFrame(tstate, frame_frame_rethinkdb$ql2_pb2$$$class__14_Datum_6);
        assert(Py_REFCNT(frame_frame_rethinkdb$ql2_pb2$$$class__14_Datum_6) == 2);

        // Framed code:
        // Tried code:
        {
            PyObject *tmp_set_locals_15;
            CHECK_OBJECT(tmp_Datum$$36$class_creation_1__prepared);
            tmp_set_locals_15 = tmp_Datum$$36$class_creation_1__prepared;
            locals_rethinkdb$ql2_pb2$$$class__15_DatumType_64 = tmp_set_locals_15;
            Py_INCREF(tmp_set_locals_15);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__15_DatumType_64, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[86];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__15_DatumType_64, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = const_int_pos_1;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__15_DatumType_64, mod_consts[87], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[30];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__15_DatumType_64, mod_consts[88], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[32];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__15_DatumType_64, mod_consts[89], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[34];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__15_DatumType_64, mod_consts[90], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[36];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__15_DatumType_64, mod_consts[91], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[92];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__15_DatumType_64, mod_consts[93], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[94];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__15_DatumType_64, mod_consts[95], tmp_dictset_value);
        assert(!(tmp_result == false));
        // Tried code:
        // Tried code:
        {
            PyObject *tmp_assign_source_53;
            PyObject *tmp_called_value_14;
            PyObject *tmp_args_value_14;
            PyObject *tmp_tuple_element_14;
            PyObject *tmp_kwargs_value_14;
            tmp_called_value_14 = (PyObject *)&PyType_Type;
            tmp_tuple_element_14 = mod_consts[96];
            tmp_args_value_14 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_14, 0, tmp_tuple_element_14);
            tmp_tuple_element_14 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_14, 1, tmp_tuple_element_14);
            tmp_tuple_element_14 = locals_rethinkdb$ql2_pb2$$$class__15_DatumType_64;
            PyTuple_SET_ITEM0(tmp_args_value_14, 2, tmp_tuple_element_14);
            CHECK_OBJECT(tmp_Datum$$36$class_creation_1__class_decl_dict);
            tmp_kwargs_value_14 = tmp_Datum$$36$class_creation_1__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2$$$class__14_Datum_6->m_frame.f_lineno = 64;
            tmp_assign_source_53 = CALL_FUNCTION(tstate, tmp_called_value_14, tmp_args_value_14, tmp_kwargs_value_14);
            Py_DECREF(tmp_args_value_14);
            if (tmp_assign_source_53 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 64;
                type_description_2 = "o";
                goto try_except_handler_45;
            }
            assert(outline_14_var___class__ == NULL);
            outline_14_var___class__ = tmp_assign_source_53;
        }
        CHECK_OBJECT(outline_14_var___class__);
        tmp_dictset_value = outline_14_var___class__;
        Py_INCREF(tmp_dictset_value);
        goto try_return_handler_45;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_45:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__15_DatumType_64);
        locals_rethinkdb$ql2_pb2$$$class__15_DatumType_64 = NULL;
        goto try_return_handler_44;
        // Exception handler code:
        try_except_handler_45:;
        exception_keeper_lineno_40 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_40 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__15_DatumType_64);
        locals_rethinkdb$ql2_pb2$$$class__15_DatumType_64 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_40;
        exception_lineno = exception_keeper_lineno_40;

        goto try_except_handler_44;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_44:;
        CHECK_OBJECT(outline_14_var___class__);
        Py_DECREF(outline_14_var___class__);
        outline_14_var___class__ = NULL;
        goto outline_result_15;
        // Exception handler code:
        try_except_handler_44:;
        exception_keeper_lineno_41 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_41 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_41;
        exception_lineno = exception_keeper_lineno_41;

        goto outline_exception_15;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_15:;
        exception_lineno = 64;
        goto try_except_handler_43;
        outline_result_15:;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__14_Datum_63, mod_consts[96], tmp_dictset_value);
        Py_DECREF(tmp_dictset_value);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 64;
            type_description_2 = "o";
            goto try_except_handler_43;
        }
        goto try_end_14;
        // Exception handler code:
        try_except_handler_43:;
        exception_keeper_lineno_42 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_42 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        CHECK_OBJECT(tmp_Datum$$36$class_creation_1__class_decl_dict);
        Py_DECREF(tmp_Datum$$36$class_creation_1__class_decl_dict);
        tmp_Datum$$36$class_creation_1__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Datum$$36$class_creation_1__prepared);
        Py_DECREF(tmp_Datum$$36$class_creation_1__prepared);
        tmp_Datum$$36$class_creation_1__prepared = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_42;
        exception_lineno = exception_keeper_lineno_42;

        goto frame_exception_exit_6;
        // End of try:
        try_end_14:;
        CHECK_OBJECT(tmp_Datum$$36$class_creation_1__class_decl_dict);
        Py_DECREF(tmp_Datum$$36$class_creation_1__class_decl_dict);
        tmp_Datum$$36$class_creation_1__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Datum$$36$class_creation_1__prepared);
        Py_DECREF(tmp_Datum$$36$class_creation_1__prepared);
        tmp_Datum$$36$class_creation_1__prepared = NULL;
        {
            PyObject *tmp_assign_source_54;
            tmp_assign_source_54 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Datum$$36$class_creation_2__class_decl_dict == NULL);
            tmp_Datum$$36$class_creation_2__class_decl_dict = tmp_assign_source_54;
        }
        {
            PyObject *tmp_assign_source_55;
            tmp_assign_source_55 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Datum$$36$class_creation_2__prepared == NULL);
            tmp_Datum$$36$class_creation_2__prepared = tmp_assign_source_55;
        }
        // Tried code:
        {
            PyObject *tmp_set_locals_16;
            CHECK_OBJECT(tmp_Datum$$36$class_creation_2__prepared);
            tmp_set_locals_16 = tmp_Datum$$36$class_creation_2__prepared;
            locals_rethinkdb$ql2_pb2$$$class__16_AssocPair_73 = tmp_set_locals_16;
            Py_INCREF(tmp_set_locals_16);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__16_AssocPair_73, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[97];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__16_AssocPair_73, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        // Tried code:
        // Tried code:
        {
            PyObject *tmp_assign_source_56;
            PyObject *tmp_called_value_15;
            PyObject *tmp_args_value_15;
            PyObject *tmp_tuple_element_15;
            PyObject *tmp_kwargs_value_15;
            tmp_called_value_15 = (PyObject *)&PyType_Type;
            tmp_tuple_element_15 = mod_consts[40];
            tmp_args_value_15 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_15, 0, tmp_tuple_element_15);
            tmp_tuple_element_15 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_15, 1, tmp_tuple_element_15);
            tmp_tuple_element_15 = locals_rethinkdb$ql2_pb2$$$class__16_AssocPair_73;
            PyTuple_SET_ITEM0(tmp_args_value_15, 2, tmp_tuple_element_15);
            CHECK_OBJECT(tmp_Datum$$36$class_creation_2__class_decl_dict);
            tmp_kwargs_value_15 = tmp_Datum$$36$class_creation_2__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2$$$class__14_Datum_6->m_frame.f_lineno = 73;
            tmp_assign_source_56 = CALL_FUNCTION(tstate, tmp_called_value_15, tmp_args_value_15, tmp_kwargs_value_15);
            Py_DECREF(tmp_args_value_15);
            if (tmp_assign_source_56 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 73;
                type_description_2 = "o";
                goto try_except_handler_48;
            }
            assert(outline_15_var___class__ == NULL);
            outline_15_var___class__ = tmp_assign_source_56;
        }
        CHECK_OBJECT(outline_15_var___class__);
        tmp_dictset_value = outline_15_var___class__;
        Py_INCREF(tmp_dictset_value);
        goto try_return_handler_48;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_48:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__16_AssocPair_73);
        locals_rethinkdb$ql2_pb2$$$class__16_AssocPair_73 = NULL;
        goto try_return_handler_47;
        // Exception handler code:
        try_except_handler_48:;
        exception_keeper_lineno_43 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_43 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__16_AssocPair_73);
        locals_rethinkdb$ql2_pb2$$$class__16_AssocPair_73 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_43;
        exception_lineno = exception_keeper_lineno_43;

        goto try_except_handler_47;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_47:;
        CHECK_OBJECT(outline_15_var___class__);
        Py_DECREF(outline_15_var___class__);
        outline_15_var___class__ = NULL;
        goto outline_result_16;
        // Exception handler code:
        try_except_handler_47:;
        exception_keeper_lineno_44 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_44 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_44;
        exception_lineno = exception_keeper_lineno_44;

        goto outline_exception_16;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_16:;
        exception_lineno = 73;
        goto try_except_handler_46;
        outline_result_16:;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__14_Datum_63, mod_consts[40], tmp_dictset_value);
        Py_DECREF(tmp_dictset_value);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 73;
            type_description_2 = "o";
            goto try_except_handler_46;
        }
        goto try_end_15;
        // Exception handler code:
        try_except_handler_46:;
        exception_keeper_lineno_45 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_45 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        CHECK_OBJECT(tmp_Datum$$36$class_creation_2__class_decl_dict);
        Py_DECREF(tmp_Datum$$36$class_creation_2__class_decl_dict);
        tmp_Datum$$36$class_creation_2__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Datum$$36$class_creation_2__prepared);
        Py_DECREF(tmp_Datum$$36$class_creation_2__prepared);
        tmp_Datum$$36$class_creation_2__prepared = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_45;
        exception_lineno = exception_keeper_lineno_45;

        goto frame_exception_exit_6;
        // End of try:
        try_end_15:;


        // Put the previous frame back on top.
        popFrameStack(tstate);

        goto frame_no_exception_5;
        frame_exception_exit_6:


        {
            PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
            if (exception_tb == NULL) {
                exception_tb = MAKE_TRACEBACK(frame_frame_rethinkdb$ql2_pb2$$$class__14_Datum_6, exception_lineno);
                SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
            } else if (exception_tb->tb_frame != &frame_frame_rethinkdb$ql2_pb2$$$class__14_Datum_6->m_frame) {
                exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_rethinkdb$ql2_pb2$$$class__14_Datum_6, exception_lineno);
                SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
            }
        }

        // Attaches locals to frame if any.
        Nuitka_Frame_AttachLocals(
            frame_frame_rethinkdb$ql2_pb2$$$class__14_Datum_6,
            type_description_2,
            outline_13_var___class__
        );



        assertFrameObject(frame_frame_rethinkdb$ql2_pb2$$$class__14_Datum_6);

        // Put the previous frame back on top.
        popFrameStack(tstate);

        // Return the error.
        goto nested_frame_exit_5;
        frame_no_exception_5:;
        goto skip_nested_handling_5;
        nested_frame_exit_5:;

        goto try_except_handler_42;
        skip_nested_handling_5:;
        CHECK_OBJECT(tmp_Datum$$36$class_creation_2__class_decl_dict);
        Py_DECREF(tmp_Datum$$36$class_creation_2__class_decl_dict);
        tmp_Datum$$36$class_creation_2__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Datum$$36$class_creation_2__prepared);
        Py_DECREF(tmp_Datum$$36$class_creation_2__prepared);
        tmp_Datum$$36$class_creation_2__prepared = NULL;
        {
            PyObject *tmp_assign_source_57;
            PyObject *tmp_called_value_16;
            PyObject *tmp_args_value_16;
            PyObject *tmp_tuple_element_16;
            PyObject *tmp_kwargs_value_16;
            tmp_called_value_16 = (PyObject *)&PyType_Type;
            tmp_tuple_element_16 = mod_consts[85];
            tmp_args_value_16 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_16, 0, tmp_tuple_element_16);
            tmp_tuple_element_16 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_16, 1, tmp_tuple_element_16);
            tmp_tuple_element_16 = locals_rethinkdb$ql2_pb2$$$class__14_Datum_63;
            PyTuple_SET_ITEM0(tmp_args_value_16, 2, tmp_tuple_element_16);
            CHECK_OBJECT(tmp_class_creation_6__class_decl_dict);
            tmp_kwargs_value_16 = tmp_class_creation_6__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2->m_frame.f_lineno = 63;
            tmp_assign_source_57 = CALL_FUNCTION(tstate, tmp_called_value_16, tmp_args_value_16, tmp_kwargs_value_16);
            Py_DECREF(tmp_args_value_16);
            if (tmp_assign_source_57 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 63;

                goto try_except_handler_42;
            }
            assert(outline_13_var___class__ == NULL);
            outline_13_var___class__ = tmp_assign_source_57;
        }
        CHECK_OBJECT(outline_13_var___class__);
        tmp_assign_source_50 = outline_13_var___class__;
        Py_INCREF(tmp_assign_source_50);
        goto try_return_handler_42;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_42:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__14_Datum_63);
        locals_rethinkdb$ql2_pb2$$$class__14_Datum_63 = NULL;
        goto try_return_handler_41;
        // Exception handler code:
        try_except_handler_42:;
        exception_keeper_lineno_46 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_46 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__14_Datum_63);
        locals_rethinkdb$ql2_pb2$$$class__14_Datum_63 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_46;
        exception_lineno = exception_keeper_lineno_46;

        goto try_except_handler_41;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_41:;
        CHECK_OBJECT(outline_13_var___class__);
        Py_DECREF(outline_13_var___class__);
        outline_13_var___class__ = NULL;
        goto outline_result_14;
        // Exception handler code:
        try_except_handler_41:;
        exception_keeper_lineno_47 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_47 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_47;
        exception_lineno = exception_keeper_lineno_47;

        goto outline_exception_14;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_14:;
        exception_lineno = 63;
        goto try_except_handler_40;
        outline_result_14:;
        UPDATE_STRING_DICT1(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)mod_consts[85], tmp_assign_source_50);
    }
    goto try_end_16;
    // Exception handler code:
    try_except_handler_40:;
    exception_keeper_lineno_48 = exception_lineno;
    exception_lineno = 0;
    exception_keeper_name_48 = exception_state;
    INIT_ERROR_OCCURRED_STATE(&exception_state);

    CHECK_OBJECT(tmp_class_creation_6__class_decl_dict);
    Py_DECREF(tmp_class_creation_6__class_decl_dict);
    tmp_class_creation_6__class_decl_dict = NULL;
    CHECK_OBJECT(tmp_class_creation_6__prepared);
    Py_DECREF(tmp_class_creation_6__prepared);
    tmp_class_creation_6__prepared = NULL;
    // Re-raise.
    exception_state = exception_keeper_name_48;
    exception_lineno = exception_keeper_lineno_48;

    goto frame_exception_exit_1;
    // End of try:
    try_end_16:;
    CHECK_OBJECT(tmp_class_creation_6__class_decl_dict);
    Py_DECREF(tmp_class_creation_6__class_decl_dict);
    tmp_class_creation_6__class_decl_dict = NULL;
    CHECK_OBJECT(tmp_class_creation_6__prepared);
    Py_DECREF(tmp_class_creation_6__prepared);
    tmp_class_creation_6__prepared = NULL;
    {
        PyObject *tmp_assign_source_58;
        tmp_assign_source_58 = MAKE_DICT_EMPTY(tstate);
        assert(tmp_class_creation_7__class_decl_dict == NULL);
        tmp_class_creation_7__class_decl_dict = tmp_assign_source_58;
    }
    {
        PyObject *tmp_assign_source_59;
        tmp_assign_source_59 = MAKE_DICT_EMPTY(tstate);
        assert(tmp_class_creation_7__prepared == NULL);
        tmp_class_creation_7__prepared = tmp_assign_source_59;
    }
    // Tried code:
    {
        PyObject *tmp_assign_source_60;
        {
            PyObject *tmp_set_locals_17;
            CHECK_OBJECT(tmp_class_creation_7__prepared);
            tmp_set_locals_17 = tmp_class_creation_7__prepared;
            locals_rethinkdb$ql2_pb2$$$class__17_Term_76 = tmp_set_locals_17;
            Py_INCREF(tmp_set_locals_17);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__17_Term_76, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[98];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__17_Term_76, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        {
            PyObject *tmp_assign_source_61;
            tmp_assign_source_61 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Term$$36$class_creation_1__class_decl_dict == NULL);
            tmp_Term$$36$class_creation_1__class_decl_dict = tmp_assign_source_61;
        }
        {
            PyObject *tmp_assign_source_62;
            tmp_assign_source_62 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Term$$36$class_creation_1__prepared == NULL);
            tmp_Term$$36$class_creation_1__prepared = tmp_assign_source_62;
        }
        // Tried code:
        // Tried code:
        frame_frame_rethinkdb$ql2_pb2$$$class__17_Term_7 = MAKE_CLASS_FRAME(tstate, code_objects_c8770b9feff63c62b48461a01db850f3, module_rethinkdb$ql2_pb2, NULL, sizeof(void *));

        // Push the new frame as the currently active one, and we should be exclusively
        // owning it.
        pushFrameStackCompiledFrame(tstate, frame_frame_rethinkdb$ql2_pb2$$$class__17_Term_7);
        assert(Py_REFCNT(frame_frame_rethinkdb$ql2_pb2$$$class__17_Term_7) == 2);

        // Framed code:
        // Tried code:
        {
            PyObject *tmp_set_locals_18;
            CHECK_OBJECT(tmp_Term$$36$class_creation_1__prepared);
            tmp_set_locals_18 = tmp_Term$$36$class_creation_1__prepared;
            locals_rethinkdb$ql2_pb2$$$class__18_TermType_77 = tmp_set_locals_18;
            Py_INCREF(tmp_set_locals_18);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[99];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = const_int_pos_1;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[100], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[30];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[101], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[32];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[102], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[103];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[104], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[105];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[106], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[107];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[108], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[109];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[110], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[111];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[112], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[113];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[114], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[115];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[116], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[117];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[118], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[53];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[119], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[120];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[121], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[55];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[122], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[57];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[123], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[124];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[125], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[126];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[127], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[128];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[129], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[130];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[131], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[132];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[133], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[134];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[135], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[136];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[137], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[138];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[139], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[140];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[141], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[142];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[143], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[144];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[145], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[146];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[147], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[148];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[149], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[150];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[151], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[152];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[153], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[154];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[155], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[156];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[157], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[158];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[159], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[160];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[161], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[162];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[163], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[164];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[165], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[166];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[167], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[168];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[169], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[170];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[171], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[172];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[173], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[174];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[175], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[176];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[177], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[178];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[179], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[180];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[181], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[182];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[183], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[184];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[185], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[186];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[187], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[188];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[189], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[190];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[191], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[192];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[193], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[194];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[195], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[196];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[197], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[198];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[199], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[200];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[201], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[202];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[203], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[204];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[205], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[206];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[207], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[208];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[209], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[210];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[211], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[212];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[213], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[214];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[215], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[216];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[217], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[218];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[219], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[220];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[221], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[222];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[223], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[224];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[225], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[226];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[227], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[228];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[229], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[230];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[231], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[232];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[233], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[234];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[235], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[236];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[237], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[238];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[239], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[240];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[241], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[242];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[243], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[244];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[245], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[246];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[247], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[248];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[249], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[250];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[251], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[252];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[253], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[254];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[255], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[256];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[257], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[258];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[259], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[260];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[261], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[262];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[263], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[264];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[265], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[266];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[267], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[268];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[269], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[270];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[271], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[272];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[273], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[274];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[275], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[276];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[277], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[278];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[279], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[280];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[281], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[282];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[283], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[284];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[285], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[286];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[287], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[288];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[289], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[290];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[291], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[292];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[293], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[294];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[295], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[296];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[297], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[298];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[299], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[300];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[301], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[302];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[303], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[304];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[305], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[306];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[307], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[308];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[309], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[310];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[311], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[312];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[313], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[314];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[315], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[316];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[317], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[318];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[319], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[320];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[25], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[321];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[322], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[323];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[324], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[325];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[326], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[327];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[328], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[329];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[330], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[331];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[332], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[333];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[334], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[335];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[336], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[337];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[338], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[339];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[340], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[341];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[342], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[343];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[344], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[345];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[346], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[347];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[348], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[349];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[350], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[351];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[352], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[353];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[354], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[355];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[356], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[357];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[358], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[359];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[360], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[361];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[362], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[363];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[364], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[365];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[366], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[367];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[368], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[369];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[370], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[371];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[372], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[373];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[374], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[375];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[376], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[377];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[378], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[379];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[380], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[381];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[382], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[383];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[384], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[385];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[386], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[387];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[388], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[389];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[390], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[391];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[392], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[393];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[394], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[395];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[396], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[397];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[398], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[399];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[400], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[401];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[402], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[403];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[404], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[405];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[406], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[407];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[408], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[409];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[410], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[411];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[412], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[413];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[414], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[415];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[416], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[417];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[418], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[419];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[420], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[421];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[422], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[423];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[424], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[425];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[426], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[427];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[428], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[429];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[430], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[431];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[432], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[433];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[434], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[435];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[436], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[437];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[438], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[439];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[440], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[441];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[442], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[443];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[444], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[445];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[446], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[447];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[448], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[449];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[450], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[451];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[452], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[453];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[454], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[455];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[456], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[457];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[458], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[459];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[460], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[461];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[462], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[463];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77, mod_consts[464], tmp_dictset_value);
        assert(!(tmp_result == false));
        // Tried code:
        // Tried code:
        {
            PyObject *tmp_assign_source_63;
            PyObject *tmp_called_value_17;
            PyObject *tmp_args_value_17;
            PyObject *tmp_tuple_element_17;
            PyObject *tmp_kwargs_value_17;
            tmp_called_value_17 = (PyObject *)&PyType_Type;
            tmp_tuple_element_17 = mod_consts[465];
            tmp_args_value_17 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_17, 0, tmp_tuple_element_17);
            tmp_tuple_element_17 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_17, 1, tmp_tuple_element_17);
            tmp_tuple_element_17 = locals_rethinkdb$ql2_pb2$$$class__18_TermType_77;
            PyTuple_SET_ITEM0(tmp_args_value_17, 2, tmp_tuple_element_17);
            CHECK_OBJECT(tmp_Term$$36$class_creation_1__class_decl_dict);
            tmp_kwargs_value_17 = tmp_Term$$36$class_creation_1__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2$$$class__17_Term_7->m_frame.f_lineno = 77;
            tmp_assign_source_63 = CALL_FUNCTION(tstate, tmp_called_value_17, tmp_args_value_17, tmp_kwargs_value_17);
            Py_DECREF(tmp_args_value_17);
            if (tmp_assign_source_63 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 77;
                type_description_2 = "o";
                goto try_except_handler_54;
            }
            assert(outline_17_var___class__ == NULL);
            outline_17_var___class__ = tmp_assign_source_63;
        }
        CHECK_OBJECT(outline_17_var___class__);
        tmp_dictset_value = outline_17_var___class__;
        Py_INCREF(tmp_dictset_value);
        goto try_return_handler_54;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_54:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77);
        locals_rethinkdb$ql2_pb2$$$class__18_TermType_77 = NULL;
        goto try_return_handler_53;
        // Exception handler code:
        try_except_handler_54:;
        exception_keeper_lineno_49 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_49 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__18_TermType_77);
        locals_rethinkdb$ql2_pb2$$$class__18_TermType_77 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_49;
        exception_lineno = exception_keeper_lineno_49;

        goto try_except_handler_53;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_53:;
        CHECK_OBJECT(outline_17_var___class__);
        Py_DECREF(outline_17_var___class__);
        outline_17_var___class__ = NULL;
        goto outline_result_18;
        // Exception handler code:
        try_except_handler_53:;
        exception_keeper_lineno_50 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_50 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_50;
        exception_lineno = exception_keeper_lineno_50;

        goto outline_exception_18;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_18:;
        exception_lineno = 77;
        goto try_except_handler_52;
        outline_result_18:;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__17_Term_76, mod_consts[465], tmp_dictset_value);
        Py_DECREF(tmp_dictset_value);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 77;
            type_description_2 = "o";
            goto try_except_handler_52;
        }
        goto try_end_17;
        // Exception handler code:
        try_except_handler_52:;
        exception_keeper_lineno_51 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_51 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        CHECK_OBJECT(tmp_Term$$36$class_creation_1__class_decl_dict);
        Py_DECREF(tmp_Term$$36$class_creation_1__class_decl_dict);
        tmp_Term$$36$class_creation_1__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Term$$36$class_creation_1__prepared);
        Py_DECREF(tmp_Term$$36$class_creation_1__prepared);
        tmp_Term$$36$class_creation_1__prepared = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_51;
        exception_lineno = exception_keeper_lineno_51;

        goto frame_exception_exit_7;
        // End of try:
        try_end_17:;
        CHECK_OBJECT(tmp_Term$$36$class_creation_1__class_decl_dict);
        Py_DECREF(tmp_Term$$36$class_creation_1__class_decl_dict);
        tmp_Term$$36$class_creation_1__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Term$$36$class_creation_1__prepared);
        Py_DECREF(tmp_Term$$36$class_creation_1__prepared);
        tmp_Term$$36$class_creation_1__prepared = NULL;
        {
            PyObject *tmp_assign_source_64;
            tmp_assign_source_64 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Term$$36$class_creation_2__class_decl_dict == NULL);
            tmp_Term$$36$class_creation_2__class_decl_dict = tmp_assign_source_64;
        }
        {
            PyObject *tmp_assign_source_65;
            tmp_assign_source_65 = MAKE_DICT_EMPTY(tstate);
            assert(tmp_Term$$36$class_creation_2__prepared == NULL);
            tmp_Term$$36$class_creation_2__prepared = tmp_assign_source_65;
        }
        // Tried code:
        {
            PyObject *tmp_set_locals_19;
            CHECK_OBJECT(tmp_Term$$36$class_creation_2__prepared);
            tmp_set_locals_19 = tmp_Term$$36$class_creation_2__prepared;
            locals_rethinkdb$ql2_pb2$$$class__19_AssocPair_265 = tmp_set_locals_19;
            Py_INCREF(tmp_set_locals_19);
        }
        tmp_dictset_value = mod_consts[5];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__19_AssocPair_265, mod_consts[6], tmp_dictset_value);
        assert(!(tmp_result == false));
        tmp_dictset_value = mod_consts[466];
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__19_AssocPair_265, mod_consts[8], tmp_dictset_value);
        assert(!(tmp_result == false));
        // Tried code:
        // Tried code:
        {
            PyObject *tmp_assign_source_66;
            PyObject *tmp_called_value_18;
            PyObject *tmp_args_value_18;
            PyObject *tmp_tuple_element_18;
            PyObject *tmp_kwargs_value_18;
            tmp_called_value_18 = (PyObject *)&PyType_Type;
            tmp_tuple_element_18 = mod_consts[40];
            tmp_args_value_18 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_18, 0, tmp_tuple_element_18);
            tmp_tuple_element_18 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_18, 1, tmp_tuple_element_18);
            tmp_tuple_element_18 = locals_rethinkdb$ql2_pb2$$$class__19_AssocPair_265;
            PyTuple_SET_ITEM0(tmp_args_value_18, 2, tmp_tuple_element_18);
            CHECK_OBJECT(tmp_Term$$36$class_creation_2__class_decl_dict);
            tmp_kwargs_value_18 = tmp_Term$$36$class_creation_2__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2$$$class__17_Term_7->m_frame.f_lineno = 265;
            tmp_assign_source_66 = CALL_FUNCTION(tstate, tmp_called_value_18, tmp_args_value_18, tmp_kwargs_value_18);
            Py_DECREF(tmp_args_value_18);
            if (tmp_assign_source_66 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 265;
                type_description_2 = "o";
                goto try_except_handler_57;
            }
            assert(outline_18_var___class__ == NULL);
            outline_18_var___class__ = tmp_assign_source_66;
        }
        CHECK_OBJECT(outline_18_var___class__);
        tmp_dictset_value = outline_18_var___class__;
        Py_INCREF(tmp_dictset_value);
        goto try_return_handler_57;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_57:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__19_AssocPair_265);
        locals_rethinkdb$ql2_pb2$$$class__19_AssocPair_265 = NULL;
        goto try_return_handler_56;
        // Exception handler code:
        try_except_handler_57:;
        exception_keeper_lineno_52 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_52 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__19_AssocPair_265);
        locals_rethinkdb$ql2_pb2$$$class__19_AssocPair_265 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_52;
        exception_lineno = exception_keeper_lineno_52;

        goto try_except_handler_56;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_56:;
        CHECK_OBJECT(outline_18_var___class__);
        Py_DECREF(outline_18_var___class__);
        outline_18_var___class__ = NULL;
        goto outline_result_19;
        // Exception handler code:
        try_except_handler_56:;
        exception_keeper_lineno_53 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_53 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_53;
        exception_lineno = exception_keeper_lineno_53;

        goto outline_exception_19;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_19:;
        exception_lineno = 265;
        goto try_except_handler_55;
        outline_result_19:;
        tmp_result = DICT_SET_ITEM(locals_rethinkdb$ql2_pb2$$$class__17_Term_76, mod_consts[40], tmp_dictset_value);
        Py_DECREF(tmp_dictset_value);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 265;
            type_description_2 = "o";
            goto try_except_handler_55;
        }
        goto try_end_18;
        // Exception handler code:
        try_except_handler_55:;
        exception_keeper_lineno_54 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_54 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        CHECK_OBJECT(tmp_Term$$36$class_creation_2__class_decl_dict);
        Py_DECREF(tmp_Term$$36$class_creation_2__class_decl_dict);
        tmp_Term$$36$class_creation_2__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Term$$36$class_creation_2__prepared);
        Py_DECREF(tmp_Term$$36$class_creation_2__prepared);
        tmp_Term$$36$class_creation_2__prepared = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_54;
        exception_lineno = exception_keeper_lineno_54;

        goto frame_exception_exit_7;
        // End of try:
        try_end_18:;


        // Put the previous frame back on top.
        popFrameStack(tstate);

        goto frame_no_exception_6;
        frame_exception_exit_7:


        {
            PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
            if (exception_tb == NULL) {
                exception_tb = MAKE_TRACEBACK(frame_frame_rethinkdb$ql2_pb2$$$class__17_Term_7, exception_lineno);
                SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
            } else if (exception_tb->tb_frame != &frame_frame_rethinkdb$ql2_pb2$$$class__17_Term_7->m_frame) {
                exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_rethinkdb$ql2_pb2$$$class__17_Term_7, exception_lineno);
                SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
            }
        }

        // Attaches locals to frame if any.
        Nuitka_Frame_AttachLocals(
            frame_frame_rethinkdb$ql2_pb2$$$class__17_Term_7,
            type_description_2,
            outline_16_var___class__
        );



        assertFrameObject(frame_frame_rethinkdb$ql2_pb2$$$class__17_Term_7);

        // Put the previous frame back on top.
        popFrameStack(tstate);

        // Return the error.
        goto nested_frame_exit_6;
        frame_no_exception_6:;
        goto skip_nested_handling_6;
        nested_frame_exit_6:;

        goto try_except_handler_51;
        skip_nested_handling_6:;
        CHECK_OBJECT(tmp_Term$$36$class_creation_2__class_decl_dict);
        Py_DECREF(tmp_Term$$36$class_creation_2__class_decl_dict);
        tmp_Term$$36$class_creation_2__class_decl_dict = NULL;
        CHECK_OBJECT(tmp_Term$$36$class_creation_2__prepared);
        Py_DECREF(tmp_Term$$36$class_creation_2__prepared);
        tmp_Term$$36$class_creation_2__prepared = NULL;
        {
            PyObject *tmp_assign_source_67;
            PyObject *tmp_called_value_19;
            PyObject *tmp_args_value_19;
            PyObject *tmp_tuple_element_19;
            PyObject *tmp_kwargs_value_19;
            tmp_called_value_19 = (PyObject *)&PyType_Type;
            tmp_tuple_element_19 = mod_consts[98];
            tmp_args_value_19 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM0(tmp_args_value_19, 0, tmp_tuple_element_19);
            tmp_tuple_element_19 = const_tuple_empty;
            PyTuple_SET_ITEM0(tmp_args_value_19, 1, tmp_tuple_element_19);
            tmp_tuple_element_19 = locals_rethinkdb$ql2_pb2$$$class__17_Term_76;
            PyTuple_SET_ITEM0(tmp_args_value_19, 2, tmp_tuple_element_19);
            CHECK_OBJECT(tmp_class_creation_7__class_decl_dict);
            tmp_kwargs_value_19 = tmp_class_creation_7__class_decl_dict;
            frame_frame_rethinkdb$ql2_pb2->m_frame.f_lineno = 76;
            tmp_assign_source_67 = CALL_FUNCTION(tstate, tmp_called_value_19, tmp_args_value_19, tmp_kwargs_value_19);
            Py_DECREF(tmp_args_value_19);
            if (tmp_assign_source_67 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 76;

                goto try_except_handler_51;
            }
            assert(outline_16_var___class__ == NULL);
            outline_16_var___class__ = tmp_assign_source_67;
        }
        CHECK_OBJECT(outline_16_var___class__);
        tmp_assign_source_60 = outline_16_var___class__;
        Py_INCREF(tmp_assign_source_60);
        goto try_return_handler_51;
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_51:;
        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__17_Term_76);
        locals_rethinkdb$ql2_pb2$$$class__17_Term_76 = NULL;
        goto try_return_handler_50;
        // Exception handler code:
        try_except_handler_51:;
        exception_keeper_lineno_55 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_55 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        Py_DECREF(locals_rethinkdb$ql2_pb2$$$class__17_Term_76);
        locals_rethinkdb$ql2_pb2$$$class__17_Term_76 = NULL;
        // Re-raise.
        exception_state = exception_keeper_name_55;
        exception_lineno = exception_keeper_lineno_55;

        goto try_except_handler_50;
        // End of try:
        NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
        return NULL;
        // Return handler code:
        try_return_handler_50:;
        CHECK_OBJECT(outline_16_var___class__);
        Py_DECREF(outline_16_var___class__);
        outline_16_var___class__ = NULL;
        goto outline_result_17;
        // Exception handler code:
        try_except_handler_50:;
        exception_keeper_lineno_56 = exception_lineno;
        exception_lineno = 0;
        exception_keeper_name_56 = exception_state;
        INIT_ERROR_OCCURRED_STATE(&exception_state);

        // Re-raise.
        exception_state = exception_keeper_name_56;
        exception_lineno = exception_keeper_lineno_56;

        goto outline_exception_17;
        // End of try:
        NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
        return NULL;
        outline_exception_17:;
        exception_lineno = 76;
        goto try_except_handler_49;
        outline_result_17:;
        UPDATE_STRING_DICT1(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)mod_consts[98], tmp_assign_source_60);
    }
    goto try_end_19;
    // Exception handler code:
    try_except_handler_49:;
    exception_keeper_lineno_57 = exception_lineno;
    exception_lineno = 0;
    exception_keeper_name_57 = exception_state;
    INIT_ERROR_OCCURRED_STATE(&exception_state);

    CHECK_OBJECT(tmp_class_creation_7__class_decl_dict);
    Py_DECREF(tmp_class_creation_7__class_decl_dict);
    tmp_class_creation_7__class_decl_dict = NULL;
    CHECK_OBJECT(tmp_class_creation_7__prepared);
    Py_DECREF(tmp_class_creation_7__prepared);
    tmp_class_creation_7__prepared = NULL;
    // Re-raise.
    exception_state = exception_keeper_name_57;
    exception_lineno = exception_keeper_lineno_57;

    goto frame_exception_exit_1;
    // End of try:
    try_end_19:;


    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto frame_no_exception_7;
    frame_exception_exit_1:


    {
        PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
        if (exception_tb == NULL) {
            exception_tb = MAKE_TRACEBACK(frame_frame_rethinkdb$ql2_pb2, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        } else if (exception_tb->tb_frame != &frame_frame_rethinkdb$ql2_pb2->m_frame) {
            exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_rethinkdb$ql2_pb2, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        }
    }



    assertFrameObject(frame_frame_rethinkdb$ql2_pb2);

    // Put the previous frame back on top.
    popFrameStack(tstate);

    // Return the error.
    goto module_exception_exit;
    frame_no_exception_7:;
    CHECK_OBJECT(tmp_class_creation_7__class_decl_dict);
    Py_DECREF(tmp_class_creation_7__class_decl_dict);
    tmp_class_creation_7__class_decl_dict = NULL;
    CHECK_OBJECT(tmp_class_creation_7__prepared);
    Py_DECREF(tmp_class_creation_7__prepared);
    tmp_class_creation_7__prepared = NULL;

    // Report to PGO about leaving the module without error.
    PGO_onModuleExit("rethinkdb$ql2_pb2", false);

#if _NUITKA_MODULE_MODE && 0
    {
        PyObject *post_load = IMPORT_EMBEDDED_MODULE(tstate, "rethinkdb.ql2_pb2" "-postLoad");
        if (post_load == NULL) {
            return NULL;
        }
    }
#endif

    Py_INCREF(module_rethinkdb$ql2_pb2);
    return module_rethinkdb$ql2_pb2;
    module_exception_exit:

#if _NUITKA_MODULE_MODE && 0
    {
        PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_rethinkdb$ql2_pb2, (Nuitka_StringObject *)const_str_plain___name__);

        if (module_name != NULL) {
            Nuitka_DelModule(tstate, module_name);
        }
    }
#endif
    PGO_onModuleExit("rethinkdb$ql2_pb2", false);

    RESTORE_ERROR_OCCURRED_STATE(tstate, &exception_state);
    return NULL;
}
