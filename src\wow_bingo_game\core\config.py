"""
WOW Bingo Game - Configuration Management
========================================

Centralized configuration management using Pydantic for validation
and type safety. Supports environment variables, config files, and
runtime configuration updates.

Features:
- Type-safe configuration with validation
- Environment variable support
- JSON/YAML config file support
- Hot-reload capabilities
- Default value management
- Path management
"""

import os
from pathlib import Path
from typing import Dict, List, Optional, Union
from enum import Enum

from pydantic import BaseModel, Field, validator
from pydantic_settings import BaseSettings

from .. import __version__


class ThemeMode(str, Enum):
    """UI theme modes."""
    LIGHT = "light"
    DARK = "dark"
    MIDNIGHT = "midnight"
    OCEAN = "ocean"


class AccentColor(str, Enum):
    """UI accent colors."""
    BLUE = "blue"
    TEAL = "teal"
    PURPLE = "purple"
    ORANGE = "orange"
    GREEN = "green"
    RED = "red"
    GOLD = "gold"


class AnnouncerLanguage(str, Enum):
    """Announcer language options."""
    DEFAULT = "Default"
    ENGLISH = "English"
    AMHARIC = "Amharic"


class PathConfig(BaseModel):
    """Path configuration for the application."""
    
    # Base directories
    app_dir: Path = Field(default_factory=lambda: Path.cwd())
    assets_dir: Path = Field(default_factory=lambda: Path("assets"))
    data_dir: Path = Field(default_factory=lambda: Path("data"))
    logs_dir: Path = Field(default_factory=lambda: Path("logs"))
    cache_dir: Path = Field(default_factory=lambda: Path("cache"))
    backup_dir: Path = Field(default_factory=lambda: Path("backup"))
    
    # Asset subdirectories
    splash_images_dir: Path = Field(default_factory=lambda: Path("assets/Splash_screen"))
    splash_music_dir: Path = Field(default_factory=lambda: Path("assets/splash_screen-background-music"))
    audio_effects_dir: Path = Field(default_factory=lambda: Path("assets/audio-effects"))
    cartella_announcer_dir: Path = Field(default_factory=lambda: Path("assets/cartella-announcer"))
    icons_dir: Path = Field(default_factory=lambda: Path("assets"))
    
    # Data files
    bingo_boards_file: Path = Field(default_factory=lambda: Path("data/bingo_boards.json"))
    players_file: Path = Field(default_factory=lambda: Path("data/players.json"))
    settings_file: Path = Field(default_factory=lambda: Path("data/settings.json"))
    stats_db_file: Path = Field(default_factory=lambda: Path("data/stats.db"))
    vouchers_db_file: Path = Field(default_factory=lambda: Path("data/vouchers.db"))
    
    def create_directories(self) -> None:
        """Create all necessary directories."""
        directories = [
            self.data_dir,
            self.logs_dir,
            self.cache_dir,
            self.backup_dir,
            self.assets_dir,
            self.splash_images_dir,
            self.splash_music_dir,
            self.audio_effects_dir,
            self.cartella_announcer_dir,
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)


class GameConfig(BaseModel):
    """Game-specific configuration."""
    
    # Game mechanics
    number_call_delay: float = Field(default=3.0, ge=0.5, le=10.0)
    strict_claim_timing: bool = Field(default=True)
    shuffle_duration: float = Field(default=3.0, ge=1.0, le=10.0)
    commission_percentage: float = Field(default=20.0, ge=0.0, le=50.0)
    
    # UI settings
    show_total_selected: bool = Field(default=True)
    remember_cartella_numbers: bool = Field(default=False)
    show_game_info: bool = Field(default=True)
    show_recent_calls: bool = Field(default=True)
    recent_calls_count: int = Field(default=5, ge=1, le=20)
    
    # Default values
    default_bet_amount: float = Field(default=50.0, ge=1.0)
    default_prize_pool: float = Field(default=500.0, ge=1.0)
    max_cartella_number: int = Field(default=100, ge=1)


class DisplayConfig(BaseModel):
    """Display and UI configuration."""
    
    # Window settings
    fullscreen: bool = Field(default=False)
    window_width: int = Field(default=1280, ge=800)
    window_height: int = Field(default=720, ge=600)
    min_width: int = Field(default=1024, ge=800)
    min_height: int = Field(default=768, ge=600)
    
    # Theme settings
    theme_mode: ThemeMode = Field(default=ThemeMode.DARK)
    accent_color: AccentColor = Field(default=AccentColor.BLUE)
    animations_enabled: bool = Field(default=True)
    
    # Animation settings
    transition_animation_enabled: bool = Field(default=True)
    transition_animation_duration: int = Field(default=13, ge=1, le=30)


class AudioConfig(BaseModel):
    """Audio configuration."""
    
    # General audio
    sound_effects_enabled: bool = Field(default=True)
    sound_effects_volume: float = Field(default=0.7, ge=0.0, le=1.0)
    music_enabled: bool = Field(default=True)
    music_volume: float = Field(default=0.5, ge=0.0, le=1.0)
    
    # Voice announcements
    voice_enabled: bool = Field(default=True)
    voice_volume: float = Field(default=0.8, ge=0.0, le=1.0)
    cartella_announcements_enabled: bool = Field(default=True)
    announcer_language: AnnouncerLanguage = Field(default=AnnouncerLanguage.DEFAULT)


class DatabaseConfig(BaseModel):
    """Database configuration."""
    
    # SQLite settings
    sqlite_file: str = Field(default="data/game.db")
    sqlite_timeout: float = Field(default=30.0, ge=1.0)
    sqlite_check_same_thread: bool = Field(default=False)
    
    # RethinkDB settings (optional)
    rethinkdb_enabled: bool = Field(default=False)
    rethinkdb_host: str = Field(default="localhost")
    rethinkdb_port: int = Field(default=28015, ge=1, le=65535)
    rethinkdb_database: str = Field(default="wow_bingo")
    rethinkdb_timeout: float = Field(default=30.0, ge=1.0)
    
    # Connection pooling
    max_connections: int = Field(default=10, ge=1, le=100)
    connection_timeout: float = Field(default=30.0, ge=1.0)


class PerformanceConfig(BaseModel):
    """Performance and optimization configuration."""
    
    # Caching
    enable_caching: bool = Field(default=True)
    cache_size_mb: int = Field(default=100, ge=10, le=1000)
    cache_ttl_seconds: int = Field(default=3600, ge=60)
    
    # Rendering
    max_fps: int = Field(default=60, ge=30, le=120)
    vsync_enabled: bool = Field(default=True)
    hardware_acceleration: bool = Field(default=True)
    
    # Memory management
    gc_threshold: int = Field(default=1000, ge=100)
    memory_limit_mb: int = Field(default=512, ge=128)


class SecurityConfig(BaseModel):
    """Security and encryption configuration."""
    
    # Voucher system
    voucher_encryption_enabled: bool = Field(default=True)
    voucher_key_rotation_days: int = Field(default=30, ge=1)
    
    # Data protection
    encrypt_sensitive_data: bool = Field(default=True)
    backup_encryption_enabled: bool = Field(default=True)
    
    # Access control
    admin_mode_enabled: bool = Field(default=False)
    developer_mode_enabled: bool = Field(default=False)


class WOWBingoConfig(BaseSettings):
    """Main application configuration."""
    
    # Application metadata
    app_name: str = Field(default="WOW Bingo Game")
    version: str = Field(default=__version__)
    author: str = Field(default="WOW Games Team")
    
    # Configuration sections
    paths: PathConfig = Field(default_factory=PathConfig)
    game: GameConfig = Field(default_factory=GameConfig)
    display: DisplayConfig = Field(default_factory=DisplayConfig)
    audio: AudioConfig = Field(default_factory=AudioConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    
    # Development settings
    debug_mode: bool = Field(default=False)
    log_level: str = Field(default="INFO")
    
    class Config:
        env_prefix = "WOW_BINGO_"
        env_nested_delimiter = "__"
        case_sensitive = False
        
    @validator('log_level')
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()
    
    def save_to_file(self, file_path: Optional[Path] = None) -> None:
        """Save configuration to JSON file."""
        if file_path is None:
            file_path = self.paths.settings_file
            
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(self.json(indent=2))
    
    @classmethod
    def load_from_file(cls, file_path: Path) -> 'WOWBingoConfig':
        """Load configuration from JSON file."""
        if not file_path.exists():
            return cls()
            
        with open(file_path, 'r', encoding='utf-8') as f:
            data = f.read()
            
        return cls.parse_raw(data)


# Global configuration instance
_config_instance: Optional[WOWBingoConfig] = None


def get_config() -> WOWBingoConfig:
    """Get the global configuration instance."""
    global _config_instance
    if _config_instance is None:
        _config_instance = WOWBingoConfig()
    return _config_instance


def reload_config() -> WOWBingoConfig:
    """Reload configuration from file."""
    global _config_instance
    config_file = Path("data/settings.json")
    _config_instance = WOWBingoConfig.load_from_file(config_file)
    return _config_instance


# Convenience alias
GameConfig = get_config
