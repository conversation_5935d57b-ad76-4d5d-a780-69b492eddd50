2025-05-27 12:06:49,920 - INFO - Loaded 7 items from persistent cache
2025-05-27 12:06:49,920 - INFO - Stats cache initialized
2025-05-27 12:06:49,920 - INFO - Database security initialized successfully
2025-05-27 12:06:49,920 - INFO - DB Operation: {"timestamp": "2025-05-27 12:06:49", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:06:49,920 - INFO - Created secure database connection
2025-05-27 12:06:49,928 - INFO - Database schema initialized successfully
2025-05-27 12:06:49,928 - INFO - DB Operation: {"timestamp": "2025-05-27 12:06:49", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:06:49,928 - INFO - DB Operation: {"timestamp": "2025-05-27 12:06:49", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:06:49,934 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-05-27 12:06:49,934 - INFO - Stats preloader initialized
2025-05-27 12:06:49,936 - INFO - Starting stats data preloading
2025-05-27 12:06:49,936 - INFO - Started stats data preloading
2025-05-27 12:06:49,936 - INFO - Loading data using optimized functions
2025-05-27 12:06:49,936 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:06:49,936 - INFO - DB Operation: {"timestamp": "2025-05-27 12:06:49", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:06:49,936 - INFO - Preloaded weekly stats for 7 days
2025-05-27 12:06:49,936 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:06:49,945 - INFO - DB Operation: {"timestamp": "2025-05-27 12:06:49", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:06:49,945 - INFO - Preloaded summary data
2025-05-27 12:06:49,945 - INFO - Preloaded game history (0 records)
2025-05-27 12:06:49,945 - INFO - Preloaded wallet data
2025-05-27 12:06:49,945 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-05-27 12:06:49,945 - INFO - Saved 9 items to persistent cache
2025-05-27 12:06:49,945 - INFO - Loaded 9 items from cache
2025-05-27 12:06:49,953 - INFO - Started background data loading
2025-05-27 12:06:49,953 - INFO - OptimizedStatsLoader initialized
2025-05-27 12:06:49,953 - INFO - Using optimized stats loader for integration
2025-05-27 12:06:49,953 - INFO - Loaded weekly stats for 7 days
2025-05-27 12:06:49,953 - INFO - Loaded summary data
2025-05-27 12:06:49,953 - INFO - Loaded game history page 0 (0 records)
2025-05-27 12:06:49,953 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 12:06:49,953 - INFO - Saved 9 items to cache
2025-05-27 12:06:49,953 - INFO - Background data loading completed
2025-05-27 12:06:49,953 - INFO - Database schema initialized successfully
2025-05-27 12:06:49,969 - INFO - Database schema initialized successfully
2025-05-27 12:06:49,969 - INFO - Stats database initialized successfully
2025-05-27 12:06:49,969 - INFO - Game stats integration module available
2025-05-27 12:06:49,969 - INFO - Started stats event worker thread
2025-05-27 12:06:49,969 - INFO - Stats event hooks initialized
2025-05-27 12:06:49,993 - INFO - Stats performance monitor initialized
2025-05-27 12:06:50,244 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-05-27 12:07:27,562 - INFO - Loaded 9 items from persistent cache
2025-05-27 12:07:27,562 - INFO - Stats cache initialized
2025-05-27 12:07:27,562 - INFO - Database security initialized successfully
2025-05-27 12:07:27,562 - INFO - DB Operation: {"timestamp": "2025-05-27 12:07:27", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:07:27,562 - INFO - Created secure database connection
2025-05-27 12:07:27,562 - INFO - Database schema initialized successfully
2025-05-27 12:07:27,562 - INFO - DB Operation: {"timestamp": "2025-05-27 12:07:27", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:07:27,562 - INFO - DB Operation: {"timestamp": "2025-05-27 12:07:27", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:07:27,562 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-05-27 12:07:27,562 - INFO - Stats preloader initialized
2025-05-27 12:07:27,570 - INFO - Starting stats data preloading
2025-05-27 12:07:27,570 - INFO - Started stats data preloading
2025-05-27 12:07:27,570 - INFO - Loading data using optimized functions
2025-05-27 12:07:27,570 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:07:27,570 - INFO - DB Operation: {"timestamp": "2025-05-27 12:07:27", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:07:27,570 - INFO - Preloaded weekly stats for 7 days
2025-05-27 12:07:27,570 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:07:27,578 - INFO - DB Operation: {"timestamp": "2025-05-27 12:07:27", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:07:27,578 - INFO - Loaded 9 items from cache
2025-05-27 12:07:27,578 - INFO - Started background data loading
2025-05-27 12:07:27,578 - INFO - OptimizedStatsLoader initialized
2025-05-27 12:07:27,578 - INFO - Using optimized stats loader for integration
2025-05-27 12:07:27,578 - INFO - Preloaded summary data
2025-05-27 12:07:27,578 - INFO - Loaded weekly stats for 7 days
2025-05-27 12:07:27,578 - INFO - Preloaded game history (0 records)
2025-05-27 12:07:27,578 - INFO - Loaded summary data
2025-05-27 12:07:27,578 - INFO - Preloaded wallet data
2025-05-27 12:07:27,578 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-05-27 12:07:27,578 - INFO - Loaded game history page 0 (0 records)
2025-05-27 12:07:27,578 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 12:07:27,578 - INFO - Saved 9 items to persistent cache
2025-05-27 12:07:27,578 - INFO - Saved 9 items to cache
2025-05-27 12:07:27,578 - INFO - Background data loading completed
2025-05-27 12:07:27,586 - INFO - Database schema initialized successfully
2025-05-27 12:07:27,586 - INFO - Database schema initialized successfully
2025-05-27 12:07:27,586 - INFO - Stats database initialized successfully
2025-05-27 12:07:27,586 - INFO - Game stats integration module available
2025-05-27 12:07:27,586 - INFO - Started stats event worker thread
2025-05-27 12:07:27,586 - INFO - Stats event hooks initialized
2025-05-27 12:07:27,594 - INFO - Stats performance monitor initialized
2025-05-27 12:07:27,690 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-05-27 12:07:28,700 - INFO - load_statistics: 0.1838s
2025-05-27 12:07:28,700 - INFO - Starting stats data preloading
2025-05-27 12:07:28,700 - INFO - Started stats data preloading
2025-05-27 12:07:28,700 - INFO - Loading data using optimized functions
2025-05-27 12:07:28,701 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:07:28,702 - INFO - DB Operation: {"timestamp": "2025-05-27 12:07:28", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:07:28,703 - INFO - Preloaded weekly stats for 7 days
2025-05-27 12:07:28,703 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:07:28,704 - INFO - DB Operation: {"timestamp": "2025-05-27 12:07:28", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:07:28,704 - INFO - Preloaded summary data
2025-05-27 12:07:28,705 - INFO - Preloaded game history (0 records)
2025-05-27 12:07:28,705 - INFO - Preloaded wallet data
2025-05-27 12:07:28,705 - INFO - Stats data preloaded successfully in 0.00 seconds
2025-05-27 12:07:28,706 - INFO - Saved 9 items to persistent cache
2025-05-27 12:12:52,948 - INFO - Loaded 7 items from persistent cache
2025-05-27 12:12:52,948 - INFO - Stats cache initialized
2025-05-27 12:12:52,948 - INFO - Database security initialized successfully
2025-05-27 12:12:52,956 - INFO - DB Operation: {"timestamp": "2025-05-27 12:12:52", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:12:52,956 - INFO - Created secure database connection
2025-05-27 12:12:52,956 - INFO - Database schema initialized successfully
2025-05-27 12:12:52,956 - INFO - DB Operation: {"timestamp": "2025-05-27 12:12:52", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:12:52,956 - INFO - DB Operation: {"timestamp": "2025-05-27 12:12:52", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:12:52,956 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-05-27 12:12:52,956 - INFO - Stats preloader initialized
2025-05-27 12:12:52,962 - INFO - Starting stats data preloading
2025-05-27 12:12:52,962 - INFO - Started stats data preloading
2025-05-27 12:12:52,962 - INFO - Loading data using optimized functions
2025-05-27 12:12:52,964 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:12:52,964 - INFO - DB Operation: {"timestamp": "2025-05-27 12:12:52", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:12:52,972 - INFO - Preloaded weekly stats for 7 days
2025-05-27 12:12:52,972 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:12:52,972 - INFO - DB Operation: {"timestamp": "2025-05-27 12:12:52", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:12:52,972 - INFO - Loaded 7 items from cache
2025-05-27 12:12:52,972 - INFO - Started background data loading
2025-05-27 12:12:52,972 - INFO - OptimizedStatsLoader initialized
2025-05-27 12:12:52,972 - INFO - Using optimized stats loader for integration
2025-05-27 12:12:52,972 - INFO - Preloaded summary data
2025-05-27 12:12:52,980 - INFO - Loaded weekly stats for 7 days
2025-05-27 12:12:52,981 - INFO - Preloaded game history (0 records)
2025-05-27 12:12:52,982 - INFO - Preloaded wallet data
2025-05-27 12:12:52,982 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-05-27 12:12:52,982 - INFO - Loaded summary data
2025-05-27 12:12:52,982 - INFO - Loaded game history page 0 (0 records)
2025-05-27 12:12:52,982 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 12:12:52,982 - INFO - Saved 9 items to persistent cache
2025-05-27 12:12:52,988 - INFO - Saved 7 items to cache
2025-05-27 12:12:52,988 - INFO - Background data loading completed
2025-05-27 12:12:52,988 - INFO - Database schema initialized successfully
2025-05-27 12:12:52,988 - INFO - Database schema initialized successfully
2025-05-27 12:12:52,988 - INFO - Stats database initialized successfully
2025-05-27 12:12:52,996 - INFO - Game stats integration module available
2025-05-27 12:12:52,996 - INFO - Started stats event worker thread
2025-05-27 12:12:52,996 - INFO - Stats event hooks initialized
2025-05-27 12:12:53,004 - INFO - Stats performance monitor initialized
2025-05-27 12:12:53,148 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-05-27 12:22:01,784 - INFO - Loaded 7 items from persistent cache
2025-05-27 12:22:01,784 - INFO - Stats cache initialized
2025-05-27 12:22:01,784 - INFO - Database security initialized successfully
2025-05-27 12:22:01,784 - INFO - DB Operation: {"timestamp": "2025-05-27 12:22:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:22:01,784 - INFO - Created secure database connection
2025-05-27 12:22:01,790 - INFO - Database schema initialized successfully
2025-05-27 12:22:01,790 - INFO - DB Operation: {"timestamp": "2025-05-27 12:22:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:22:01,792 - INFO - DB Operation: {"timestamp": "2025-05-27 12:22:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:22:01,792 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-05-27 12:22:01,792 - INFO - Stats preloader initialized
2025-05-27 12:22:01,801 - INFO - Starting stats data preloading
2025-05-27 12:22:01,802 - INFO - Started stats data preloading
2025-05-27 12:22:01,802 - INFO - Loading data using optimized functions
2025-05-27 12:22:01,802 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:22:01,802 - INFO - DB Operation: {"timestamp": "2025-05-27 12:22:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:22:01,802 - INFO - Preloaded weekly stats for 7 days
2025-05-27 12:22:01,810 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:22:01,810 - INFO - DB Operation: {"timestamp": "2025-05-27 12:22:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:22:01,814 - INFO - Preloaded summary data
2025-05-27 12:22:01,814 - INFO - Preloaded game history (0 records)
2025-05-27 12:22:01,814 - INFO - Loaded 7 items from cache
2025-05-27 12:22:01,814 - INFO - Preloaded wallet data
2025-05-27 12:22:01,814 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-05-27 12:22:01,814 - INFO - Started background data loading
2025-05-27 12:22:01,814 - INFO - OptimizedStatsLoader initialized
2025-05-27 12:22:01,819 - INFO - Using optimized stats loader for integration
2025-05-27 12:22:01,819 - INFO - Loaded weekly stats for 7 days
2025-05-27 12:22:01,819 - INFO - Loaded summary data
2025-05-27 12:22:01,819 - INFO - Saved 9 items to persistent cache
2025-05-27 12:22:01,819 - INFO - Loaded game history page 0 (0 records)
2025-05-27 12:22:01,819 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 12:22:01,819 - INFO - Saved 7 items to cache
2025-05-27 12:22:01,819 - INFO - Background data loading completed
2025-05-27 12:22:01,827 - INFO - Database schema initialized successfully
2025-05-27 12:22:01,835 - INFO - Database schema initialized successfully
2025-05-27 12:22:01,835 - INFO - Stats database initialized successfully
2025-05-27 12:22:01,843 - INFO - Game stats integration module available
2025-05-27 12:22:01,843 - INFO - Started stats event worker thread
2025-05-27 12:22:01,843 - INFO - Stats event hooks initialized
2025-05-27 12:22:01,859 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-05-27 12:22:01,859 - INFO - Stats performance monitor initialized
2025-05-27 12:22:01,996 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-05-27 12:22:31,982 - INFO - Loaded 7 items from persistent cache
2025-05-27 12:22:31,990 - INFO - Stats cache initialized
2025-05-27 12:22:31,990 - INFO - Database security initialized successfully
2025-05-27 12:22:31,990 - INFO - DB Operation: {"timestamp": "2025-05-27 12:22:31", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:22:31,990 - INFO - Created secure database connection
2025-05-27 12:22:31,990 - INFO - Database schema initialized successfully
2025-05-27 12:22:31,990 - INFO - DB Operation: {"timestamp": "2025-05-27 12:22:31", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:22:31,990 - INFO - DB Operation: {"timestamp": "2025-05-27 12:22:31", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:22:31,990 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-05-27 12:22:31,990 - INFO - Stats preloader initialized
2025-05-27 12:22:31,998 - INFO - Starting stats data preloading
2025-05-27 12:22:31,998 - INFO - Started stats data preloading
2025-05-27 12:22:32,000 - INFO - Loading data using optimized functions
2025-05-27 12:22:32,000 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:22:32,000 - INFO - DB Operation: {"timestamp": "2025-05-27 12:22:32", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:22:32,006 - INFO - Preloaded weekly stats for 7 days
2025-05-27 12:22:32,006 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:22:32,006 - INFO - DB Operation: {"timestamp": "2025-05-27 12:22:32", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:22:32,006 - INFO - Loaded 7 items from cache
2025-05-27 12:22:32,006 - INFO - Preloaded summary data
2025-05-27 12:22:32,006 - INFO - Started background data loading
2025-05-27 12:22:32,006 - INFO - OptimizedStatsLoader initialized
2025-05-27 12:22:32,006 - INFO - Using optimized stats loader for integration
2025-05-27 12:22:32,006 - INFO - Preloaded game history (0 records)
2025-05-27 12:22:32,006 - INFO - Loaded weekly stats for 7 days
2025-05-27 12:22:32,006 - INFO - Preloaded wallet data
2025-05-27 12:22:32,006 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-05-27 12:22:32,006 - INFO - Loaded summary data
2025-05-27 12:22:32,006 - INFO - Saved 9 items to persistent cache
2025-05-27 12:22:32,014 - INFO - Loaded game history page 0 (0 records)
2025-05-27 12:22:32,014 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 12:22:32,014 - INFO - Saved 7 items to cache
2025-05-27 12:22:32,014 - INFO - Background data loading completed
2025-05-27 12:22:32,014 - INFO - Database schema initialized successfully
2025-05-27 12:22:32,022 - INFO - Database schema initialized successfully
2025-05-27 12:22:32,022 - INFO - Stats database initialized successfully
2025-05-27 12:22:32,022 - INFO - Game stats integration module available
2025-05-27 12:22:32,022 - INFO - Started stats event worker thread
2025-05-27 12:22:32,022 - INFO - Stats event hooks initialized
2025-05-27 12:22:32,030 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-05-27 12:22:32,030 - INFO - Stats performance monitor initialized
2025-05-27 12:22:32,207 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-05-27 12:26:07,955 - INFO - Loaded 7 items from persistent cache
2025-05-27 12:26:07,955 - INFO - Stats cache initialized
2025-05-27 12:26:07,955 - INFO - Database security initialized successfully
2025-05-27 12:26:07,963 - INFO - DB Operation: {"timestamp": "2025-05-27 12:26:07", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:26:07,963 - INFO - Created secure database connection
2025-05-27 12:26:07,963 - INFO - Database schema initialized successfully
2025-05-27 12:26:07,963 - INFO - DB Operation: {"timestamp": "2025-05-27 12:26:07", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:26:07,963 - INFO - DB Operation: {"timestamp": "2025-05-27 12:26:07", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:26:07,963 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-05-27 12:26:07,963 - INFO - Stats preloader initialized
2025-05-27 12:26:07,963 - INFO - Starting stats data preloading
2025-05-27 12:26:07,963 - INFO - Started stats data preloading
2025-05-27 12:26:07,963 - INFO - Loading data using optimized functions
2025-05-27 12:26:07,963 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:26:07,971 - INFO - DB Operation: {"timestamp": "2025-05-27 12:26:07", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:26:07,979 - INFO - Preloaded weekly stats for 7 days
2025-05-27 12:26:07,979 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:26:07,979 - INFO - DB Operation: {"timestamp": "2025-05-27 12:26:07", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:26:07,979 - INFO - Loaded 7 items from cache
2025-05-27 12:26:07,979 - INFO - Preloaded summary data
2025-05-27 12:26:07,979 - INFO - Preloaded game history (0 records)
2025-05-27 12:26:07,979 - INFO - Preloaded wallet data
2025-05-27 12:26:07,979 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-05-27 12:26:07,987 - INFO - Saved 9 items to persistent cache
2025-05-27 12:26:07,987 - INFO - Started background data loading
2025-05-27 12:26:07,987 - INFO - OptimizedStatsLoader initialized
2025-05-27 12:26:07,987 - INFO - Using optimized stats loader for integration
2025-05-27 12:26:07,987 - INFO - Loaded weekly stats for 7 days
2025-05-27 12:26:07,995 - INFO - Loaded summary data
2025-05-27 12:26:07,995 - INFO - Loaded game history page 0 (0 records)
2025-05-27 12:26:07,995 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 12:26:07,995 - INFO - Saved 7 items to cache
2025-05-27 12:26:07,995 - INFO - Background data loading completed
2025-05-27 12:26:08,008 - INFO - Database schema initialized successfully
2025-05-27 12:26:08,019 - INFO - Database schema initialized successfully
2025-05-27 12:26:08,019 - INFO - Stats database initialized successfully
2025-05-27 12:26:08,019 - INFO - Game stats integration module available
2025-05-27 12:26:08,019 - INFO - Started stats event worker thread
2025-05-27 12:26:08,019 - INFO - Stats event hooks initialized
2025-05-27 12:26:08,035 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-05-27 12:26:08,035 - INFO - Stats performance monitor initialized
2025-05-27 12:26:08,216 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-05-27 12:27:11,774 - INFO - Loaded 7 items from persistent cache
2025-05-27 12:27:11,774 - INFO - Stats cache initialized
2025-05-27 12:27:11,774 - INFO - Database security initialized successfully
2025-05-27 12:27:11,774 - INFO - DB Operation: {"timestamp": "2025-05-27 12:27:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:27:11,774 - INFO - Created secure database connection
2025-05-27 12:27:11,782 - INFO - Database schema initialized successfully
2025-05-27 12:27:11,782 - INFO - DB Operation: {"timestamp": "2025-05-27 12:27:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:27:11,782 - INFO - DB Operation: {"timestamp": "2025-05-27 12:27:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:27:11,782 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-05-27 12:27:11,782 - INFO - Stats preloader initialized
2025-05-27 12:27:11,782 - INFO - Starting stats data preloading
2025-05-27 12:27:11,782 - INFO - Started stats data preloading
2025-05-27 12:27:11,782 - INFO - Loading data using optimized functions
2025-05-27 12:27:11,782 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:27:11,782 - INFO - DB Operation: {"timestamp": "2025-05-27 12:27:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:27:11,790 - INFO - Preloaded weekly stats for 7 days
2025-05-27 12:27:11,790 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:27:11,790 - INFO - DB Operation: {"timestamp": "2025-05-27 12:27:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:27:11,798 - INFO - Preloaded summary data
2025-05-27 12:27:11,798 - INFO - Loaded 7 items from cache
2025-05-27 12:27:11,798 - INFO - Preloaded game history (0 records)
2025-05-27 12:27:11,798 - INFO - Started background data loading
2025-05-27 12:27:11,798 - INFO - OptimizedStatsLoader initialized
2025-05-27 12:27:11,798 - INFO - Using optimized stats loader for integration
2025-05-27 12:27:11,806 - INFO - Preloaded wallet data
2025-05-27 12:27:11,806 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-05-27 12:27:11,806 - INFO - Saved 9 items to persistent cache
2025-05-27 12:27:11,806 - INFO - Loaded weekly stats for 7 days
2025-05-27 12:27:11,806 - INFO - Loaded summary data
2025-05-27 12:27:11,806 - INFO - Loaded game history page 0 (0 records)
2025-05-27 12:27:11,806 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 12:27:11,806 - INFO - Saved 7 items to cache
2025-05-27 12:27:11,806 - INFO - Background data loading completed
2025-05-27 12:27:11,822 - INFO - Database schema initialized successfully
2025-05-27 12:27:11,830 - INFO - Database schema initialized successfully
2025-05-27 12:27:11,830 - INFO - Stats database initialized successfully
2025-05-27 12:27:11,830 - INFO - Game stats integration module available
2025-05-27 12:27:11,830 - INFO - Started stats event worker thread
2025-05-27 12:27:11,830 - INFO - Stats event hooks initialized
2025-05-27 12:27:11,847 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-05-27 12:27:11,847 - INFO - Stats performance monitor initialized
2025-05-27 12:27:12,023 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-05-27 12:27:43,089 - INFO - Loaded 7 items from persistent cache
2025-05-27 12:27:43,089 - INFO - Stats cache initialized
2025-05-27 12:27:43,090 - INFO - Database security initialized successfully
2025-05-27 12:27:43,091 - INFO - DB Operation: {"timestamp": "2025-05-27 12:27:43", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:27:43,091 - INFO - Created secure database connection
2025-05-27 12:27:43,093 - INFO - Database schema initialized successfully
2025-05-27 12:27:43,094 - INFO - DB Operation: {"timestamp": "2025-05-27 12:27:43", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:27:43,095 - INFO - DB Operation: {"timestamp": "2025-05-27 12:27:43", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:27:43,095 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-05-27 12:27:43,095 - INFO - Stats preloader initialized
2025-05-27 12:27:43,097 - INFO - Starting stats data preloading
2025-05-27 12:27:43,097 - INFO - Started stats data preloading
2025-05-27 12:27:43,097 - INFO - Loading data using optimized functions
2025-05-27 12:27:43,098 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:27:43,101 - INFO - DB Operation: {"timestamp": "2025-05-27 12:27:43", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:27:43,112 - INFO - Preloaded weekly stats for 7 days
2025-05-27 12:27:43,112 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:27:43,113 - INFO - DB Operation: {"timestamp": "2025-05-27 12:27:43", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:27:43,115 - INFO - Preloaded summary data
2025-05-27 12:27:43,117 - INFO - Loaded 7 items from cache
2025-05-27 12:27:43,118 - INFO - Preloaded game history (0 records)
2025-05-27 12:27:43,121 - INFO - Preloaded wallet data
2025-05-27 12:27:43,122 - INFO - Started background data loading
2025-05-27 12:27:43,122 - INFO - Stats data preloaded successfully in 0.03 seconds
2025-05-27 12:27:43,122 - INFO - OptimizedStatsLoader initialized
2025-05-27 12:27:43,123 - INFO - Using optimized stats loader for integration
2025-05-27 12:27:43,125 - INFO - Loaded weekly stats for 7 days
2025-05-27 12:27:43,125 - INFO - Saved 9 items to persistent cache
2025-05-27 12:27:43,126 - INFO - Loaded summary data
2025-05-27 12:27:43,126 - INFO - Loaded game history page 0 (0 records)
2025-05-27 12:27:43,126 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 12:27:43,128 - INFO - Saved 7 items to cache
2025-05-27 12:27:43,129 - INFO - Background data loading completed
2025-05-27 12:27:43,135 - INFO - Database schema initialized successfully
2025-05-27 12:27:43,142 - INFO - Database schema initialized successfully
2025-05-27 12:27:43,143 - INFO - Stats database initialized successfully
2025-05-27 12:27:43,143 - INFO - Game stats integration module available
2025-05-27 12:27:43,144 - INFO - Started stats event worker thread
2025-05-27 12:27:43,144 - INFO - Stats event hooks initialized
2025-05-27 12:27:43,158 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-05-27 12:27:43,159 - INFO - Stats performance monitor initialized
2025-05-27 12:27:43,376 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-05-27 12:28:20,066 - INFO - Loaded 7 items from persistent cache
2025-05-27 12:28:20,066 - INFO - Stats cache initialized
2025-05-27 12:28:20,068 - INFO - Database security initialized successfully
2025-05-27 12:28:20,069 - INFO - DB Operation: {"timestamp": "2025-05-27 12:28:20", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:28:20,069 - INFO - Created secure database connection
2025-05-27 12:28:20,073 - INFO - Database schema initialized successfully
2025-05-27 12:28:20,073 - INFO - DB Operation: {"timestamp": "2025-05-27 12:28:20", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:28:20,074 - INFO - DB Operation: {"timestamp": "2025-05-27 12:28:20", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:28:20,074 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-05-27 12:28:20,074 - INFO - Stats preloader initialized
2025-05-27 12:28:20,075 - INFO - Starting stats data preloading
2025-05-27 12:28:20,075 - INFO - Started stats data preloading
2025-05-27 12:28:20,075 - INFO - Loading data using optimized functions
2025-05-27 12:28:20,075 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:28:20,076 - INFO - DB Operation: {"timestamp": "2025-05-27 12:28:20", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:28:20,089 - INFO - Preloaded weekly stats for 7 days
2025-05-27 12:28:20,089 - WARNING - Connection validation failed, creating new connection
2025-05-27 12:28:20,089 - INFO - DB Operation: {"timestamp": "2025-05-27 12:28:20", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:28:20,092 - INFO - Preloaded summary data
2025-05-27 12:28:20,093 - INFO - Loaded 7 items from cache
2025-05-27 12:28:20,093 - INFO - Preloaded game history (0 records)
2025-05-27 12:28:20,094 - INFO - Started background data loading
2025-05-27 12:28:20,094 - INFO - OptimizedStatsLoader initialized
2025-05-27 12:28:20,094 - INFO - Using optimized stats loader for integration
2025-05-27 12:28:20,094 - INFO - Preloaded wallet data
2025-05-27 12:28:20,095 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-05-27 12:28:20,095 - INFO - Loaded weekly stats for 7 days
2025-05-27 12:28:20,099 - INFO - Saved 9 items to persistent cache
2025-05-27 12:28:20,101 - INFO - Loaded summary data
2025-05-27 12:28:20,103 - INFO - Loaded game history page 0 (0 records)
2025-05-27 12:28:20,103 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 12:28:20,104 - INFO - Saved 7 items to cache
2025-05-27 12:28:20,104 - INFO - Background data loading completed
2025-05-27 12:28:20,108 - INFO - Database schema initialized successfully
2025-05-27 12:28:20,120 - INFO - Database schema initialized successfully
2025-05-27 12:28:20,122 - INFO - Stats database initialized successfully
2025-05-27 12:28:20,123 - INFO - Game stats integration module available
2025-05-27 12:28:20,124 - INFO - Started stats event worker thread
2025-05-27 12:28:20,124 - INFO - Stats event hooks initialized
2025-05-27 12:28:20,137 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-05-27 12:28:20,137 - INFO - Stats performance monitor initialized
2025-05-27 12:28:20,388 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-05-27 13:30:44,487 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-05-27 13:30:44,487 - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 13:30:56,695 - INFO - 127.0.0.1 - - [27/May/2025 13:30:56] "GET / HTTP/1.1" 200 -
2025-05-27 13:31:04,007 - INFO - 127.0.0.1 - - [27/May/2025 13:31:04] "[33mGET /performance HTTP/1.1[0m" 404 -
2025-05-27 13:31:12,619 - INFO - 127.0.0.1 - - [27/May/2025 13:31:12] "GET /daily-stats HTTP/1.1" 200 -
2025-05-27 13:33:07,630 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-05-27 13:33:07,630 - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 13:33:20,940 - INFO - 127.0.0.1 - - [27/May/2025 13:33:20] "GET / HTTP/1.1" 200 -
2025-05-27 13:33:59,157 - INFO - 127.0.0.1 - - [27/May/2025 13:33:59] "GET / HTTP/1.1" 200 -
2025-05-27 13:34:20,171 - INFO - 127.0.0.1 - - [27/May/2025 13:34:20] "GET /game-history HTTP/1.1" 200 -
2025-05-27 13:34:27,731 - INFO - 127.0.0.1 - - [27/May/2025 13:34:27] "GET /wallet HTTP/1.1" 200 -
2025-05-27 13:39:41,223 - INFO - 127.0.0.1 - - [27/May/2025 13:39:41] "GET /wallet HTTP/1.1" 200 -
2025-05-27 13:39:47,467 - INFO - 127.0.0.1 - - [27/May/2025 13:39:47] "GET /game-history HTTP/1.1" 200 -
2025-05-27 13:39:55,585 - INFO - 127.0.0.1 - - [27/May/2025 13:39:55] "GET /game-history HTTP/1.1" 200 -
2025-05-27 13:40:04,457 - INFO - 127.0.0.1 - - [27/May/2025 13:40:04] "GET /daily-stats HTTP/1.1" 200 -
2025-05-27 13:40:11,950 - INFO - 127.0.0.1 - - [27/May/2025 13:40:11] "GET / HTTP/1.1" 200 -
2025-05-27 13:45:53,059 - INFO - 127.0.0.1 - - [27/May/2025 13:45:53] "GET / HTTP/1.1" 200 -
2025-05-27 13:46:07,480 - INFO - 127.0.0.1 - - [27/May/2025 13:46:07] "GET /game-history HTTP/1.1" 200 -
2025-05-27 13:46:16,557 - INFO - 127.0.0.1 - - [27/May/2025 13:46:16] "GET / HTTP/1.1" 200 -
2025-05-27 13:53:58,652 - INFO - Database security initialized successfully
2025-05-27 13:53:58,652 - INFO - DB Operation: {"timestamp": "2025-05-27 13:53:58", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:53:58,653 - INFO - Created secure database connection
2025-05-27 13:53:58,655 - INFO - Database schema initialized successfully
2025-05-27 13:54:00,702 - INFO - Connected to RethinkDB at localhost:28015
2025-05-27 13:54:00,710 - INFO - Sync thread started
2025-05-27 13:54:00,710 - INFO - Sync manager initialized (RethinkDB available: True)
2025-05-27 13:54:00,711 - WARNING - Remote database not connected, aborting sync for table daily_stats
2025-05-27 13:54:00,712 - WARNING - Remote database not connected, aborting sync for table game_history
2025-05-27 13:54:00,712 - WARNING - Remote database not connected, aborting sync for table wallet_transactions
2025-05-27 13:54:00,712 - WARNING - Remote database not connected, aborting sync for table admin_users
2025-05-27 13:54:00,712 - WARNING - Remote database not connected, aborting sync for table audit_log
2025-05-27 13:54:00,713 - INFO - Saved sync metadata: {'daily_stats': 1748379240.7115402, 'game_history': 1748379240.7115402, 'wallet_transactions': 1748379240.7115402, 'admin_users': 1748379240.7115402, 'audit_log': 1748379240.7115402}
2025-05-27 13:55:03,080 - INFO - Hybrid DB initialized in offline mode (RethinkDB not available)
2025-05-27 13:55:03,084 - INFO - Loaded 0 items from cache
2025-05-27 13:55:03,085 - INFO - Started background data loading
2025-05-27 13:55:03,085 - INFO - OptimizedStatsLoader initialized
2025-05-27 13:55:03,085 - INFO - Using optimized stats loader for integration
2025-05-27 13:55:03,087 - INFO - Loaded weekly stats for 7 days
2025-05-27 13:55:03,088 - INFO - Loaded summary data
2025-05-27 13:55:03,088 - INFO - Loaded game history page 0 (2 records)
2025-05-27 13:55:03,088 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 13:55:03,091 - INFO - Saved 7 items to cache
2025-05-27 13:55:03,091 - INFO - Background data loading completed
2025-05-27 13:55:03,093 - INFO - Database schema initialized successfully
2025-05-27 13:55:03,100 - INFO - Database schema initialized successfully
2025-05-27 13:55:03,102 - INFO - Stats database initialized successfully
2025-05-27 13:55:03,102 - INFO - Game stats integration module available
2025-05-27 13:55:03,104 - INFO - Started stats event worker thread
2025-05-27 13:55:03,104 - INFO - Stats event hooks initialized
2025-05-27 13:55:03,780 - INFO - Game start event recorded in database (players: 5)
2025-05-27 13:55:04,754 - INFO - Started background data loading
2025-05-27 13:55:04,754 - INFO - Forced data refresh
2025-05-27 13:55:04,754 - INFO - Game start recorded with optimized integration: True
2025-05-27 13:55:04,755 - INFO - Loaded weekly stats for 7 days
2025-05-27 13:55:04,756 - INFO - Loaded summary data
2025-05-27 13:55:04,756 - INFO - Loaded game history page 0 (2 records)
2025-05-27 13:55:04,756 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 13:55:04,758 - INFO - Saved 7 items to cache
2025-05-27 13:55:04,758 - INFO - Background data loading completed
2025-05-27 13:55:04,762 - INFO - Started background data loading
2025-05-27 13:55:04,763 - INFO - Forced data refresh
2025-05-27 13:55:04,763 - INFO - Forced refresh of optimized loader data
2025-05-27 13:55:04,764 - INFO - Loaded weekly stats for 7 days
2025-05-27 13:55:04,765 - INFO - Loaded summary data
2025-05-27 13:55:04,766 - INFO - Loaded game history page 0 (2 records)
2025-05-27 13:55:04,766 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 13:55:04,767 - INFO - Saved 7 items to cache
2025-05-27 13:55:04,768 - INFO - Background data loading completed
2025-05-27 13:55:04,768 - INFO - Forced refresh of thread_safe_db data
2025-05-27 13:55:04,770 - INFO - Loaded 7 items from persistent cache
2025-05-27 13:55:04,770 - INFO - Stats cache initialized
2025-05-27 13:55:04,770 - INFO - DB Operation: {"timestamp": "2025-05-27 13:55:04", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:55:04,771 - INFO - DB Operation: {"timestamp": "2025-05-27 13:55:04", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:55:04,771 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-05-27 13:55:04,771 - INFO - Stats preloader initialized
2025-05-27 13:55:04,771 - INFO - Starting stats data preloading
2025-05-27 13:55:04,771 - INFO - Started stats data preloading
2025-05-27 13:55:04,771 - INFO - Loading data using optimized functions
2025-05-27 13:55:04,772 - WARNING - Connection validation failed, creating new connection
2025-05-27 13:55:04,772 - INFO - DB Operation: {"timestamp": "2025-05-27 13:55:04", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:55:04,772 - INFO - Saved 0 items to persistent cache
2025-05-27 13:55:04,772 - INFO - Cache cleared
2025-05-27 13:55:04,773 - INFO - Cleared all preloader cache data
2025-05-27 13:55:04,777 - INFO - Preloaded weekly stats for 7 days
2025-05-27 13:55:04,777 - WARNING - Connection validation failed, creating new connection
2025-05-27 13:55:04,778 - INFO - DB Operation: {"timestamp": "2025-05-27 13:55:04", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:55:04,779 - INFO - Preloaded summary data
2025-05-27 13:55:04,779 - INFO - Preloaded game history (2 records)
2025-05-27 13:55:04,780 - INFO - Preloaded wallet data
2025-05-27 13:55:04,780 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-05-27 13:55:04,781 - INFO - Saved 8 items to persistent cache
2025-05-27 13:55:04,781 - INFO - Stopped stats data preloading
2025-05-27 13:55:04,782 - INFO - Starting stats data preloading
2025-05-27 13:55:04,782 - INFO - Started stats data preloading
2025-05-27 13:55:04,782 - INFO - Loading data using optimized functions
2025-05-27 13:55:04,782 - INFO - Cleared stats preloader cache
2025-05-27 13:55:04,782 - WARNING - Connection validation failed, creating new connection
2025-05-27 13:55:04,783 - INFO - DB Operation: {"timestamp": "2025-05-27 13:55:04", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:55:04,785 - INFO - Preloaded weekly stats for 7 days
2025-05-27 13:55:04,785 - WARNING - Connection validation failed, creating new connection
2025-05-27 13:55:04,786 - INFO - DB Operation: {"timestamp": "2025-05-27 13:55:04", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:55:04,788 - INFO - Preloaded summary data
2025-05-27 13:55:04,788 - INFO - Preloaded game history (2 records)
2025-05-27 13:55:04,789 - INFO - Preloaded wallet data
2025-05-27 13:55:04,789 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-05-27 13:55:05,401 - INFO - Saved 8 items to persistent cache
2025-05-27 13:55:05,438 - INFO - Got summary stats
2025-05-27 13:55:05,638 - INFO - Processed game_started event: True
2025-05-27 13:55:05,640 - INFO - Loaded game history page 0 from GameStatsIntegration
2025-05-27 13:55:05,646 - INFO - Loaded weekly stats from GameStatsIntegration
2025-05-27 13:55:05,747 - INFO - RECORDING GAME COMPLETED IN THREAD_SAFE_DB
2025-05-27 13:55:05,747 - INFO - Game data: {'winner_name': 'TestPlayer', 'winner_cartella': 42, 'claim_type': 'Full House', 'game_duration': 120, 'player_count': 5, 'prize_amount': 500, 'commission_percentage': 20, 'called_numbers': [1, 5, 12, 23, 34, 45, 56, 67, 78], 'is_demo_mode': False, 'timestamp': '2025-05-27T13:55:05.112417', 'stake': 50}
2025-05-27 13:55:06,034 - INFO - Game statistics recorded in database (ID: 135)
2025-05-27 13:55:06,034 - INFO - Game winner recorded via thread_safe_db.py - Winner: TestPlayer, Status: Won
2025-05-27 13:55:06,046 - INFO - Started background data loading
2025-05-27 13:55:06,046 - INFO - Forced data refresh
2025-05-27 13:55:06,046 - INFO - Forced refresh of optimized loader data
2025-05-27 13:55:06,047 - INFO - Loaded weekly stats for 7 days
2025-05-27 13:55:06,048 - INFO - Loaded summary data
2025-05-27 13:55:06,048 - INFO - Loaded game history page 0 (3 records)
2025-05-27 13:55:06,049 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 13:55:06,051 - INFO - Saved 7 items to cache
2025-05-27 13:55:06,052 - INFO - Background data loading completed
2025-05-27 13:55:06,056 - INFO - Forced refresh of thread_safe_db data
2025-05-27 13:55:06,057 - INFO - Saved 0 items to persistent cache
2025-05-27 13:55:06,057 - INFO - Cache cleared
2025-05-27 13:55:06,057 - INFO - Cleared all preloader cache data
2025-05-27 13:55:06,060 - INFO - Starting stats data preloading
2025-05-27 13:55:06,060 - INFO - Started stats data preloading
2025-05-27 13:55:06,060 - INFO - Cleared stats preloader cache
2025-05-27 13:55:06,060 - INFO - Loading data using optimized functions
2025-05-27 13:55:06,061 - WARNING - Connection validation failed, creating new connection
2025-05-27 13:55:06,061 - INFO - DB Operation: {"timestamp": "2025-05-27 13:55:06", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:55:06,064 - INFO - Preloaded weekly stats for 7 days
2025-05-27 13:55:06,064 - WARNING - Connection validation failed, creating new connection
2025-05-27 13:55:06,065 - INFO - DB Operation: {"timestamp": "2025-05-27 13:55:06", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:55:06,066 - INFO - Preloaded summary data
2025-05-27 13:55:06,067 - INFO - Preloaded game history (3 records)
2025-05-27 13:55:06,067 - INFO - Preloaded wallet data
2025-05-27 13:55:06,067 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-05-27 13:55:06,069 - INFO - Saved 8 items to persistent cache
2025-05-27 13:55:06,073 - INFO - Started background data loading
2025-05-27 13:55:06,074 - INFO - Forced data refresh
2025-05-27 13:55:06,074 - INFO - Forced refresh of optimized loader data
2025-05-27 13:55:06,076 - INFO - Loaded weekly stats for 7 days
2025-05-27 13:55:06,076 - INFO - Loaded summary data
2025-05-27 13:55:06,076 - INFO - Loaded game history page 0 (3 records)
2025-05-27 13:55:06,076 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 13:55:06,081 - INFO - Saved 7 items to cache
2025-05-27 13:55:06,082 - INFO - Background data loading completed
2025-05-27 13:55:06,084 - INFO - Forced refresh of thread_safe_db data
2025-05-27 13:55:06,084 - INFO - Saved 0 items to persistent cache
2025-05-27 13:55:06,084 - INFO - Cache cleared
2025-05-27 13:55:06,086 - INFO - Cleared all preloader cache data
2025-05-27 13:55:06,086 - INFO - Starting stats data preloading
2025-05-27 13:55:06,086 - INFO - Started stats data preloading
2025-05-27 13:55:06,086 - INFO - Loading data using optimized functions
2025-05-27 13:55:06,086 - INFO - Cleared stats preloader cache
2025-05-27 13:55:06,087 - WARNING - Connection validation failed, creating new connection
2025-05-27 13:55:06,087 - INFO - DB Operation: {"timestamp": "2025-05-27 13:55:06", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:55:06,089 - INFO - Preloaded weekly stats for 7 days
2025-05-27 13:55:06,089 - WARNING - Connection validation failed, creating new connection
2025-05-27 13:55:06,089 - INFO - DB Operation: {"timestamp": "2025-05-27 13:55:06", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:55:06,090 - INFO - Started background data loading
2025-05-27 13:55:06,090 - INFO - Forced data refresh
2025-05-27 13:55:06,091 - INFO - Preloaded summary data
2025-05-27 13:55:06,092 - INFO - Loaded weekly stats for 7 days
2025-05-27 13:55:06,093 - INFO - Loaded summary data
2025-05-27 13:55:06,093 - INFO - Loaded game history page 0 (3 records)
2025-05-27 13:55:06,094 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 13:55:06,098 - INFO - Saved 7 items to cache
2025-05-27 13:55:06,098 - INFO - Background data loading completed
2025-05-27 13:55:06,099 - INFO - Preloaded game history (3 records)
2025-05-27 13:55:06,099 - INFO - Preloaded wallet data
2025-05-27 13:55:06,100 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-05-27 13:55:06,102 - INFO - Saved 8 items to persistent cache
2025-05-27 13:55:06,111 - INFO - Started background data loading
2025-05-27 13:55:06,111 - INFO - Forced data refresh
2025-05-27 13:55:06,112 - INFO - Forced refresh of optimized loader data
2025-05-27 13:55:06,114 - INFO - Loaded weekly stats for 7 days
2025-05-27 13:55:06,115 - INFO - Loaded summary data
2025-05-27 13:55:06,116 - INFO - Loaded game history page 0 (3 records)
2025-05-27 13:55:06,116 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 13:55:06,117 - INFO - Saved 7 items to cache
2025-05-27 13:55:06,118 - INFO - Background data loading completed
2025-05-27 13:55:06,119 - INFO - Forced refresh of thread_safe_db data
2025-05-27 13:55:06,120 - INFO - Saved 0 items to persistent cache
2025-05-27 13:55:06,120 - INFO - Cache cleared
2025-05-27 13:55:06,120 - INFO - Cleared all preloader cache data
2025-05-27 13:55:06,122 - INFO - Starting stats data preloading
2025-05-27 13:55:06,122 - INFO - Started stats data preloading
2025-05-27 13:55:06,122 - INFO - Loading data using optimized functions
2025-05-27 13:55:06,122 - INFO - Cleared stats preloader cache
2025-05-27 13:55:06,122 - WARNING - Connection validation failed, creating new connection
2025-05-27 13:55:06,123 - INFO - DB Operation: {"timestamp": "2025-05-27 13:55:06", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:55:06,129 - INFO - Preloaded weekly stats for 7 days
2025-05-27 13:55:06,129 - WARNING - Connection validation failed, creating new connection
2025-05-27 13:55:06,130 - INFO - DB Operation: {"timestamp": "2025-05-27 13:55:06", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:55:06,132 - INFO - Preloaded summary data
2025-05-27 13:55:06,132 - INFO - Preloaded game history (3 records)
2025-05-27 13:55:06,133 - INFO - Preloaded wallet data
2025-05-27 13:55:06,133 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-05-27 13:55:06,134 - INFO - Saved 8 items to persistent cache
2025-05-27 13:55:06,188 - INFO - Successfully refreshed data using: GameStatsIntegration.force_refresh_data, optimized_stats_loader.refresh_data, stats_data_provider.force_refresh
2025-05-27 13:55:06,232 - INFO - Processed game_completed event
2025-05-27 13:55:06,237 - INFO - Loaded game history page 0 from GameStatsIntegration
2025-05-27 13:55:06,238 - INFO - Loaded weekly stats from GameStatsIntegration
2025-05-27 14:06:38,207 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-05-27 14:06:38,207 - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 14:07:04,750 - INFO - 127.0.0.1 - - [27/May/2025 14:07:04] "GET / HTTP/1.1" 200 -
2025-05-27 14:07:16,852 - INFO - 127.0.0.1 - - [27/May/2025 14:07:16] "GET /daily-stats HTTP/1.1" 200 -
2025-05-27 14:07:24,346 - INFO - 127.0.0.1 - - [27/May/2025 14:07:24] "GET /wallet HTTP/1.1" 200 -
2025-05-27 14:07:25,811 - INFO - 127.0.0.1 - - [27/May/2025 14:07:25] "[33mGET /performance HTTP/1.1[0m" 404 -
2025-05-27 14:09:58,819 - INFO - 127.0.0.1 - - [27/May/2025 14:09:58] "[33mGET /performance HTTP/1.1[0m" 404 -
2025-05-27 14:10:10,683 - INFO - 127.0.0.1 - - [27/May/2025 14:10:10] "GET /game-history HTTP/1.1" 200 -
2025-05-27 14:10:19,701 - INFO - 127.0.0.1 - - [27/May/2025 14:10:19] "GET /daily-stats HTTP/1.1" 200 -
2025-05-27 14:10:26,852 - INFO - 127.0.0.1 - - [27/May/2025 14:10:26] "GET /game-history HTTP/1.1" 200 -
2025-05-27 14:12:42,332 - INFO - 127.0.0.1 - - [27/May/2025 14:12:42] "GET / HTTP/1.1" 200 -
