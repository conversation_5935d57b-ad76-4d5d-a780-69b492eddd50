{"__bytecode.const": {"blob_name": ".bytecode", "blob_size": 6876616, "input_size": 6916625}, "__constants.const": {"blob_name": "", "blob_size": 10372, "input_size": 11680}, "module.Board_selection_fixed.const": {"blob_name": "Board_selection_fixed", "blob_size": 38899, "input_size": 50175}, "module.OpenSSL.SSL.const": {"blob_name": "OpenSSL.SSL", "blob_size": 60323, "input_size": 74373}, "module.OpenSSL._util.const": {"blob_name": "OpenSSL._util", "blob_size": 2448, "input_size": 3397}, "module.OpenSSL.const": {"blob_name": "OpenSSL", "blob_size": 644, "input_size": 902}, "module.OpenSSL.crypto.const": {"blob_name": "OpenSSL.crypto", "blob_size": 59650, "input_size": 73312}, "module.OpenSSL.version.const": {"blob_name": "OpenSSL.version", "blob_size": 579, "input_size": 950}, "module.PIL.BlpImagePlugin.const": {"blob_name": "PIL.BlpImagePlugin", "blob_size": 5370, "input_size": 8261}, "module.PIL.BmpImagePlugin.const": {"blob_name": "PIL.BmpImagePlugin", "blob_size": 3790, "input_size": 6422}, "module.PIL.BufrStubImagePlugin.const": {"blob_name": "PIL.BufrStubImagePlugin", "blob_size": 1126, "input_size": 1940}, "module.PIL.CurImagePlugin.const": {"blob_name": "PIL.CurImagePlugin", "blob_size": 810, "input_size": 1587}, "module.PIL.DcxImagePlugin.const": {"blob_name": "PIL.DcxImagePlugin", "blob_size": 933, "input_size": 1800}, "module.PIL.DdsImagePlugin.const": {"blob_name": "PIL.DdsImagePlugin", "blob_size": 6781, "input_size": 13132}, "module.PIL.EpsImagePlugin.const": {"blob_name": "PIL.EpsImagePlugin", "blob_size": 4488, "input_size": 7117}, "module.PIL.ExifTags.const": {"blob_name": "PIL.ExifTags", "blob_size": 6759, "input_size": 14537}, "module.PIL.FliImagePlugin.const": {"blob_name": "PIL.FliImagePlugin", "blob_size": 1626, "input_size": 3116}, "module.PIL.FpxImagePlugin.const": {"blob_name": "PIL.FpxImagePlugin", "blob_size": 2073, "input_size": 3675}, "module.PIL.FtexImagePlugin.const": {"blob_name": "PIL.FtexImagePlugin", "blob_size": 2786, "input_size": 3723}, "module.PIL.GbrImagePlugin.const": {"blob_name": "PIL.GbrImagePlugin", "blob_size": 1043, "input_size": 1977}, "module.PIL.GifImagePlugin.const": {"blob_name": "PIL.GifImagePlugin", "blob_size": 9733, "input_size": 14206}, "module.PIL.GimpGradientFile.const": {"blob_name": "PIL.GimpGradientFile", "blob_size": 1527, "input_size": 2654}, "module.PIL.GimpPaletteFile.const": {"blob_name": "PIL.GimpPaletteFile", "blob_size": 873, "input_size": 1576}, "module.PIL.GribStubImagePlugin.const": {"blob_name": "PIL.GribStubImagePlugin", "blob_size": 1120, "input_size": 1936}, "module.PIL.Hdf5StubImagePlugin.const": {"blob_name": "PIL.Hdf5StubImagePlugin", "blob_size": 1127, "input_size": 1953}, "module.PIL.IcnsImagePlugin.const": {"blob_name": "PIL.IcnsImagePlugin", "blob_size": 4525, "input_size": 7124}, "module.PIL.IcoImagePlugin.const": {"blob_name": "PIL.IcoImagePlugin", "blob_size": 3997, "input_size": 6259}, "module.PIL.ImImagePlugin.const": {"blob_name": "PIL.ImImagePlugin", "blob_size": 3085, "input_size": 5837}, "module.PIL.Image.const": {"blob_name": "PIL.Image", "blob_size": 77056, "input_size": 91236}, "module.PIL.ImageChops.const": {"blob_name": "PIL.ImageChops", "blob_size": 5144, "input_size": 6189}, "module.PIL.ImageCms.const": {"blob_name": "PIL.ImageCms", "blob_size": 28694, "input_size": 31558}, "module.PIL.ImageColor.const": {"blob_name": "PIL.ImageColor", "blob_size": 4864, "input_size": 6000}, "module.PIL.ImageFile.const": {"blob_name": "PIL.ImageFile", "blob_size": 11054, "input_size": 15278}, "module.PIL.ImageFilter.const": {"blob_name": "PIL.ImageFilter", "blob_size": 10307, "input_size": 12990}, "module.PIL.ImageMath.const": {"blob_name": "PIL.ImageMath", "blob_size": 5387, "input_size": 7619}, "module.PIL.ImageMode.const": {"blob_name": "PIL.ImageMode", "blob_size": 1380, "input_size": 2108}, "module.PIL.ImageOps.const": {"blob_name": "PIL.ImageOps", "blob_size": 14967, "input_size": 16990}, "module.PIL.ImagePalette.const": {"blob_name": "PIL.ImagePalette", "blob_size": 3470, "input_size": 5059}, "module.PIL.ImageSequence.const": {"blob_name": "PIL.ImageSequence", "blob_size": 1358, "input_size": 1928}, "module.PIL.ImageShow.const": {"blob_name": "PIL.ImageShow", "blob_size": 4435, "input_size": 6218}, "module.PIL.ImageTk.const": {"blob_name": "PIL.ImageTk", "blob_size": 4035, "input_size": 5305}, "module.PIL.ImageWin.const": {"blob_name": "PIL.ImageWin", "blob_size": 5694, "input_size": 6994}, "module.PIL.ImtImagePlugin.const": {"blob_name": "PIL.ImtImagePlugin", "blob_size": 752, "input_size": 1669}, "module.PIL.IptcImagePlugin.const": {"blob_name": "PIL.IptcImagePlugin", "blob_size": 2406, "input_size": 4142}, "module.PIL.Jpeg2KImagePlugin.const": {"blob_name": "PIL.Jpeg2KImagePlugin", "blob_size": 4656, "input_size": 7446}, "module.PIL.JpegImagePlugin.const": {"blob_name": "PIL.JpegImagePlugin", "blob_size": 8447, "input_size": 14952}, "module.PIL.JpegPresets.const": {"blob_name": "PIL.JpegPresets", "blob_size": 4108, "input_size": 4953}, "module.PIL.McIdasImagePlugin.const": {"blob_name": "PIL.McIdasImagePlugin", "blob_size": 809, "input_size": 1578}, "module.PIL.MicImagePlugin.const": {"blob_name": "PIL.MicImagePlugin", "blob_size": 1134, "input_size": 2038}, "module.PIL.MpegImagePlugin.const": {"blob_name": "PIL.MpegImagePlugin", "blob_size": 1038, "input_size": 1931}, "module.PIL.MpoImagePlugin.const": {"blob_name": "PIL.MpoImagePlugin", "blob_size": 2506, "input_size": 4101}, "module.PIL.MspImagePlugin.const": {"blob_name": "PIL.MspImagePlugin", "blob_size": 1485, "input_size": 2849}, "module.PIL.PaletteFile.const": {"blob_name": "PIL.PaletteFile", "blob_size": 521, "input_size": 1048}, "module.PIL.PalmImagePlugin.const": {"blob_name": "PIL.PalmImagePlugin", "blob_size": 3400, "input_size": 4265}, "module.PIL.PcdImagePlugin.const": {"blob_name": "PIL.PcdImagePlugin", "blob_size": 713, "input_size": 1474}, "module.PIL.PcxImagePlugin.const": {"blob_name": "PIL.PcxImagePlugin", "blob_size": 1641, "input_size": 3107}, "module.PIL.PdfImagePlugin.const": {"blob_name": "PIL.PdfImagePlugin", "blob_size": 2655, "input_size": 4650}, "module.PIL.PdfParser.const": {"blob_name": "PIL.PdfParser", "blob_size": 11047, "input_size": 17394}, "module.PIL.PixarImagePlugin.const": {"blob_name": "PIL.PixarImagePlugin", "blob_size": 738, "input_size": 1514}, "module.PIL.PngImagePlugin.const": {"blob_name": "PIL.PngImagePlugin", "blob_size": 12438, "input_size": 19685}, "module.PIL.PpmImagePlugin.const": {"blob_name": "PIL.PpmImagePlugin", "blob_size": 3445, "input_size": 5937}, "module.PIL.PsdImagePlugin.const": {"blob_name": "PIL.PsdImagePlugin", "blob_size": 2528, "input_size": 4274}, "module.PIL.SgiImagePlugin.const": {"blob_name": "PIL.SgiImagePlugin", "blob_size": 1966, "input_size": 3535}, "module.PIL.SpiderImagePlugin.const": {"blob_name": "PIL.SpiderImagePlugin", "blob_size": 2668, "input_size": 4766}, "module.PIL.SunImagePlugin.const": {"blob_name": "PIL.SunImagePlugin", "blob_size": 1050, "input_size": 2003}, "module.PIL.TgaImagePlugin.const": {"blob_name": "PIL.TgaImagePlugin", "blob_size": 1855, "input_size": 3386}, "module.PIL.TiffImagePlugin.const": {"blob_name": "PIL.TiffImagePlugin", "blob_size": 24291, "input_size": 36683}, "module.PIL.TiffTags.const": {"blob_name": "PIL.TiffTags", "blob_size": 7570, "input_size": 11933}, "module.PIL.WebPImagePlugin.const": {"blob_name": "PIL.WebPImagePlugin", "blob_size": 3316, "input_size": 5565}, "module.PIL.WmfImagePlugin.const": {"blob_name": "PIL.WmfImagePlugin", "blob_size": 1788, "input_size": 3137}, "module.PIL.XVThumbImagePlugin.const": {"blob_name": "PIL.XVThumbImagePlugin", "blob_size": 854, "input_size": 1759}, "module.PIL.XbmImagePlugin.const": {"blob_name": "PIL.XbmImagePlugin", "blob_size": 1353, "input_size": 2451}, "module.PIL.XpmImagePlugin.const": {"blob_name": "PIL.XpmImagePlugin", "blob_size": 1156, "input_size": 2413}, "module.PIL._binary.const": {"blob_name": "PIL._binary", "blob_size": 1473, "input_size": 2088}, "module.PIL._deprecate.const": {"blob_name": "PIL._deprecate", "blob_size": 1527, "input_size": 2002}, "module.PIL._tkinter_finder.const": {"blob_name": "PIL._tkinter_finder", "blob_size": 284, "input_size": 542}, "module.PIL._typing.const": {"blob_name": "PIL._typing", "blob_size": 762, "input_size": 1471}, "module.PIL._util.const": {"blob_name": "PIL._util", "blob_size": 676, "input_size": 1092}, "module.PIL._version.const": {"blob_name": "PIL._version", "blob_size": 141, "input_size": 295}, "module.PIL.const": {"blob_name": "PIL", "blob_size": 1887, "input_size": 2390}, "module.PIL.features.const": {"blob_name": "PIL.features", "blob_size": 6027, "input_size": 8072}, "module.PyInstaller._shared_with_waf.const": {"blob_name": "PyInstaller._shared_with_waf", "blob_size": 1709, "input_size": 2205}, "module.PyInstaller.building.const": {"blob_name": "PyInstaller.building", "blob_size": 314, "input_size": 561}, "module.PyInstaller.building.utils.const": {"blob_name": "PyInstaller.building.utils", "blob_size": 12149, "input_size": 15983}, "module.PyInstaller.compat.const": {"blob_name": "PyInstaller.compat", "blob_size": 10871, "input_size": 14320}, "module.PyInstaller.config.const": {"blob_name": "PyInstaller.config", "blob_size": 1320, "input_size": 1478}, "module.PyInstaller.const": {"blob_name": "PyInstaller", "blob_size": 554, "input_size": 1077}, "module.PyInstaller.depend.const": {"blob_name": "PyInstaller.depend", "blob_size": 306, "input_size": 553}, "module.PyInstaller.depend.imphookapi.const": {"blob_name": "PyInstaller.depend.imphookapi", "blob_size": 17196, "input_size": 18885}, "module.PyInstaller.exceptions.const": {"blob_name": "PyInstaller.exceptions", "blob_size": 2386, "input_size": 3167}, "module.PyInstaller.isolated._parent.const": {"blob_name": "PyInstaller.isolated._parent", "blob_size": 7975, "input_size": 10085}, "module.PyInstaller.isolated.const": {"blob_name": "PyInstaller.isolated", "blob_size": 1340, "input_size": 1608}, "module.PyInstaller.lib.const": {"blob_name": "PyInstaller.lib", "blob_size": 294, "input_size": 541}, "module.PyInstaller.lib.modulegraph.const": {"blob_name": "PyInstaller.lib.modulegraph", "blob_size": 423, "input_size": 723}, "module.PyInstaller.lib.modulegraph.modulegraph.const": {"blob_name": "PyInstaller.lib.modulegraph.modulegraph", "blob_size": 57248, "input_size": 64871}, "module.PyInstaller.lib.modulegraph.util.const": {"blob_name": "PyInstaller.lib.modulegraph.util", "blob_size": 551, "input_size": 839}, "module.PyInstaller.log.const": {"blob_name": "PyInstaller.log", "blob_size": 987, "input_size": 1624}, "module.PyInstaller.utils.const": {"blob_name": "PyInstaller.utils", "blob_size": 302, "input_size": 549}, "module.PyInstaller.utils.hooks.conda.const": {"blob_name": "PyInstaller.utils.hooks.conda", "blob_size": 8188, "input_size": 9939}, "module.PyInstaller.utils.hooks.const": {"blob_name": "PyInstaller.utils.hooks", "blob_size": 30888, "input_size": 34929}, "module.PyInstaller.utils.misc.const": {"blob_name": "PyInstaller.utils.misc", "blob_size": 3402, "input_size": 5198}, "module.PyInstaller.utils.osx.const": {"blob_name": "PyInstaller.utils.osx", "blob_size": 11145, "input_size": 14421}, "module.PyInstaller.utils.win32.const": {"blob_name": "PyInstaller.utils.win32", "blob_size": 404, "input_size": 704}, "module.PyInstaller.utils.win32.versioninfo.const": {"blob_name": "PyInstaller.utils.win32.versioninfo", "blob_size": 9488, "input_size": 12705}, "module.PyQt5.const": {"blob_name": "PyQt5", "blob_size": 208, "input_size": 415}, "module.PyQt6.const": {"blob_name": "PyQt6", "blob_size": 370, "input_size": 740}, "module.PySide2-postLoad.const": {"blob_name": "PySide2-postLoad", "blob_size": 267, "input_size": 535}, "module.PySide2.const": {"blob_name": "PySide2", "blob_size": 1751, "input_size": 2572}, "module.PySide6-postLoad.const": {"blob_name": "PySide6-postLoad", "blob_size": 440, "input_size": 865}, "module.PySide6.const": {"blob_name": "PySide6", "blob_size": 2234, "input_size": 3649}, "module.PySide6.support.const": {"blob_name": "PySide6.support", "blob_size": 347, "input_size": 629}, "module.PySide6.support.deprecated.const": {"blob_name": "PySide6.support.deprecated", "blob_size": 148, "input_size": 276}, "module.__main__.const": {"blob_name": "__main__", "blob_size": 58525, "input_size": 79354}, "module.__parents_main__.const": {"blob_name": "__parents_main__", "blob_size": 58532, "input_size": 79199}, "module.admin_auth.const": {"blob_name": "admin_auth", "blob_size": 3765, "input_size": 5411}, "module.admin_cache.const": {"blob_name": "admin_cache", "blob_size": 2860, "input_size": 4398}, "module.admin_integration.const": {"blob_name": "admin_integration", "blob_size": 1837, "input_size": 2526}, "module.admin_ui.const": {"blob_name": "admin_ui", "blob_size": 29312, "input_size": 37001}, "module.altgraph.Graph.const": {"blob_name": "altgraph.Graph", "blob_size": 11404, "input_size": 14237}, "module.altgraph.GraphUtil.const": {"blob_name": "altgraph.GraphUtil", "blob_size": 1993, "input_size": 2611}, "module.altgraph.ObjectGraph.const": {"blob_name": "altgraph.ObjectGraph", "blob_size": 3567, "input_size": 5156}, "module.altgraph.const": {"blob_name": "altgraph", "blob_size": 5396, "input_size": 5841}, "module.amharic_support.const": {"blob_name": "amharic_support", "blob_size": 2776, "input_size": 4435}, "module.announcer_language_manager.const": {"blob_name": "announcer_language_manager", "blob_size": 1308, "input_size": 1826}, "module.attr._cmp.const": {"blob_name": "attr._cmp", "blob_size": 2568, "input_size": 3501}, "module.attr._compat.const": {"blob_name": "attr._compat", "blob_size": 1193, "input_size": 2012}, "module.attr._config.const": {"blob_name": "attr._config", "blob_size": 682, "input_size": 902}, "module.attr._funcs.const": {"blob_name": "attr._funcs", "blob_size": 7720, "input_size": 8179}, "module.attr._make.const": {"blob_name": "attr._make", "blob_size": 38252, "input_size": 45754}, "module.attr._next_gen.const": {"blob_name": "attr._next_gen", "blob_size": 23032, "input_size": 22882}, "module.attr._version_info.const": {"blob_name": "attr._version_info", "blob_size": 1289, "input_size": 1873}, "module.attr.const": {"blob_name": "attr", "blob_size": 1956, "input_size": 2748}, "module.attr.converters.const": {"blob_name": "attr.converters", "blob_size": 2657, "input_size": 3249}, "module.attr.exceptions.const": {"blob_name": "attr.exceptions", "blob_size": 1900, "input_size": 2617}, "module.attr.filters.const": {"blob_name": "attr.filters", "blob_size": 1404, "input_size": 1775}, "module.attr.setters.const": {"blob_name": "attr.setters", "blob_size": 991, "input_size": 1390}, "module.attr.validators.const": {"blob_name": "attr.validators", "blob_size": 12235, "input_size": 14905}, "module.attrs.const": {"blob_name": "attrs", "blob_size": 1207, "input_size": 1454}, "module.attrs.converters.const": {"blob_name": "attrs.converters", "blob_size": 136, "input_size": 278}, "module.attrs.exceptions.const": {"blob_name": "attrs.exceptions", "blob_size": 136, "input_size": 278}, "module.attrs.filters.const": {"blob_name": "attrs.filters", "blob_size": 127, "input_size": 269}, "module.attrs.setters.const": {"blob_name": "attrs.setters", "blob_size": 127, "input_size": 269}, "module.attrs.validators.const": {"blob_name": "attrs.validators", "blob_size": 136, "input_size": 278}, "module.backports.const": {"blob_name": "backports", "blob_size": 244, "input_size": 477}, "module.backports.tarfile.compat.const": {"blob_name": "backports.tarfile.compat", "blob_size": 389, "input_size": 663}, "module.backports.tarfile.compat.py38.const": {"blob_name": "backports.tarfile.compat.py38", "blob_size": 206, "input_size": 383}, "module.backports.tarfile.const": {"blob_name": "backports.tarfile", "blob_size": 33659, "input_size": 45491}, "module.bcrypt.__about__.const": {"blob_name": "bcrypt.__about__", "blob_size": 603, "input_size": 1000}, "module.bcrypt.const": {"blob_name": "bcrypt", "blob_size": 1921, "input_size": 2957}, "module.bingo_caller.const": {"blob_name": "bingo_caller", "blob_size": 6698, "input_size": 9094}, "module.bingo_card.const": {"blob_name": "bingo_card", "blob_size": 6289, "input_size": 8104}, "module.bingo_favor_mode.const": {"blob_name": "bingo_favor_mode", "blob_size": 11121, "input_size": 14259}, "module.bingo_logic.const": {"blob_name": "bingo_logic", "blob_size": 3182, "input_size": 4400}, "module.board_manager.const": {"blob_name": "board_manager", "blob_size": 4175, "input_size": 5668}, "module.cartella_preview_overlay.const": {"blob_name": "cartella_preview_overlay", "blob_size": 11070, "input_size": 16009}, "module.cffi._imp_emulation.const": {"blob_name": "cffi._imp_emulation", "blob_size": 1115, "input_size": 2000}, "module.cffi.api.const": {"blob_name": "cffi.api", "blob_size": 20302, "input_size": 25340}, "module.cffi.commontypes.const": {"blob_name": "cffi.commontypes", "blob_size": 1095, "input_size": 1902}, "module.cffi.const": {"blob_name": "cffi", "blob_size": 548, "input_size": 881}, "module.cffi.cparser.const": {"blob_name": "cffi.cparser", "blob_size": 10004, "input_size": 15770}, "module.cffi.error.const": {"blob_name": "cffi.error", "blob_size": 735, "input_size": 1272}, "module.cffi.ffiplatform.const": {"blob_name": "cffi.ffiplatform", "blob_size": 972, "input_size": 1743}, "module.cffi.lock.const": {"blob_name": "cffi.lock", "blob_size": 162, "input_size": 319}, "module.cffi.model.const": {"blob_name": "cffi.model", "blob_size": 6886, "input_size": 10598}, "module.cffi.pkgconfig.const": {"blob_name": "cffi.pkgconfig", "blob_size": 2458, "input_size": 3267}, "module.cffi.vengine_cpy.const": {"blob_name": "cffi.vengine_cpy", "blob_size": 22443, "input_size": 28168}, "module.cffi.vengine_gen.const": {"blob_name": "cffi.vengine_gen", "blob_size": 10386, "input_size": 14490}, "module.cffi.verifier.const": {"blob_name": "cffi.verifier", "blob_size": 3694, "input_size": 5934}, "module.chardet.big5freq.const": {"blob_name": "chardet.big5freq", "blob_size": 16205, "input_size": 16267}, "module.chardet.big5prober.const": {"blob_name": "chardet.big5prober", "blob_size": 738, "input_size": 1230}, "module.chardet.chardistribution.const": {"blob_name": "chardet.chardistribution", "blob_size": 2634, "input_size": 3541}, "module.chardet.charsetgroupprober.const": {"blob_name": "chardet.charsetgroupprober", "blob_size": 1010, "input_size": 1718}, "module.chardet.charsetprober.const": {"blob_name": "chardet.charsetp<PERSON>r", "blob_size": 2220, "input_size": 3266}, "module.chardet.codingstatemachine.const": {"blob_name": "chardet.codingstatemachine", "blob_size": 2088, "input_size": 2800}, "module.chardet.compat.const": {"blob_name": "chardet.compat", "blob_size": 161, "input_size": 372}, "module.chardet.const": {"blob_name": "chardet", "blob_size": 620, "input_size": 969}, "module.chardet.cp949prober.const": {"blob_name": "chardet.cp949p<PERSON>r", "blob_size": 749, "input_size": 1239}, "module.chardet.enums.const": {"blob_name": "chardet.enums", "blob_size": 1542, "input_size": 2413}, "module.chardet.escprober.const": {"blob_name": "chardet.escprober", "blob_size": 1467, "input_size": 2266}, "module.chardet.escsm.const": {"blob_name": "chardet.escsm", "blob_size": 1722, "input_size": 3371}, "module.chardet.eucjpprober.const": {"blob_name": "chardet.e<PERSON><PERSON><PERSON><PERSON>r", "blob_size": 1265, "input_size": 2131}, "module.chardet.euckrfreq.const": {"blob_name": "chardet.euckrfreq", "blob_size": 7138, "input_size": 7200}, "module.chardet.euckrprober.const": {"blob_name": "chardet.e<PERSON><PERSON><PERSON><PERSON>", "blob_size": 750, "input_size": 1240}, "module.chardet.euctwfreq.const": {"blob_name": "chardet.euctwfreq", "blob_size": 16210, "input_size": 16272}, "module.chardet.euctwprober.const": {"blob_name": "chardet.euctwp<PERSON>r", "blob_size": 750, "input_size": 1240}, "module.chardet.gb2312freq.const": {"blob_name": "chardet.gb2312freq", "blob_size": 11367, "input_size": 11429}, "module.chardet.gb2312prober.const": {"blob_name": "chardet.gb2312prober", "blob_size": 762, "input_size": 1250}, "module.chardet.hebrewprober.const": {"blob_name": "chardet.hebrew<PERSON><PERSON>r", "blob_size": 1294, "input_size": 2320}, "module.chardet.jisfreq.const": {"blob_name": "chardet.jisfreq", "blob_size": 13176, "input_size": 13238}, "module.chardet.jpcntx.const": {"blob_name": "chardet.jpcntx", "blob_size": 12948, "input_size": 16111}, "module.chardet.langbulgarianmodel.const": {"blob_name": "chardet.langbulgarianmodel", "blob_size": 7117, "input_size": 10011}, "module.chardet.langcyrillicmodel.const": {"blob_name": "chardet.langcyrillicmodel", "blob_size": 9589, "input_size": 12433}, "module.chardet.langgreekmodel.const": {"blob_name": "chardet.langgreekmodel", "blob_size": 6580, "input_size": 9952}, "module.chardet.langhebrewmodel.const": {"blob_name": "chardet.langhebrewmodel", "blob_size": 6161, "input_size": 9337}, "module.chardet.langthaimodel.const": {"blob_name": "chardet.langthaimodel", "blob_size": 6558, "input_size": 9314}, "module.chardet.langturkishmodel.const": {"blob_name": "chardet.langturkis<PERSON>l", "blob_size": 6768, "input_size": 9340}, "module.chardet.latin1prober.const": {"blob_name": "chardet.latin1prober", "blob_size": 1044, "input_size": 1938}, "module.chardet.mbcharsetprober.const": {"blob_name": "chardet.mbcharsetp<PERSON>r", "blob_size": 1109, "input_size": 1873}, "module.chardet.mbcsgroupprober.const": {"blob_name": "chardet.mbcsgroupprober", "blob_size": 809, "input_size": 1366}, "module.chardet.mbcssm.const": {"blob_name": "chardet.mbcssm", "blob_size": 3526, "input_size": 6887}, "module.chardet.sbcharsetprober.const": {"blob_name": "chardet.sbcharsetprober", "blob_size": 1528, "input_size": 2520}, "module.chardet.sbcsgroupprober.const": {"blob_name": "chardet.sbcsgroupprober", "blob_size": 1249, "input_size": 1753}, "module.chardet.sjisprober.const": {"blob_name": "chardet.s<PERSON><PERSON><PERSON>r", "blob_size": 1245, "input_size": 2107}, "module.chardet.universaldetector.const": {"blob_name": "chardet.universaldetector", "blob_size": 3991, "input_size": 5565}, "module.chardet.utf8prober.const": {"blob_name": "chardet.utf8prober", "blob_size": 1000, "input_size": 1794}, "module.chardet.version.const": {"blob_name": "chardet.version", "blob_size": 325, "input_size": 506}, "module.colorama.ansi.const": {"blob_name": "colorama.ansi", "blob_size": 1123, "input_size": 2471}, "module.colorama.ansitowin32.const": {"blob_name": "colorama.ansitowin32", "blob_size": 3661, "input_size": 5743}, "module.colorama.const": {"blob_name": "colorama", "blob_size": 480, "input_size": 747}, "module.colorama.initialise.const": {"blob_name": "colorama.initialise", "blob_size": 740, "input_size": 1242}, "module.colorama.win32.const": {"blob_name": "colorama.win32", "blob_size": 1784, "input_size": 3056}, "module.colorama.winterm.const": {"blob_name": "colorama.winterm", "blob_size": 1947, "input_size": 3343}, "module.commctrl.const": {"blob_name": "commctrl", "blob_size": 24818, "input_size": 45527}, "module.common_header.const": {"blob_name": "common_header", "blob_size": 862, "input_size": 1424}, "module.compact_voucher_generator.const": {"blob_name": "compact_voucher_generator", "blob_size": 6197, "input_size": 8672}, "module.constantly._constants.const": {"blob_name": "constantly._constants", "blob_size": 11410, "input_size": 13404}, "module.constantly._version.const": {"blob_name": "constantly._version", "blob_size": 325, "input_size": 505}, "module.constantly.const": {"blob_name": "constantly", "blob_size": 614, "input_size": 975}, "module.cryptography.__about__.const": {"blob_name": "cryptography.__about__", "blob_size": 283, "input_size": 502}, "module.cryptography.const": {"blob_name": "cryptography", "blob_size": 357, "input_size": 593}, "module.cryptography.exceptions.const": {"blob_name": "cryptography.exceptions", "blob_size": 868, "input_size": 1390}, "module.cryptography.hazmat._oid.const": {"blob_name": "cryptography.hazmat._oid", "blob_size": 6395, "input_size": 10901}, "module.cryptography.hazmat.backends.const": {"blob_name": "cryptography.hazmat.backends", "blob_size": 525, "input_size": 871}, "module.cryptography.hazmat.backends.openssl.aead.const": {"blob_name": "cryptography.hazmat.backends.openssl.aead", "blob_size": 3887, "input_size": 4313}, "module.cryptography.hazmat.backends.openssl.backend.const": {"blob_name": "cryptography.hazmat.backends.openssl.backend", "blob_size": 25409, "input_size": 34567}, "module.cryptography.hazmat.backends.openssl.ciphers.const": {"blob_name": "cryptography.hazmat.backends.openssl.ciphers", "blob_size": 3194, "input_size": 4818}, "module.cryptography.hazmat.backends.openssl.cmac.const": {"blob_name": "cryptography.hazmat.backends.openssl.cmac", "blob_size": 1426, "input_size": 2317}, "module.cryptography.hazmat.backends.openssl.const": {"blob_name": "cryptography.hazmat.backends.openssl", "blob_size": 608, "input_size": 957}, "module.cryptography.hazmat.backends.openssl.ec.const": {"blob_name": "cryptography.hazmat.backends.openssl.ec", "blob_size": 5280, "input_size": 6969}, "module.cryptography.hazmat.backends.openssl.rsa.const": {"blob_name": "cryptography.hazmat.backends.openssl.rsa", "blob_size": 7656, "input_size": 9119}, "module.cryptography.hazmat.backends.openssl.utils.const": {"blob_name": "cryptography.hazmat.backends.openssl.utils", "blob_size": 1070, "input_size": 1700}, "module.cryptography.hazmat.bindings.const": {"blob_name": "cryptography.hazmat.bindings", "blob_size": 409, "input_size": 683}, "module.cryptography.hazmat.bindings.openssl._conditional.const": {"blob_name": "cryptography.hazmat.bindings.openssl._conditional", "blob_size": 4821, "input_size": 6632}, "module.cryptography.hazmat.bindings.openssl.binding.const": {"blob_name": "cryptography.hazmat.bindings.openssl.binding", "blob_size": 3063, "input_size": 4326}, "module.cryptography.hazmat.bindings.openssl.const": {"blob_name": "cryptography.hazmat.bindings.openssl", "blob_size": 520, "input_size": 821}, "module.cryptography.hazmat.const": {"blob_name": "cryptography.hazmat", "blob_size": 323, "input_size": 583}, "module.cryptography.hazmat.primitives._asymmetric.const": {"blob_name": "cryptography.hazmat.primitives._asymmetric", "blob_size": 577, "input_size": 952}, "module.cryptography.hazmat.primitives._cipheralgorithm.const": {"blob_name": "cryptography.hazmat.primitives._cipheralgorithm", "blob_size": 1056, "input_size": 1644}, "module.cryptography.hazmat.primitives._serialization.const": {"blob_name": "cryptography.hazmat.primitives._serialization", "blob_size": 2990, "input_size": 3730}, "module.cryptography.hazmat.primitives.asymmetric.const": {"blob_name": "cryptography.hazmat.primitives.asymmetric", "blob_size": 550, "input_size": 851}, "module.cryptography.hazmat.primitives.asymmetric.dh.const": {"blob_name": "cryptography.hazmat.primitives.asymmetric.dh", "blob_size": 4360, "input_size": 5748}, "module.cryptography.hazmat.primitives.asymmetric.dsa.const": {"blob_name": "cryptography.hazmat.primitives.asymmetric.dsa", "blob_size": 5075, "input_size": 6661}, "module.cryptography.hazmat.primitives.asymmetric.ec.const": {"blob_name": "cryptography.hazmat.primitives.asymmetric.ec", "blob_size": 6830, "input_size": 9141}, "module.cryptography.hazmat.primitives.asymmetric.ed25519.const": {"blob_name": "cryptography.hazmat.primitives.asymmetric.ed25519", "blob_size": 2698, "input_size": 3574}, "module.cryptography.hazmat.primitives.asymmetric.ed448.const": {"blob_name": "cryptography.hazmat.primitives.asymmetric.ed448", "blob_size": 2638, "input_size": 3526}, "module.cryptography.hazmat.primitives.asymmetric.padding.const": {"blob_name": "cryptography.hazmat.primitives.asymmetric.padding", "blob_size": 1782, "input_size": 2708}, "module.cryptography.hazmat.primitives.asymmetric.rsa.const": {"blob_name": "cryptography.hazmat.primitives.asymmetric.rsa", "blob_size": 6489, "input_size": 8312}, "module.cryptography.hazmat.primitives.asymmetric.types.const": {"blob_name": "cryptography.hazmat.primitives.asymmetric.types", "blob_size": 1347, "input_size": 2048}, "module.cryptography.hazmat.primitives.asymmetric.utils.const": {"blob_name": "cryptography.hazmat.primitives.asymmetric.utils", "blob_size": 654, "input_size": 1076}, "module.cryptography.hazmat.primitives.asymmetric.x25519.const": {"blob_name": "cryptography.hazmat.primitives.asymmetric.x25519", "blob_size": 2629, "input_size": 3440}, "module.cryptography.hazmat.primitives.asymmetric.x448.const": {"blob_name": "cryptography.hazmat.primitives.asymmetric.x448", "blob_size": 2572, "input_size": 3397}, "module.cryptography.hazmat.primitives.ciphers.aead.const": {"blob_name": "cryptography.hazmat.primitives.ciphers.aead", "blob_size": 3196, "input_size": 4701}, "module.cryptography.hazmat.primitives.ciphers.algorithms.const": {"blob_name": "cryptography.hazmat.primitives.ciphers.algorithms", "blob_size": 1837, "input_size": 3245}, "module.cryptography.hazmat.primitives.ciphers.base.const": {"blob_name": "cryptography.hazmat.primitives.ciphers.base", "blob_size": 3983, "input_size": 5483}, "module.cryptography.hazmat.primitives.ciphers.const": {"blob_name": "cryptography.hazmat.primitives.ciphers", "blob_size": 1035, "input_size": 1349}, "module.cryptography.hazmat.primitives.ciphers.modes.const": {"blob_name": "cryptography.hazmat.primitives.ciphers.modes", "blob_size": 3669, "input_size": 5060}, "module.cryptography.hazmat.primitives.const": {"blob_name": "cryptography.hazmat.primitives", "blob_size": 419, "input_size": 693}, "module.cryptography.hazmat.primitives.constant_time.const": {"blob_name": "cryptography.hazmat.primitives.constant_time", "blob_size": 274, "input_size": 488}, "module.cryptography.hazmat.primitives.hashes.const": {"blob_name": "cryptography.hazmat.primitives.hashes", "blob_size": 2589, "input_size": 4047}, "module.cryptography.hazmat.primitives.serialization.base.const": {"blob_name": "cryptography.hazmat.primitives.serialization.base", "blob_size": 946, "input_size": 1148}, "module.cryptography.hazmat.primitives.serialization.const": {"blob_name": "cryptography.hazmat.primitives.serialization", "blob_size": 2228, "input_size": 2321}, "module.cryptography.hazmat.primitives.serialization.pkcs12.const": {"blob_name": "cryptography.hazmat.primitives.serialization.pkcs12", "blob_size": 3716, "input_size": 4930}, "module.cryptography.hazmat.primitives.serialization.ssh.const": {"blob_name": "cryptography.hazmat.primitives.serialization.ssh", "blob_size": 18547, "input_size": 24330}, "module.cryptography.utils.const": {"blob_name": "cryptography.utils", "blob_size": 2240, "input_size": 3362}, "module.cryptography.x509.base.const": {"blob_name": "cryptography.x509.base", "blob_size": 18124, "input_size": 21950}, "module.cryptography.x509.certificate_transparency.const": {"blob_name": "cryptography.x509.certificate_transparency", "blob_size": 2151, "input_size": 3068}, "module.cryptography.x509.const": {"blob_name": "cryptography.x509", "blob_size": 7691, "input_size": 8705}, "module.cryptography.x509.extensions.const": {"blob_name": "cryptography.x509.extensions", "blob_size": 27131, "input_size": 35163}, "module.cryptography.x509.general_name.const": {"blob_name": "cryptography.x509.general_name", "blob_size": 3430, "input_size": 5035}, "module.cryptography.x509.name.const": {"blob_name": "cryptography.x509.name", "blob_size": 7356, "input_size": 10724}, "module.cryptography.x509.oid.const": {"blob_name": "cryptography.x509.oid", "blob_size": 846, "input_size": 881}, "module.db_connection_pool.const": {"blob_name": "db_connection_pool", "blob_size": 3320, "input_size": 4726}, "module.db_hybrid.const": {"blob_name": "db_hybrid", "blob_size": 9992, "input_size": 12174}, "module.db_security.const": {"blob_name": "db_security", "blob_size": 3888, "input_size": 5493}, "module.defusedxml.ElementTree.const": {"blob_name": "defusedxml.ElementTree", "blob_size": 2344, "input_size": 3275}, "module.defusedxml.cElementTree.const": {"blob_name": "defusedxml.cElementTree", "blob_size": 890, "input_size": 1211}, "module.defusedxml.common.const": {"blob_name": "defusedxml.common", "blob_size": 2414, "input_size": 3173}, "module.defusedxml.const": {"blob_name": "defusedxml", "blob_size": 1207, "input_size": 1579}, "module.defusedxml.expatbuilder.const": {"blob_name": "defusedxml.expatbuilder", "blob_size": 2251, "input_size": 2999}, "module.defusedxml.expatreader.const": {"blob_name": "defusedxml.expatreader", "blob_size": 1523, "input_size": 2156}, "module.defusedxml.minidom.const": {"blob_name": "defusedxml.minidom", "blob_size": 694, "input_size": 1023}, "module.defusedxml.pulldom.const": {"blob_name": "defusedxml.pulldom", "blob_size": 520, "input_size": 784}, "module.defusedxml.sax.const": {"blob_name": "defusedxml.sax", "blob_size": 658, "input_size": 963}, "module.defusedxml.xmlrpc.const": {"blob_name": "defusedxml.xmlrpc", "blob_size": 2279, "input_size": 3355}, "module.exceptiongroup._catch.const": {"blob_name": "exceptiongroup._catch", "blob_size": 1587, "input_size": 2295}, "module.exceptiongroup._exceptions.const": {"blob_name": "exceptiongroup._exceptions", "blob_size": 6197, "input_size": 7795}, "module.exceptiongroup._formatting.const": {"blob_name": "exceptiongroup._formatting", "blob_size": 5369, "input_size": 7488}, "module.exceptiongroup._suppress.const": {"blob_name": "exceptiongroup._suppress", "blob_size": 673, "input_size": 1111}, "module.exceptiongroup._version.const": {"blob_name": "exceptiongroup._version", "blob_size": 200, "input_size": 392}, "module.exceptiongroup.const": {"blob_name": "exceptiongroup", "blob_size": 797, "input_size": 1206}, "module.external_display_manager.const": {"blob_name": "external_display_manager", "blob_size": 7845, "input_size": 10308}, "module.game_state_handler.const": {"blob_name": "game_state_handler", "blob_size": 20919, "input_size": 28101}, "module.game_stats_integration.const": {"blob_name": "game_stats_integration", "blob_size": 8589, "input_size": 11119}, "module.game_ui_handler.const": {"blob_name": "game_ui_handler", "blob_size": 38865, "input_size": 47475}, "module.get_machine_uuid.const": {"blob_name": "get_machine_uuid", "blob_size": 1380, "input_size": 2425}, "module.greenlet.const": {"blob_name": "greenlet", "blob_size": 885, "input_size": 1234}, "module.hdmi_display_fixer.const": {"blob_name": "hdmi_display_fixer", "blob_size": 3814, "input_size": 5486}, "module.hybrid_db_integration.const": {"blob_name": "hybrid_db_integration", "blob_size": 7948, "input_size": 9836}, "module.idna.const": {"blob_name": "idna", "blob_size": 257, "input_size": 509}, "module.idna.core.const": {"blob_name": "idna.core", "blob_size": 3261, "input_size": 5384}, "module.idna.idnadata.const": {"blob_name": "idna.idnadata", "blob_size": 12234, "input_size": 15237}, "module.idna.intranges.const": {"blob_name": "idna.intranges", "blob_size": 976, "input_size": 1311}, "module.idna.package_data.const": {"blob_name": "idna.package_data", "blob_size": 136, "input_size": 277}, "module.idna.uts46data.const": {"blob_name": "idna.uts46data", "blob_size": 88201, "input_size": 96507}, "module.incremental._version.const": {"blob_name": "incremental._version", "blob_size": 244, "input_size": 434}, "module.incremental.const": {"blob_name": "incremental", "blob_size": 4993, "input_size": 6829}, "module.jaraco.const": {"blob_name": "jaraco", "blob_size": 114, "input_size": 231}, "module.jaraco.context.const": {"blob_name": "jaraco.context", "blob_size": 7187, "input_size": 9268}, "module.jaraco.functools.const": {"blob_name": "jaraco.functools", "blob_size": 14251, "input_size": 16579}, "module.jaraco.text.const": {"blob_name": "jaraco.text", "blob_size": 13359, "input_size": 16812}, "module.looseversion.const": {"blob_name": "looseversion", "blob_size": 3365, "input_size": 4408}, "module.main.const": {"blob_name": "main", "blob_size": 58227, "input_size": 78637}, "module.modern_advertising_integration.const": {"blob_name": "modern_advertising_integration", "blob_size": 9207, "input_size": 11607}, "module.monitor_stats_performance.const": {"blob_name": "monitor_stats_performance", "blob_size": 3943, "input_size": 5784}, "module.more_itertools.const": {"blob_name": "more_itertools", "blob_size": 335, "input_size": 621}, "module.more_itertools.more.const": {"blob_name": "more_itertools.more", "blob_size": 97751, "input_size": 107404}, "module.more_itertools.recipes.const": {"blob_name": "more_itertools.recipes", "blob_size": 20372, "input_size": 23746}, "module.multiprocessing-postLoad.const": {"blob_name": "multiprocessing-postLoad", "blob_size": 364, "input_size": 692}, "module.multiprocessing-preLoad.const": {"blob_name": "multiprocessing-preLoad", "blob_size": 226, "input_size": 471}, "module.nacl.bindings.const": {"blob_name": "nacl.bindings", "blob_size": 21022, "input_size": 16591}, "module.nacl.bindings.crypto_aead.const": {"blob_name": "nacl.bindings.crypto_aead", "blob_size": 4678, "input_size": 5830}, "module.nacl.bindings.crypto_box.const": {"blob_name": "nacl.bindings.crypto_box", "blob_size": 4608, "input_size": 5647}, "module.nacl.bindings.crypto_core.const": {"blob_name": "nacl.bindings.crypto_core", "blob_size": 7231, "input_size": 8167}, "module.nacl.bindings.crypto_generichash.const": {"blob_name": "nacl.bindings.crypto_generichash", "blob_size": 4862, "input_size": 6178}, "module.nacl.bindings.crypto_hash.const": {"blob_name": "nacl.bindings.crypto_hash", "blob_size": 669, "input_size": 1166}, "module.nacl.bindings.crypto_kx.const": {"blob_name": "nacl.bindings.crypto_kx", "blob_size": 2691, "input_size": 3415}, "module.nacl.bindings.crypto_pwhash.const": {"blob_name": "nacl.bindings.crypto_pwhash", "blob_size": 9306, "input_size": 11720}, "module.nacl.bindings.crypto_scalarmult.const": {"blob_name": "nacl.bindings.crypto_scalarmult", "blob_size": 4162, "input_size": 4932}, "module.nacl.bindings.crypto_secretbox.const": {"blob_name": "nacl.bindings.crypto_secretbox", "blob_size": 1431, "input_size": 2031}, "module.nacl.bindings.crypto_secretstream.const": {"blob_name": "nacl.bindings.crypto_secretstream", "blob_size": 5250, "input_size": 6615}, "module.nacl.bindings.crypto_shorthash.const": {"blob_name": "nacl.bindings.crypto_shorthash", "blob_size": 1315, "input_size": 1916}, "module.nacl.bindings.crypto_sign.const": {"blob_name": "nacl.bindings.crypto_sign", "blob_size": 5051, "input_size": 6345}, "module.nacl.bindings.randombytes.const": {"blob_name": "nacl.bindings.randombytes", "blob_size": 821, "input_size": 1239}, "module.nacl.bindings.sodium_core.const": {"blob_name": "nacl.bindings.sodium_core", "blob_size": 451, "input_size": 793}, "module.nacl.bindings.utils.const": {"blob_name": "nacl.bindings.utils", "blob_size": 2167, "input_size": 2808}, "module.nacl.const": {"blob_name": "nacl", "blob_size": 620, "input_size": 1070}, "module.nacl.encoding.const": {"blob_name": "nacl.encoding", "blob_size": 1222, "input_size": 2119}, "module.nacl.exceptions.const": {"blob_name": "nacl.exceptions", "blob_size": 1261, "input_size": 1815}, "module.nacl.public.const": {"blob_name": "nacl.public", "blob_size": 8628, "input_size": 10359}, "module.nacl.signing.const": {"blob_name": "nacl.signing", "blob_size": 4710, "input_size": 6251}, "module.nacl.utils.const": {"blob_name": "nacl.utils", "blob_size": 1550, "input_size": 2477}, "module.numpy.__config__.const": {"blob_name": "numpy.__config__", "blob_size": 2861, "input_size": 3745}, "module.numpy._distributor_init.const": {"blob_name": "numpy._distributor_init", "blob_size": 554, "input_size": 892}, "module.numpy._globals.const": {"blob_name": "numpy._globals", "blob_size": 2764, "input_size": 3385}, "module.numpy._pytesttester.const": {"blob_name": "numpy._pytesttester", "blob_size": 229, "input_size": 436}, "module.numpy._typing._array_like.const": {"blob_name": "numpy._typing._array_like", "blob_size": 1831, "input_size": 2747}, "module.numpy._typing._char_codes.const": {"blob_name": "numpy._typing._char_codes", "blob_size": 4601, "input_size": 6210}, "module.numpy._typing._dtype_like.const": {"blob_name": "numpy._typing._dtype_like", "blob_size": 2247, "input_size": 3155}, "module.numpy._typing._nbit.const": {"blob_name": "numpy._typing._nbit", "blob_size": 319, "input_size": 590}, "module.numpy._typing._nested_sequence.const": {"blob_name": "numpy._typing._nested_sequence", "blob_size": 2482, "input_size": 3224}, "module.numpy._typing._scalars.const": {"blob_name": "numpy._typing._scalars", "blob_size": 407, "input_size": 852}, "module.numpy._typing._shape.const": {"blob_name": "numpy._typing._shape", "blob_size": 213, "input_size": 498}, "module.numpy._typing.const": {"blob_name": "numpy._typing", "blob_size": 5550, "input_size": 5605}, "module.numpy._utils._convertions.const": {"blob_name": "numpy._utils._convertions", "blob_size": 280, "input_size": 500}, "module.numpy._utils._inspect.const": {"blob_name": "numpy._utils._inspect", "blob_size": 5022, "input_size": 5923}, "module.numpy._utils.const": {"blob_name": "numpy._utils", "blob_size": 962, "input_size": 1330}, "module.numpy.compat.const": {"blob_name": "numpy.compat", "blob_size": 663, "input_size": 1009}, "module.numpy.compat.py3k.const": {"blob_name": "numpy.compat.py3k", "blob_size": 2677, "input_size": 3726}, "module.numpy.const": {"blob_name": "numpy", "blob_size": 8635, "input_size": 11600}, "module.numpy.core._add_newdocs.const": {"blob_name": "numpy.core._add_newdocs", "blob_size": 193440, "input_size": 198384}, "module.numpy.core._add_newdocs_scalars.const": {"blob_name": "numpy.core._add_newdocs_scalars", "blob_size": 9255, "input_size": 11049}, "module.numpy.core._asarray.const": {"blob_name": "numpy.core._asarray", "blob_size": 3121, "input_size": 3569}, "module.numpy.core._dtype.const": {"blob_name": "numpy.core._dtype", "blob_size": 3939, "input_size": 5618}, "module.numpy.core._dtype_ctypes.const": {"blob_name": "numpy.core._dtype_ctypes", "blob_size": 1660, "input_size": 2272}, "module.numpy.core._exceptions.const": {"blob_name": "numpy.core._exceptions", "blob_size": 2858, "input_size": 4102}, "module.numpy.core._internal.const": {"blob_name": "numpy.core._internal", "blob_size": 10832, "input_size": 15033}, "module.numpy.core._machar.const": {"blob_name": "numpy.core._machar", "blob_size": 5638, "input_size": 6772}, "module.numpy.core._methods.const": {"blob_name": "numpy.core._methods", "blob_size": 2068, "input_size": 3549}, "module.numpy.core._string_helpers.const": {"blob_name": "numpy.core._string_helpers", "blob_size": 2512, "input_size": 3024}, "module.numpy.core._type_aliases.const": {"blob_name": "numpy.core._type_aliases", "blob_size": 2490, "input_size": 3724}, "module.numpy.core._ufunc_config.const": {"blob_name": "numpy.core._ufunc_config", "blob_size": 11801, "input_size": 13057}, "module.numpy.core.arrayprint.const": {"blob_name": "numpy.core.arrayprint", "blob_size": 35515, "input_size": 39646}, "module.numpy.core.const": {"blob_name": "numpy.core", "blob_size": 2055, "input_size": 3213}, "module.numpy.core.defchararray.const": {"blob_name": "numpy.core.def<PERSON><PERSON><PERSON>", "blob_size": 56003, "input_size": 61169}, "module.numpy.core.einsumfunc.const": {"blob_name": "numpy.core.einsumfunc", "blob_size": 31575, "input_size": 33299}, "module.numpy.core.fromnumeric.const": {"blob_name": "numpy.core.fromnumeric", "blob_size": 113071, "input_size": 116375}, "module.numpy.core.function_base.const": {"blob_name": "numpy.core.function_base", "blob_size": 14885, "input_size": 16236}, "module.numpy.core.getlimits.const": {"blob_name": "numpy.core.getlimits", "blob_size": 12148, "input_size": 15699}, "module.numpy.core.memmap.const": {"blob_name": "numpy.core.memmap", "blob_size": 8628, "input_size": 9917}, "module.numpy.core.multiarray.const": {"blob_name": "numpy.core.multiarray", "blob_size": 52097, "input_size": 53582}, "module.numpy.core.numeric.const": {"blob_name": "numpy.core.numeric", "blob_size": 61686, "input_size": 66122}, "module.numpy.core.numerictypes.const": {"blob_name": "numpy.core.numerictypes", "blob_size": 13564, "input_size": 15434}, "module.numpy.core.overrides.const": {"blob_name": "numpy.core.overrides", "blob_size": 5313, "input_size": 5863}, "module.numpy.core.records.const": {"blob_name": "numpy.core.records", "blob_size": 20637, "input_size": 23255}, "module.numpy.core.shape_base.const": {"blob_name": "numpy.core.shape_base", "blob_size": 20453, "input_size": 21964}, "module.numpy.core.umath.const": {"blob_name": "numpy.core.umath", "blob_size": 1655, "input_size": 1933}, "module.numpy.ctypeslib.const": {"blob_name": "numpy.ctypeslib", "blob_size": 9325, "input_size": 11916}, "module.numpy.dtypes.const": {"blob_name": "numpy.dtypes", "blob_size": 2017, "input_size": 2257}, "module.numpy.exceptions.const": {"blob_name": "numpy.exceptions", "blob_size": 6761, "input_size": 7529}, "module.numpy.fft._pocketfft.const": {"blob_name": "numpy.fft._pocketfft", "blob_size": 47715, "input_size": 48983}, "module.numpy.fft.const": {"blob_name": "numpy.fft", "blob_size": 8288, "input_size": 8638}, "module.numpy.fft.helper.const": {"blob_name": "numpy.fft.helper", "blob_size": 5033, "input_size": 5591}, "module.numpy.lib._datasource.const": {"blob_name": "numpy.lib._datasource", "blob_size": 15604, "input_size": 17665}, "module.numpy.lib._iotools.const": {"blob_name": "numpy.lib._iotools", "blob_size": 16703, "input_size": 19756}, "module.numpy.lib._version.const": {"blob_name": "numpy.lib._version", "blob_size": 2548, "input_size": 3405}, "module.numpy.lib.arraypad.const": {"blob_name": "numpy.lib.arraypad", "blob_size": 17583, "input_size": 19010}, "module.numpy.lib.arraysetops.const": {"blob_name": "numpy.lib.arraysetops", "blob_size": 22706, "input_size": 24445}, "module.numpy.lib.arrayterator.const": {"blob_name": "numpy.lib.arrayterator", "blob_size": 4283, "input_size": 5119}, "module.numpy.lib.const": {"blob_name": "numpy.lib", "blob_size": 1697, "input_size": 2320}, "module.numpy.lib.format.const": {"blob_name": "numpy.lib.format", "blob_size": 21048, "input_size": 23690}, "module.numpy.lib.function_base.const": {"blob_name": "numpy.lib.function_base", "blob_size": 130886, "input_size": 139543}, "module.numpy.lib.histograms.const": {"blob_name": "numpy.lib.histograms", "blob_size": 24378, "input_size": 27259}, "module.numpy.lib.index_tricks.const": {"blob_name": "numpy.lib.index_tricks", "blob_size": 22360, "input_size": 25129}, "module.numpy.lib.mixins.const": {"blob_name": "numpy.lib.mixins", "blob_size": 5349, "input_size": 7207}, "module.numpy.lib.nanfunctions.const": {"blob_name": "numpy.lib.nanfunctions", "blob_size": 50633, "input_size": 52956}, "module.numpy.lib.npyio.const": {"blob_name": "numpy.lib.npyio", "blob_size": 56317, "input_size": 60726}, "module.numpy.lib.polynomial.const": {"blob_name": "numpy.lib.polynomial", "blob_size": 31065, "input_size": 35323}, "module.numpy.lib.scimath.const": {"blob_name": "numpy.lib.scimath", "blob_size": 13478, "input_size": 14452}, "module.numpy.lib.shape_base.const": {"blob_name": "numpy.lib.shape_base", "blob_size": 28953, "input_size": 31161}, "module.numpy.lib.stride_tricks.const": {"blob_name": "numpy.lib.stride_tricks", "blob_size": 13748, "input_size": 15051}, "module.numpy.lib.twodim_base.const": {"blob_name": "numpy.lib.twodim_base", "blob_size": 28402, "input_size": 30402}, "module.numpy.lib.type_check.const": {"blob_name": "numpy.lib.type_check", "blob_size": 15786, "input_size": 17526}, "module.numpy.lib.ufunclike.const": {"blob_name": "numpy.lib.ufunclike", "blob_size": 5359, "input_size": 5901}, "module.numpy.lib.utils.const": {"blob_name": "numpy.lib.utils", "blob_size": 19485, "input_size": 23470}, "module.numpy.linalg.const": {"blob_name": "numpy.linalg", "blob_size": 2002, "input_size": 2361}, "module.numpy.linalg.linalg.const": {"blob_name": "numpy.linalg.linalg", "blob_size": 67579, "input_size": 72853}, "module.numpy.ma.const": {"blob_name": "numpy.ma", "blob_size": 1523, "input_size": 1878}, "module.numpy.ma.core.const": {"blob_name": "numpy.ma.core", "blob_size": 159155, "input_size": 175395}, "module.numpy.ma.extras.const": {"blob_name": "numpy.ma.extras", "blob_size": 42492, "input_size": 47584}, "module.numpy.ma.mrecords.const": {"blob_name": "numpy.ma.mrecords", "blob_size": 10911, "input_size": 13906}, "module.numpy.matrixlib.const": {"blob_name": "numpy.matrixlib", "blob_size": 473, "input_size": 810}, "module.numpy.matrixlib.defmatrix.const": {"blob_name": "numpy.matrixlib.defmatrix", "blob_size": 23446, "input_size": 26222}, "module.numpy.polynomial._polybase.const": {"blob_name": "numpy.polynomial._polybase", "blob_size": 24850, "input_size": 29144}, "module.numpy.polynomial.chebyshev.const": {"blob_name": "numpy.polynomial.cheb<PERSON><PERSON>v", "blob_size": 53298, "input_size": 57009}, "module.numpy.polynomial.const": {"blob_name": "numpy.polynomial", "blob_size": 6813, "input_size": 7373}, "module.numpy.polynomial.hermite.const": {"blob_name": "numpy.polynomial.hermite", "blob_size": 45285, "input_size": 48287}, "module.numpy.polynomial.hermite_e.const": {"blob_name": "numpy.polynomial.hermite_e", "blob_size": 45457, "input_size": 48400}, "module.numpy.polynomial.laguerre.const": {"blob_name": "numpy.polynomial.laguerre", "blob_size": 43984, "input_size": 46806}, "module.numpy.polynomial.legendre.const": {"blob_name": "numpy.polynomial.legendre", "blob_size": 44324, "input_size": 47134}, "module.numpy.polynomial.polynomial.const": {"blob_name": "numpy.polynomial.polynomial", "blob_size": 42986, "input_size": 45853}, "module.numpy.polynomial.polyutils.const": {"blob_name": "numpy.polynomial.polyutils", "blob_size": 15819, "input_size": 18708}, "module.numpy.random._pickle.const": {"blob_name": "numpy.random._pickle", "blob_size": 1714, "input_size": 2095}, "module.numpy.random.const": {"blob_name": "numpy.random", "blob_size": 7353, "input_size": 7982}, "module.numpy.version.const": {"blob_name": "numpy.version", "blob_size": 235, "input_size": 468}, "module.optimized_stats_loader.const": {"blob_name": "optimized_stats_loader", "blob_size": 6256, "input_size": 9191}, "module.ordlookup.const": {"blob_name": "ordlookup", "blob_size": 492, "input_size": 882}, "module.ordlookup.oleaut32.const": {"blob_name": "ordlookup.oleaut32", "blob_size": 7392, "input_size": 7803}, "module.ordlookup.ws2_32.const": {"blob_name": "ordlookup.ws2_32", "blob_size": 2317, "input_size": 2575}, "module.outcome._impl.const": {"blob_name": "outcome._impl", "blob_size": 3727, "input_size": 5068}, "module.outcome._util.const": {"blob_name": "outcome._util", "blob_size": 805, "input_size": 1339}, "module.outcome._version.const": {"blob_name": "outcome._version", "blob_size": 185, "input_size": 365}, "module.outcome.const": {"blob_name": "outcome", "blob_size": 569, "input_size": 862}, "module.pattern_utils.const": {"blob_name": "pattern_utils", "blob_size": 1627, "input_size": 2363}, "module.payment.const": {"blob_name": "payment", "blob_size": 573, "input_size": 843}, "module.payment.crypto_utils.const": {"blob_name": "payment.crypto_utils", "blob_size": 5226, "input_size": 7540}, "module.payment.game_integration.const": {"blob_name": "payment.game_integration", "blob_size": 6189, "input_size": 7824}, "module.payment.simple_integration.const": {"blob_name": "payment.simple_integration", "blob_size": 2847, "input_size": 3967}, "module.payment.simple_recharge_ui.const": {"blob_name": "payment.simple_recharge_ui", "blob_size": 5935, "input_size": 9216}, "module.payment.usage_tracker.const": {"blob_name": "payment.usage_tracker", "blob_size": 3299, "input_size": 4469}, "module.payment.voucher_generator.const": {"blob_name": "payment.voucher_generator", "blob_size": 4544, "input_size": 6562}, "module.payment.voucher_manager.const": {"blob_name": "payment.voucher_manager", "blob_size": 12981, "input_size": 16397}, "module.payment.voucher_processor.const": {"blob_name": "payment.voucher_processor", "blob_size": 4805, "input_size": 7276}, "module.pefile.const": {"blob_name": "pefile", "blob_size": 85744, "input_size": 106527}, "module.pkg_resources-postLoad.const": {"blob_name": "pkg_resources-postLoad", "blob_size": 708, "input_size": 1293}, "module.platformdirs.android.const": {"blob_name": "platformdirs.android", "blob_size": 4378, "input_size": 6012}, "module.platformdirs.api.const": {"blob_name": "platformdirs.api", "blob_size": 4455, "input_size": 6328}, "module.platformdirs.const": {"blob_name": "platformdirs", "blob_size": 12430, "input_size": 13653}, "module.platformdirs.version.const": {"blob_name": "platformdirs.version", "blob_size": 195, "input_size": 387}, "module.platformdirs.windows.const": {"blob_name": "platformdirs.windows", "blob_size": 5600, "input_size": 7637}, "module.player_storage.const": {"blob_name": "player_storage", "blob_size": 3736, "input_size": 4926}, "module.power_management.const": {"blob_name": "power_management", "blob_size": 3946, "input_size": 5608}, "module.psutil._common.const": {"blob_name": "psutil._common", "blob_size": 11948, "input_size": 17881}, "module.psutil._psaix.const": {"blob_name": "psutil._psaix", "blob_size": 5931, "input_size": 10104}, "module.psutil._psbsd.const": {"blob_name": "psutil._psbsd", "blob_size": 8567, "input_size": 13961}, "module.psutil._pslinux.const": {"blob_name": "psutil._pslinux", "blob_size": 22956, "input_size": 33936}, "module.psutil._psosx.const": {"blob_name": "psutil._psosx", "blob_size": 5574, "input_size": 9341}, "module.psutil._psposix.const": {"blob_name": "psutil._psposix", "blob_size": 2498, "input_size": 3430}, "module.psutil._pssunos.const": {"blob_name": "psutil._pssunos", "blob_size": 6982, "input_size": 11774}, "module.psutil._pswindows.const": {"blob_name": "psutil._pswindows", "blob_size": 12606, "input_size": 19170}, "module.psutil.const": {"blob_name": "psutil", "blob_size": 42107, "input_size": 50814}, "module.pyasn1.codec.ber.const": {"blob_name": "pyasn1.codec.ber", "blob_size": 349, "input_size": 623}, "module.pyasn1.codec.ber.decoder.const": {"blob_name": "pyasn1.codec.ber.decoder", "blob_size": 10564, "input_size": 13708}, "module.pyasn1.codec.ber.eoo.const": {"blob_name": "pyasn1.codec.ber.eoo", "blob_size": 524, "input_size": 991}, "module.pyasn1.codec.cer.const": {"blob_name": "pyasn1.codec.cer", "blob_size": 349, "input_size": 623}, "module.pyasn1.codec.cer.decoder.const": {"blob_name": "pyasn1.codec.cer.decoder", "blob_size": 946, "input_size": 1699}, "module.pyasn1.codec.const": {"blob_name": "pyasn1.codec", "blob_size": 282, "input_size": 529}, "module.pyasn1.codec.der.const": {"blob_name": "pyasn1.codec.der", "blob_size": 349, "input_size": 623}, "module.pyasn1.codec.der.decoder.const": {"blob_name": "pyasn1.codec.der.decoder", "blob_size": 609, "input_size": 1161}, "module.pyasn1.compat.binary.const": {"blob_name": "pyasn1.compat.binary", "blob_size": 142, "input_size": 283}, "module.pyasn1.compat.calling.const": {"blob_name": "pyasn1.compat.calling", "blob_size": 158, "input_size": 312}, "module.pyasn1.compat.const": {"blob_name": "pyasn1.compat", "blob_size": 286, "input_size": 533}, "module.pyasn1.compat.dateandtime.const": {"blob_name": "pyasn1.compat.dateandtime", "blob_size": 214, "input_size": 412}, "module.pyasn1.compat.integer.const": {"blob_name": "pyasn1.compat.integer", "blob_size": 793, "input_size": 1430}, "module.pyasn1.compat.octets.const": {"blob_name": "pyasn1.compat.octets", "blob_size": 295, "input_size": 646}, "module.pyasn1.compat.string.const": {"blob_name": "pyasn1.compat.string", "blob_size": 163, "input_size": 318}, "module.pyasn1.const": {"blob_name": "pyasn1", "blob_size": 236, "input_size": 482}, "module.pyasn1.debug.const": {"blob_name": "pyasn1.debug", "blob_size": 1545, "input_size": 3077}, "module.pyasn1.error.const": {"blob_name": "pyasn1.error", "blob_size": 2008, "input_size": 2513}, "module.pyasn1.type.base.const": {"blob_name": "pyasn1.type.base", "blob_size": 13962, "input_size": 16837}, "module.pyasn1.type.char.const": {"blob_name": "pyasn1.type.char", "blob_size": 3181, "input_size": 4501}, "module.pyasn1.type.const": {"blob_name": "pyasn1.type", "blob_size": 278, "input_size": 525}, "module.pyasn1.type.constraint.const": {"blob_name": "pyasn1.type.constraint", "blob_size": 16711, "input_size": 18858}, "module.pyasn1.type.error.const": {"blob_name": "pyasn1.type.error", "blob_size": 339, "input_size": 602}, "module.pyasn1.type.namedtype.const": {"blob_name": "pyasn1.type.namedtype", "blob_size": 10491, "input_size": 13174}, "module.pyasn1.type.namedval.const": {"blob_name": "pyasn1.type.namedval", "blob_size": 2385, "input_size": 3598}, "module.pyasn1.type.opentype.const": {"blob_name": "pyasn1.type.opentype", "blob_size": 2606, "input_size": 3192}, "module.pyasn1.type.tag.const": {"blob_name": "pyasn1.type.tag", "blob_size": 5634, "input_size": 7456}, "module.pyasn1.type.tagmap.const": {"blob_name": "pyasn1.type.tagmap", "blob_size": 1961, "input_size": 2782}, "module.pyasn1.type.univ.const": {"blob_name": "pyasn1.type.univ", "blob_size": 51289, "input_size": 60055}, "module.pyasn1.type.useful.const": {"blob_name": "pyasn1.type.useful", "blob_size": 2132, "input_size": 3624}, "module.pyasn1_modules.const": {"blob_name": "pyasn1_modules", "blob_size": 255, "input_size": 488}, "module.pyasn1_modules.rfc2459.const": {"blob_name": "pyasn1_modules.rfc2459", "blob_size": 10347, "input_size": 17852}, "module.pygame.__pyinstaller.const": {"blob_name": "pygame.__pyinstaller", "blob_size": 333, "input_size": 606}, "module.pygame.__pyinstaller.hook-pygame.const": {"blob_name": "pygame.__pyinstaller.hook-pygame", "blob_size": 723, "input_size": 1166}, "module.pygame._camera_opencv.const": {"blob_name": "pygame._camera_opencv", "blob_size": 2242, "input_size": 3753}, "module.pygame._camera_vidcapture.const": {"blob_name": "pygame._camera_vidcapture", "blob_size": 2304, "input_size": 3201}, "module.pygame._sdl2.const": {"blob_name": "pygame._sdl2", "blob_size": 306, "input_size": 606}, "module.pygame.camera.const": {"blob_name": "pygame.camera", "blob_size": 2082, "input_size": 3444}, "module.pygame.colordict.const": {"blob_name": "pygame.colordict", "blob_size": 14758, "input_size": 15222}, "module.pygame.const": {"blob_name": "pygame", "blob_size": 3120, "input_size": 5826}, "module.pygame.cursors.const": {"blob_name": "pygame.cursors", "blob_size": 7667, "input_size": 9615}, "module.pygame.docs.__main__.const": {"blob_name": "pygame.docs.__main__", "blob_size": 415, "input_size": 786}, "module.pygame.docs.const": {"blob_name": "pygame.docs", "blob_size": 195, "input_size": 378}, "module.pygame.draw_py.const": {"blob_name": "pygame.draw_py", "blob_size": 4483, "input_size": 5886}, "module.pygame.examples.aacircle.const": {"blob_name": "pygame.examples.aacircle", "blob_size": 499, "input_size": 1164}, "module.pygame.examples.aliens.const": {"blob_name": "pygame.examples.aliens", "blob_size": 4116, "input_size": 6929}, "module.pygame.examples.arraydemo.const": {"blob_name": "pygame.examples.arraydemo", "blob_size": 1392, "input_size": 2704}, "module.pygame.examples.audiocapture.const": {"blob_name": "pygame.examples.audiocapture", "blob_size": 1134, "input_size": 1717}, "module.pygame.examples.blend_fill.const": {"blob_name": "pygame.examples.blend_fill", "blob_size": 1240, "input_size": 2281}, "module.pygame.examples.blit_blends.const": {"blob_name": "pygame.examples.blit_blends", "blob_size": 1644, "input_size": 3088}, "module.pygame.examples.camera.const": {"blob_name": "pygame.examples.camera", "blob_size": 1232, "input_size": 2161}, "module.pygame.examples.chimp.const": {"blob_name": "pygame.examples.chimp", "blob_size": 2701, "input_size": 4763}, "module.pygame.examples.const": {"blob_name": "pygame.examples", "blob_size": 294, "input_size": 541}, "module.pygame.examples.cursors.const": {"blob_name": "pygame.examples.cursors", "blob_size": 2672, "input_size": 4351}, "module.pygame.examples.dropevent.const": {"blob_name": "pygame.examples.dropevent", "blob_size": 870, "input_size": 1621}, "module.pygame.examples.eventlist.const": {"blob_name": "pygame.examples.eventlist", "blob_size": 2322, "input_size": 4061}, "module.pygame.examples.font_viewer.const": {"blob_name": "pygame.examples.font_viewer", "blob_size": 4340, "input_size": 6282}, "module.pygame.examples.fonty.const": {"blob_name": "pygame.examples.fonty", "blob_size": 841, "input_size": 1467}, "module.pygame.examples.freetype_misc.const": {"blob_name": "pygame.examples.freetype_misc", "blob_size": 1767, "input_size": 3035}, "module.pygame.examples.glcube.const": {"blob_name": "pygame.examples.glcube", "blob_size": 7261, "input_size": 10479}, "module.pygame.examples.go_over_there.const": {"blob_name": "pygame.examples.go_over_there", "blob_size": 1203, "input_size": 2216}, "module.pygame.examples.grid.const": {"blob_name": "pygame.examples.grid", "blob_size": 730, "input_size": 1634}, "module.pygame.examples.headless_no_windows_needed.const": {"blob_name": "pygame.examples.headless_no_windows_needed", "blob_size": 827, "input_size": 1285}, "module.pygame.examples.joystick.const": {"blob_name": "pygame.examples.joystick", "blob_size": 1372, "input_size": 2640}, "module.pygame.examples.liquid.const": {"blob_name": "pygame.examples.liquid", "blob_size": 1147, "input_size": 1924}, "module.pygame.examples.mask.const": {"blob_name": "pygame.examples.mask", "blob_size": 2501, "input_size": 3544}, "module.pygame.examples.midi.const": {"blob_name": "pygame.examples.midi", "blob_size": 12263, "input_size": 15992}, "module.pygame.examples.moveit.const": {"blob_name": "pygame.examples.moveit", "blob_size": 1300, "input_size": 2416}, "module.pygame.examples.music_drop_fade.const": {"blob_name": "pygame.examples.music_drop_fade", "blob_size": 3803, "input_size": 5690}, "module.pygame.examples.pixelarray.const": {"blob_name": "pygame.examples.pixelarray", "blob_size": 1026, "input_size": 2014}, "module.pygame.examples.playmus.const": {"blob_name": "pygame.examples.playmus", "blob_size": 2074, "input_size": 3591}, "module.pygame.examples.resizing_new.const": {"blob_name": "pygame.examples.resizing_new", "blob_size": 480, "input_size": 1199}, "module.pygame.examples.scaletest.const": {"blob_name": "pygame.examples.scaletest", "blob_size": 1323, "input_size": 2306}, "module.pygame.examples.scrap_clipboard.const": {"blob_name": "pygame.examples.scrap_clipboard", "blob_size": 1470, "input_size": 2702}, "module.pygame.examples.scroll.const": {"blob_name": "pygame.examples.scroll", "blob_size": 2037, "input_size": 3356}, "module.pygame.examples.setmodescale.const": {"blob_name": "pygame.examples.setmodescale", "blob_size": 1110, "input_size": 2006}, "module.pygame.examples.sound.const": {"blob_name": "pygame.examples.sound", "blob_size": 791, "input_size": 1238}, "module.pygame.examples.sound_array_demos.const": {"blob_name": "pygame.examples.sound_array_demos", "blob_size": 1840, "input_size": 2733}, "module.pygame.examples.sprite_texture.const": {"blob_name": "pygame.examples.sprite_texture", "blob_size": 1376, "input_size": 2757}, "module.pygame.examples.stars.const": {"blob_name": "pygame.examples.stars", "blob_size": 1270, "input_size": 2276}, "module.pygame.examples.testsprite.const": {"blob_name": "pygame.examples.testsprite", "blob_size": 2314, "input_size": 3967}, "module.pygame.examples.textinput.const": {"blob_name": "pygame.examples.textinput", "blob_size": 2232, "input_size": 3888}, "module.pygame.examples.vgrade.const": {"blob_name": "pygame.examples.vgrade", "blob_size": 2125, "input_size": 3203}, "module.pygame.examples.video.const": {"blob_name": "pygame.examples.video", "blob_size": 1628, "input_size": 3350}, "module.pygame.fastevent.const": {"blob_name": "pygame.fastevent", "blob_size": 1161, "input_size": 1665}, "module.pygame.freetype.const": {"blob_name": "pygame.freetype", "blob_size": 2176, "input_size": 2367}, "module.pygame.ftfont.const": {"blob_name": "pygame.ftfont", "blob_size": 4221, "input_size": 5657}, "module.pygame.locals.const": {"blob_name": "pygame.locals", "blob_size": 308, "input_size": 551}, "module.pygame.macosx.const": {"blob_name": "pygame.macosx", "blob_size": 251, "input_size": 510}, "module.pygame.midi.const": {"blob_name": "pygame.midi", "blob_size": 17666, "input_size": 20054}, "module.pygame.pkgdata.const": {"blob_name": "pygame.pkgdata", "blob_size": 1785, "input_size": 2132}, "module.pygame.sndarray.const": {"blob_name": "pygame.sndarray", "blob_size": 2443, "input_size": 2975}, "module.pygame.sprite.const": {"blob_name": "pygame.sprite", "blob_size": 33332, "input_size": 38134}, "module.pygame.surfarray.const": {"blob_name": "pygame.surfarray", "blob_size": 10168, "input_size": 11644}, "module.pygame.sysfont.const": {"blob_name": "pygame.sysfont", "blob_size": 6557, "input_size": 8483}, "module.pygame.tests.__main__.const": {"blob_name": "pygame.tests.__main__", "blob_size": 1641, "input_size": 2446}, "module.pygame.tests.base_test.const": {"blob_name": "pygame.tests.base_test", "blob_size": 6070, "input_size": 9011}, "module.pygame.tests.blit_test.const": {"blob_name": "pygame.tests.blit_test", "blob_size": 2827, "input_size": 4361}, "module.pygame.tests.bufferproxy_test.const": {"blob_name": "pygame.tests.bufferproxy_test", "blob_size": 4333, "input_size": 6924}, "module.pygame.tests.camera_test.const": {"blob_name": "pygame.tests.camera_test", "blob_size": 773, "input_size": 1446}, "module.pygame.tests.color_test.const": {"blob_name": "pygame.tests.color_test", "blob_size": 13462, "input_size": 21651}, "module.pygame.tests.const": {"blob_name": "pygame.tests", "blob_size": 1021, "input_size": 1310}, "module.pygame.tests.constants_test.const": {"blob_name": "pygame.tests.constants_test", "blob_size": 4832, "input_size": 6508}, "module.pygame.tests.controller_test.const": {"blob_name": "pygame.tests.controller_test", "blob_size": 3804, "input_size": 6083}, "module.pygame.tests.cursors_test.const": {"blob_name": "pygame.tests.cursors_test", "blob_size": 1735, "input_size": 2799}, "module.pygame.tests.display_test.const": {"blob_name": "pygame.tests.display_test", "blob_size": 10533, "input_size": 15851}, "module.pygame.tests.docs_test.const": {"blob_name": "pygame.tests.docs_test", "blob_size": 861, "input_size": 1425}, "module.pygame.tests.draw_test.const": {"blob_name": "pygame.tests.draw_test", "blob_size": 57271, "input_size": 70764}, "module.pygame.tests.event_test.const": {"blob_name": "pygame.tests.event_test", "blob_size": 10849, "input_size": 16158}, "module.pygame.tests.font_test.const": {"blob_name": "pygame.tests.font_test", "blob_size": 7952, "input_size": 12927}, "module.pygame.tests.freetype_tags.const": {"blob_name": "pygame.tests.freetype_tags", "blob_size": 219, "input_size": 425}, "module.pygame.tests.freetype_test.const": {"blob_name": "pygame.tests.freetype_test", "blob_size": 12634, "input_size": 19898}, "module.pygame.tests.ftfont_tags.const": {"blob_name": "pygame.tests.ftfont_tags", "blob_size": 213, "input_size": 419}, "module.pygame.tests.ftfont_test.const": {"blob_name": "pygame.tests.ftfont_test", "blob_size": 293, "input_size": 597}, "module.pygame.tests.gfxdraw_test.const": {"blob_name": "pygame.tests.gfxdraw_test", "blob_size": 7490, "input_size": 10037}, "module.pygame.tests.image__save_gl_surface_test.const": {"blob_name": "pygame.tests.image__save_gl_surface_test", "blob_size": 922, "input_size": 1641}, "module.pygame.tests.image_tags.const": {"blob_name": "pygame.tests.image_tags", "blob_size": 214, "input_size": 421}, "module.pygame.tests.image_test.const": {"blob_name": "pygame.tests.image_test", "blob_size": 10745, "input_size": 16077}, "module.pygame.tests.imageext_tags.const": {"blob_name": "pygame.tests.imageext_tags", "blob_size": 223, "input_size": 430}, "module.pygame.tests.imageext_test.const": {"blob_name": "pygame.tests.imageext_test", "blob_size": 1902, "input_size": 3153}, "module.pygame.tests.joystick_test.const": {"blob_name": "pygame.tests.joystick_test", "blob_size": 1820, "input_size": 2793}, "module.pygame.tests.key_test.const": {"blob_name": "pygame.tests.key_test", "blob_size": 3737, "input_size": 5498}, "module.pygame.tests.locals_test.const": {"blob_name": "pygame.tests.locals_test", "blob_size": 531, "input_size": 947}, "module.pygame.tests.mask_test.const": {"blob_name": "pygame.tests.mask_test", "blob_size": 79753, "input_size": 89006}, "module.pygame.tests.math_test.const": {"blob_name": "pygame.tests.math_test", "blob_size": 20622, "input_size": 31783}, "module.pygame.tests.midi_test.const": {"blob_name": "pygame.tests.midi_test", "blob_size": 5786, "input_size": 9010}, "module.pygame.tests.mixer_music_tags.const": {"blob_name": "pygame.tests.mixer_music_tags", "blob_size": 232, "input_size": 439}, "module.pygame.tests.mixer_music_test.const": {"blob_name": "pygame.tests.mixer_music_test", "blob_size": 4619, "input_size": 7321}, "module.pygame.tests.mixer_tags.const": {"blob_name": "pygame.tests.mixer_tags", "blob_size": 214, "input_size": 421}, "module.pygame.tests.mixer_test.const": {"blob_name": "pygame.tests.mixer_test", "blob_size": 16546, "input_size": 23400}, "module.pygame.tests.mouse_test.const": {"blob_name": "pygame.tests.mouse_test", "blob_size": 4009, "input_size": 6003}, "module.pygame.tests.pixelarray_test.const": {"blob_name": "pygame.tests.pixelarray_test", "blob_size": 12165, "input_size": 19715}, "module.pygame.tests.pixelcopy_test.const": {"blob_name": "pygame.tests.pixelcopy_test", "blob_size": 6062, "input_size": 9431}, "module.pygame.tests.rect_test.const": {"blob_name": "pygame.tests.rect_test", "blob_size": 35266, "input_size": 46856}, "module.pygame.tests.run_tests__tests.all_ok.const": {"blob_name": "pygame.tests.run_tests__tests.all_ok", "blob_size": 520, "input_size": 821}, "module.pygame.tests.run_tests__tests.all_ok.fake_2_test.const": {"blob_name": "pygame.tests.run_tests__tests.all_ok.fake_2_test", "blob_size": 726, "input_size": 1217}, "module.pygame.tests.run_tests__tests.all_ok.fake_3_test.const": {"blob_name": "pygame.tests.run_tests__tests.all_ok.fake_3_test", "blob_size": 726, "input_size": 1217}, "module.pygame.tests.run_tests__tests.all_ok.fake_4_test.const": {"blob_name": "pygame.tests.run_tests__tests.all_ok.fake_4_test", "blob_size": 726, "input_size": 1217}, "module.pygame.tests.run_tests__tests.all_ok.fake_5_test.const": {"blob_name": "pygame.tests.run_tests__tests.all_ok.fake_5_test", "blob_size": 726, "input_size": 1217}, "module.pygame.tests.run_tests__tests.all_ok.fake_6_test.const": {"blob_name": "pygame.tests.run_tests__tests.all_ok.fake_6_test", "blob_size": 726, "input_size": 1217}, "module.pygame.tests.run_tests__tests.all_ok.no_assertions__ret_code_of_1__test.const": {"blob_name": "pygame.tests.run_tests__tests.all_ok.no_assertions__ret_code_of_1__test", "blob_size": 780, "input_size": 1246}, "module.pygame.tests.run_tests__tests.all_ok.zero_tests_test.const": {"blob_name": "pygame.tests.run_tests__tests.all_ok.zero_tests_test", "blob_size": 430, "input_size": 714}, "module.pygame.tests.run_tests__tests.const": {"blob_name": "pygame.tests.run_tests__tests", "blob_size": 414, "input_size": 688}, "module.pygame.tests.run_tests__tests.everything.const": {"blob_name": "pygame.tests.run_tests__tests.everything", "blob_size": 544, "input_size": 845}, "module.pygame.tests.run_tests__tests.everything.fake_2_test.const": {"blob_name": "pygame.tests.run_tests__tests.everything.fake_2_test", "blob_size": 738, "input_size": 1229}, "module.pygame.tests.run_tests__tests.everything.incomplete_todo_test.const": {"blob_name": "pygame.tests.run_tests__tests.everything.incomplete_todo_test", "blob_size": 785, "input_size": 1276}, "module.pygame.tests.run_tests__tests.everything.magic_tag_test.const": {"blob_name": "pygame.tests.run_tests__tests.everything.magic_tag_test", "blob_size": 716, "input_size": 1207}, "module.pygame.tests.run_tests__tests.everything.sleep_test.const": {"blob_name": "pygame.tests.run_tests__tests.everything.sleep_test", "blob_size": 550, "input_size": 962}, "module.pygame.tests.run_tests__tests.exclude.const": {"blob_name": "pygame.tests.run_tests__tests.exclude", "blob_size": 526, "input_size": 827}, "module.pygame.tests.run_tests__tests.exclude.fake_2_test.const": {"blob_name": "pygame.tests.run_tests__tests.exclude.fake_2_test", "blob_size": 729, "input_size": 1220}, "module.pygame.tests.run_tests__tests.exclude.invisible_tag_test.const": {"blob_name": "pygame.tests.run_tests__tests.exclude.invisible_tag_test", "blob_size": 771, "input_size": 1288}, "module.pygame.tests.run_tests__tests.exclude.magic_tag_test.const": {"blob_name": "pygame.tests.run_tests__tests.exclude.magic_tag_test", "blob_size": 707, "input_size": 1198}, "module.pygame.tests.run_tests__tests.failures1.const": {"blob_name": "pygame.tests.run_tests__tests.failures1", "blob_size": 538, "input_size": 839}, "module.pygame.tests.run_tests__tests.failures1.fake_2_test.const": {"blob_name": "pygame.tests.run_tests__tests.failures1.fake_2_test", "blob_size": 735, "input_size": 1226}, "module.pygame.tests.run_tests__tests.failures1.fake_3_test.const": {"blob_name": "pygame.tests.run_tests__tests.failures1.fake_3_test", "blob_size": 735, "input_size": 1226}, "module.pygame.tests.run_tests__tests.failures1.fake_4_test.const": {"blob_name": "pygame.tests.run_tests__tests.failures1.fake_4_test", "blob_size": 754, "input_size": 1258}, "module.pygame.tests.run_tests__tests.incomplete.const": {"blob_name": "pygame.tests.run_tests__tests.incomplete", "blob_size": 544, "input_size": 845}, "module.pygame.tests.run_tests__tests.incomplete.fake_2_test.const": {"blob_name": "pygame.tests.run_tests__tests.incomplete.fake_2_test", "blob_size": 764, "input_size": 1268}, "module.pygame.tests.run_tests__tests.incomplete.fake_3_test.const": {"blob_name": "pygame.tests.run_tests__tests.incomplete.fake_3_test", "blob_size": 738, "input_size": 1229}, "module.pygame.tests.run_tests__tests.incomplete_todo.const": {"blob_name": "pygame.tests.run_tests__tests.incomplete_todo", "blob_size": 574, "input_size": 875}, "module.pygame.tests.run_tests__tests.incomplete_todo.fake_2_test.const": {"blob_name": "pygame.tests.run_tests__tests.incomplete_todo.fake_2_test", "blob_size": 773, "input_size": 1264}, "module.pygame.tests.run_tests__tests.incomplete_todo.fake_3_test.const": {"blob_name": "pygame.tests.run_tests__tests.incomplete_todo.fake_3_test", "blob_size": 753, "input_size": 1244}, "module.pygame.tests.run_tests__tests.infinite_loop.const": {"blob_name": "pygame.tests.run_tests__tests.infinite_loop", "blob_size": 562, "input_size": 863}, "module.pygame.tests.run_tests__tests.infinite_loop.fake_1_test.const": {"blob_name": "pygame.tests.run_tests__tests.infinite_loop.fake_1_test", "blob_size": 747, "input_size": 1238}, "module.pygame.tests.run_tests__tests.infinite_loop.fake_2_test.const": {"blob_name": "pygame.tests.run_tests__tests.infinite_loop.fake_2_test", "blob_size": 747, "input_size": 1238}, "module.pygame.tests.run_tests__tests.print_stderr.const": {"blob_name": "pygame.tests.run_tests__tests.print_stderr", "blob_size": 556, "input_size": 857}, "module.pygame.tests.run_tests__tests.print_stderr.fake_2_test.const": {"blob_name": "pygame.tests.run_tests__tests.print_stderr.fake_2_test", "blob_size": 744, "input_size": 1235}, "module.pygame.tests.run_tests__tests.print_stderr.fake_3_test.const": {"blob_name": "pygame.tests.run_tests__tests.print_stderr.fake_3_test", "blob_size": 798, "input_size": 1353}, "module.pygame.tests.run_tests__tests.print_stderr.fake_4_test.const": {"blob_name": "pygame.tests.run_tests__tests.print_stderr.fake_4_test", "blob_size": 763, "input_size": 1267}, "module.pygame.tests.run_tests__tests.print_stdout.const": {"blob_name": "pygame.tests.run_tests__tests.print_stdout", "blob_size": 556, "input_size": 857}, "module.pygame.tests.run_tests__tests.print_stdout.fake_2_test.const": {"blob_name": "pygame.tests.run_tests__tests.print_stdout.fake_2_test", "blob_size": 744, "input_size": 1235}, "module.pygame.tests.run_tests__tests.print_stdout.fake_3_test.const": {"blob_name": "pygame.tests.run_tests__tests.print_stdout.fake_3_test", "blob_size": 830, "input_size": 1398}, "module.pygame.tests.run_tests__tests.print_stdout.fake_4_test.const": {"blob_name": "pygame.tests.run_tests__tests.print_stdout.fake_4_test", "blob_size": 763, "input_size": 1267}, "module.pygame.tests.run_tests__tests.run_tests__test.const": {"blob_name": "pygame.tests.run_tests__tests.run_tests__test", "blob_size": 1713, "input_size": 2950}, "module.pygame.tests.run_tests__tests.timeout.const": {"blob_name": "pygame.tests.run_tests__tests.timeout", "blob_size": 526, "input_size": 827}, "module.pygame.tests.run_tests__tests.timeout.fake_2_test.const": {"blob_name": "pygame.tests.run_tests__tests.timeout.fake_2_test", "blob_size": 729, "input_size": 1220}, "module.pygame.tests.run_tests__tests.timeout.sleep_test.const": {"blob_name": "pygame.tests.run_tests__tests.timeout.sleep_test", "blob_size": 541, "input_size": 953}, "module.pygame.tests.rwobject_test.const": {"blob_name": "pygame.tests.rwobject_test", "blob_size": 2653, "input_size": 3986}, "module.pygame.tests.scrap_tags.const": {"blob_name": "pygame.tests.scrap_tags", "blob_size": 166, "input_size": 320}, "module.pygame.tests.scrap_test.const": {"blob_name": "pygame.tests.scrap_test", "blob_size": 5347, "input_size": 7778}, "module.pygame.tests.sndarray_tags.const": {"blob_name": "pygame.tests.sndarray_tags", "blob_size": 219, "input_size": 426}, "module.pygame.tests.sndarray_test.const": {"blob_name": "pygame.tests.sndarray_test", "blob_size": 2223, "input_size": 3542}, "module.pygame.tests.sprite_test.const": {"blob_name": "pygame.tests.sprite_test", "blob_size": 12408, "input_size": 17798}, "module.pygame.tests.surface_test.const": {"blob_name": "pygame.tests.surface_test", "blob_size": 36819, "input_size": 51471}, "module.pygame.tests.surfarray_tags.const": {"blob_name": "pygame.tests.surfarray_tags", "blob_size": 225, "input_size": 432}, "module.pygame.tests.surfarray_test.const": {"blob_name": "pygame.tests.surfarray_test", "blob_size": 6187, "input_size": 9586}, "module.pygame.tests.surflock_test.const": {"blob_name": "pygame.tests.surflock_test", "blob_size": 860, "input_size": 1624}, "module.pygame.tests.sysfont_test.const": {"blob_name": "pygame.tests.sysfont_test", "blob_size": 989, "input_size": 1713}, "module.pygame.tests.test_utils.arrinter.const": {"blob_name": "pygame.tests.test_utils.arrinter", "blob_size": 4287, "input_size": 7901}, "module.pygame.tests.test_utils.async_sub.const": {"blob_name": "pygame.tests.test_utils.async_sub", "blob_size": 2503, "input_size": 4394}, "module.pygame.tests.test_utils.buftools.const": {"blob_name": "pygame.tests.test_utils.buftools", "blob_size": 6590, "input_size": 11057}, "module.pygame.tests.test_utils.const": {"blob_name": "pygame.tests.test_utils", "blob_size": 2543, "input_size": 4149}, "module.pygame.tests.test_utils.endian.const": {"blob_name": "pygame.tests.test_utils.endian", "blob_size": 361, "input_size": 622}, "module.pygame.tests.test_utils.png.const": {"blob_name": "pygame.tests.test_utils.png", "blob_size": 83089, "input_size": 96084}, "module.pygame.tests.test_utils.run_tests.const": {"blob_name": "pygame.tests.test_utils.run_tests", "blob_size": 6355, "input_size": 7901}, "module.pygame.tests.test_utils.test_machinery.const": {"blob_name": "pygame.tests.test_utils.test_machinery", "blob_size": 1260, "input_size": 2168}, "module.pygame.tests.test_utils.test_runner.const": {"blob_name": "pygame.tests.test_utils.test_runner", "blob_size": 3801, "input_size": 5914}, "module.pygame.tests.threads_test.const": {"blob_name": "pygame.tests.threads_test", "blob_size": 3239, "input_size": 5111}, "module.pygame.tests.time_test.const": {"blob_name": "pygame.tests.time_test", "blob_size": 3649, "input_size": 5580}, "module.pygame.tests.touch_test.const": {"blob_name": "pygame.tests.touch_test", "blob_size": 1740, "input_size": 2975}, "module.pygame.tests.transform_test.const": {"blob_name": "pygame.tests.transform_test", "blob_size": 12134, "input_size": 16879}, "module.pygame.tests.version_test.const": {"blob_name": "pygame.tests.version_test", "blob_size": 1036, "input_size": 1703}, "module.pygame.tests.video_test.const": {"blob_name": "pygame.tests.video_test", "blob_size": 715, "input_size": 1343}, "module.pygame.threads.const": {"blob_name": "pygame.threads", "blob_size": 3609, "input_size": 5255}, "module.pygame.version.const": {"blob_name": "pygame.version", "blob_size": 1342, "input_size": 2166}, "module.pyperclip.__main__.const": {"blob_name": "pyperclip.__main__", "blob_size": 590, "input_size": 944}, "module.pyperclip.const": {"blob_name": "pyperclip", "blob_size": 11424, "input_size": 14554}, "module.pyreadline3.clipboard.const": {"blob_name": "pyreadline3.clipboard", "blob_size": 1049, "input_size": 1739}, "module.pyreadline3.clipboard.ironpython_clipboard.const": {"blob_name": "pyreadline3.clipboard.ironpython_clipboard", "blob_size": 413, "input_size": 749}, "module.pyreadline3.clipboard.no_clipboard.const": {"blob_name": "pyreadline3.clipboard.no_clipboard", "blob_size": 256, "input_size": 462}, "module.pyreadline3.clipboard.win32_clipboard.const": {"blob_name": "pyreadline3.clipboard.win32_clipboard", "blob_size": 1083, "input_size": 1896}, "module.pyreadline3.console.ansi.const": {"blob_name": "pyreadline3.console.ansi", "blob_size": 1779, "input_size": 3041}, "module.pyreadline3.console.console.const": {"blob_name": "pyreadline3.console.console", "blob_size": 8003, "input_size": 13291}, "module.pyreadline3.console.const": {"blob_name": "pyreadline3.console", "blob_size": 597, "input_size": 979}, "module.pyreadline3.console.event.const": {"blob_name": "pyreadline3.console.event", "blob_size": 820, "input_size": 1529}, "module.pyreadline3.console.ironpython_console.const": {"blob_name": "pyreadline3.console.ironpython_console", "blob_size": 5422, "input_size": 9118}, "module.pyreadline3.const": {"blob_name": "pyreadline3", "blob_size": 529, "input_size": 861}, "module.pyreadline3.error.const": {"blob_name": "pyreadline3.error", "blob_size": 367, "input_size": 690}, "module.pyreadline3.keysyms.common.const": {"blob_name": "pyreadline3.keysyms.common", "blob_size": 1824, "input_size": 3089}, "module.pyreadline3.keysyms.const": {"blob_name": "pyreadline3.keysyms", "blob_size": 595, "input_size": 981}, "module.pyreadline3.keysyms.ironpython_keysyms.const": {"blob_name": "pyreadline3.keysyms.ironpython_keysyms", "blob_size": 1692, "input_size": 3623}, "module.pyreadline3.keysyms.keysyms.const": {"blob_name": "pyreadline3.keysyms.keysyms", "blob_size": 1914, "input_size": 4440}, "module.pyreadline3.keysyms.winconstants.const": {"blob_name": "pyreadline3.keysyms.winconstants", "blob_size": 2447, "input_size": 5158}, "module.pyreadline3.lineeditor.const": {"blob_name": "pyreadline3.lineeditor", "blob_size": 373, "input_size": 659}, "module.pyreadline3.lineeditor.history.const": {"blob_name": "pyreadline3.lineeditor.history", "blob_size": 3734, "input_size": 5571}, "module.pyreadline3.lineeditor.lineobj.const": {"blob_name": "pyreadline3.lineeditor.lineobj", "blob_size": 6826, "input_size": 10865}, "module.pyreadline3.lineeditor.wordmatcher.const": {"blob_name": "pyreadline3.lineeditor.wordmatcher", "blob_size": 886, "input_size": 1497}, "module.pyreadline3.logger.const": {"blob_name": "pyreadline3.logger", "blob_size": 1085, "input_size": 2122}, "module.pyreadline3.modes.basemode.const": {"blob_name": "pyreadline3.modes.basemode", "blob_size": 9594, "input_size": 14008}, "module.pyreadline3.modes.const": {"blob_name": "pyreadline3.modes", "blob_size": 453, "input_size": 813}, "module.pyreadline3.modes.emacs.const": {"blob_name": "pyreadline3.modes.emacs", "blob_size": 15640, "input_size": 21358}, "module.pyreadline3.modes.notemacs.const": {"blob_name": "pyreadline3.modes.notemacs", "blob_size": 16526, "input_size": 21591}, "module.pyreadline3.modes.vi.const": {"blob_name": "pyreadline3.modes.vi", "blob_size": 8244, "input_size": 15009}, "module.pyreadline3.py3k_compat.const": {"blob_name": "pyreadline3.py3k_compat", "blob_size": 338, "input_size": 705}, "module.pyreadline3.release.const": {"blob_name": "pyreadline3.release", "blob_size": 2084, "input_size": 2577}, "module.pyreadline3.rlmain.const": {"blob_name": "pyreadline3.rlmain", "blob_size": 10722, "input_size": 15343}, "module.pyreadline3.unicode_helper.const": {"blob_name": "pyreadline3.unicode_helper", "blob_size": 475, "input_size": 884}, "module.pythoncom.const": {"blob_name": "pythoncom", "blob_size": 158, "input_size": 312}, "module.pywin.const": {"blob_name": "pywin", "blob_size": 243, "input_size": 476}, "module.pywin.dialogs.const": {"blob_name": "pywin.dialogs", "blob_size": 286, "input_size": 533}, "module.pywin.dialogs.list.const": {"blob_name": "pywin.dialogs.list", "blob_size": 1873, "input_size": 3356}, "module.pywin.dialogs.status.const": {"blob_name": "pywin.dialogs.status", "blob_size": 2749, "input_size": 4820}, "module.pywin.mfc.const": {"blob_name": "pywin.mfc", "blob_size": 270, "input_size": 517}, "module.pywin.mfc.dialog.const": {"blob_name": "pywin.mfc.dialog", "blob_size": 3639, "input_size": 5991}, "module.pywin.mfc.object.const": {"blob_name": "pywin.mfc.object", "blob_size": 881, "input_size": 1591}, "module.pywin.mfc.thread.const": {"blob_name": "pywin.mfc.thread", "blob_size": 532, "input_size": 1008}, "module.pywin.mfc.window.const": {"blob_name": "pywin.mfc.window", "blob_size": 684, "input_size": 1301}, "module.pywin32_system32.const": {"blob_name": "pywin32_system32", "blob_size": 134, "input_size": 251}, "module.pywintypes.const": {"blob_name": "pywintypes", "blob_size": 698, "input_size": 1271}, "module.qtpy.QtCore.const": {"blob_name": "qtpy.QtCore", "blob_size": 1369, "input_size": 2422}, "module.qtpy.QtDataVisualization.const": {"blob_name": "qtpy.QtDataVisualization", "blob_size": 634, "input_size": 958}, "module.qtpy.QtGui.const": {"blob_name": "qtpy.QtGui", "blob_size": 1985, "input_size": 3138}, "module.qtpy.QtWidgets.const": {"blob_name": "qtpy.QtWidgets", "blob_size": 1634, "input_size": 2553}, "module.qtpy._utils.const": {"blob_name": "qtpy._utils", "blob_size": 1623, "input_size": 2162}, "module.qtpy.const": {"blob_name": "qtpy", "blob_size": 4448, "input_size": 6392}, "module.qtpy.enums_compat.const": {"blob_name": "qtpy.enums_compat", "blob_size": 660, "input_size": 977}, "module.qtpy.sip.const": {"blob_name": "qtpy.sip", "blob_size": 289, "input_size": 465}, "module.readline.const": {"blob_name": "readline", "blob_size": 1322, "input_size": 2046}, "module.reportlab.const": {"blob_name": "reportlab", "blob_size": 783, "input_size": 1312}, "module.reportlab.graphics.charts.areas.const": {"blob_name": "reportlab.graphics.charts.areas", "blob_size": 1749, "input_size": 2559}, "module.reportlab.graphics.charts.const": {"blob_name": "reportlab.graphics.charts", "blob_size": 431, "input_size": 744}, "module.reportlab.graphics.charts.legends.const": {"blob_name": "reportlab.graphics.charts.legends", "blob_size": 9562, "input_size": 12710}, "module.reportlab.graphics.charts.piecharts.const": {"blob_name": "reportlab.graphics.charts.piecharts", "blob_size": 17382, "input_size": 24528}, "module.reportlab.graphics.charts.textlabels.const": {"blob_name": "reportlab.graphics.charts.textlabels", "blob_size": 8415, "input_size": 11558}, "module.reportlab.graphics.charts.utils.const": {"blob_name": "reportlab.graphics.charts.utils", "blob_size": 4998, "input_size": 7889}, "module.reportlab.graphics.charts.utils3d.const": {"blob_name": "reportlab.graphics.charts.utils3d", "blob_size": 2667, "input_size": 3762}, "module.reportlab.graphics.const": {"blob_name": "reportlab.graphics", "blob_size": 389, "input_size": 675}, "module.reportlab.graphics.renderPDF.const": {"blob_name": "reportlab.graphics.renderPDF", "blob_size": 5266, "input_size": 8610}, "module.reportlab.graphics.renderPM.const": {"blob_name": "reportlab.graphics.renderPM", "blob_size": 12269, "input_size": 19158}, "module.reportlab.graphics.renderPS.const": {"blob_name": "reportlab.graphics.renderPS", "blob_size": 13062, "input_size": 19820}, "module.reportlab.graphics.renderSVG.const": {"blob_name": "reportlab.graphics.renderSVG", "blob_size": 13623, "input_size": 20894}, "module.reportlab.graphics.renderbase.const": {"blob_name": "reportlab.graphics.renderbase", "blob_size": 5575, "input_size": 8441}, "module.reportlab.graphics.shapes.const": {"blob_name": "reportlab.graphics.shapes", "blob_size": 19955, "input_size": 29599}, "module.reportlab.graphics.testshapes.const": {"blob_name": "reportlab.graphics.testshapes", "blob_size": 8161, "input_size": 11962}, "module.reportlab.graphics.transform.const": {"blob_name": "reportlab.graphics.transform", "blob_size": 952, "input_size": 1642}, "module.reportlab.graphics.utils.const": {"blob_name": "reportlab.graphics.utils", "blob_size": 5255, "input_size": 7616}, "module.reportlab.graphics.widgetbase.const": {"blob_name": "reportlab.graphics.widgetbase", "blob_size": 12220, "input_size": 16646}, "module.reportlab.graphics.widgets.const": {"blob_name": "reportlab.graphics.widgets", "blob_size": 443, "input_size": 756}, "module.reportlab.graphics.widgets.flags.const": {"blob_name": "reportlab.graphics.widgets.flags", "blob_size": 8092, "input_size": 12365}, "module.reportlab.graphics.widgets.markers.const": {"blob_name": "reportlab.graphics.widgets.markers", "blob_size": 3559, "input_size": 5743}, "module.reportlab.graphics.widgets.signsandsymbols.const": {"blob_name": "reportlab.graphics.widgets.signsandsymbols", "blob_size": 7626, "input_size": 10668}, "module.reportlab.lib.PyFontify.const": {"blob_name": "reportlab.lib.PyFontify", "blob_size": 1957, "input_size": 2891}, "module.reportlab.lib.abag.const": {"blob_name": "reportlab.lib.abag", "blob_size": 760, "input_size": 1183}, "module.reportlab.lib.arciv.const": {"blob_name": "reportlab.lib.arciv", "blob_size": 2945, "input_size": 3641}, "module.reportlab.lib.attrmap.const": {"blob_name": "reportlab.lib.attrmap", "blob_size": 2957, "input_size": 4499}, "module.reportlab.lib.boxstuff.const": {"blob_name": "reportlab.lib.boxstuff", "blob_size": 1455, "input_size": 1856}, "module.reportlab.lib.colors.const": {"blob_name": "reportlab.lib.colors", "blob_size": 15813, "input_size": 24452}, "module.reportlab.lib.const": {"blob_name": "reportlab.lib", "blob_size": 320, "input_size": 619}, "module.reportlab.lib.corp.const": {"blob_name": "reportlab.lib.corp", "blob_size": 14821, "input_size": 18252}, "module.reportlab.lib.enums.const": {"blob_name": "reportlab.lib.enums", "blob_size": 228, "input_size": 440}, "module.reportlab.lib.fonts.const": {"blob_name": "reportlab.lib.fonts", "blob_size": 1599, "input_size": 2091}, "module.reportlab.lib.formatters.const": {"blob_name": "reportlab.lib.formatters", "blob_size": 1504, "input_size": 2531}, "module.reportlab.lib.geomutils.const": {"blob_name": "reportlab.lib.geomutils", "blob_size": 968, "input_size": 1217}, "module.reportlab.lib.logger.const": {"blob_name": "reportlab.lib.logger", "blob_size": 948, "input_size": 1709}, "module.reportlab.lib.normalDate.const": {"blob_name": "reportlab.lib.normalDate", "blob_size": 9200, "input_size": 13530}, "module.reportlab.lib.pagesizes.const": {"blob_name": "reportlab.lib.pagesizes", "blob_size": 809, "input_size": 1922}, "module.reportlab.lib.pdfencrypt.const": {"blob_name": "reportlab.lib.pdfencrypt", "blob_size": 10577, "input_size": 15174}, "module.reportlab.lib.rl_accel.const": {"blob_name": "reportlab.lib.rl_accel", "blob_size": 3060, "input_size": 5258}, "module.reportlab.lib.rl_safe_eval.const": {"blob_name": "reportlab.lib.rl_safe_eval", "blob_size": 18996, "input_size": 27050}, "module.reportlab.lib.rltempfile.const": {"blob_name": "reportlab.lib.rltempfile", "blob_size": 722, "input_size": 1102}, "module.reportlab.lib.rparsexml.const": {"blob_name": "reportlab.lib.rparsexml", "blob_size": 4873, "input_size": 6687}, "module.reportlab.lib.sequencer.const": {"blob_name": "reportlab.lib.sequencer", "blob_size": 4599, "input_size": 6884}, "module.reportlab.lib.styles.const": {"blob_name": "reportlab.lib.styles", "blob_size": 6155, "input_size": 8365}, "module.reportlab.lib.textsplit.const": {"blob_name": "reportlab.lib.textsplit", "blob_size": 4398, "input_size": 5453}, "module.reportlab.lib.units.const": {"blob_name": "reportlab.lib.units", "blob_size": 474, "input_size": 936}, "module.reportlab.lib.utils.const": {"blob_name": "reportlab.lib.utils", "blob_size": 15717, "input_size": 25583}, "module.reportlab.lib.validators.const": {"blob_name": "reportlab.lib.validators", "blob_size": 5456, "input_size": 8863}, "module.reportlab.pdfbase._fontdata.const": {"blob_name": "reportlab.pdfbase._fontdata", "blob_size": 5387, "input_size": 7229}, "module.reportlab.pdfbase._fontdata_enc_macexpert.const": {"blob_name": "reportlab.pdfbase._fontdata_enc_macexpert", "blob_size": 2333, "input_size": 2638}, "module.reportlab.pdfbase._fontdata_enc_macroman.const": {"blob_name": "reportlab.pdfbase._fontdata_enc_macroman", "blob_size": 1811, "input_size": 2211}, "module.reportlab.pdfbase._fontdata_enc_pdfdoc.const": {"blob_name": "reportlab.pdfbase._fontdata_enc_pdfdoc", "blob_size": 1976, "input_size": 2397}, "module.reportlab.pdfbase._fontdata_enc_standard.const": {"blob_name": "reportlab.pdfbase._fontdata_enc_standard", "blob_size": 1298, "input_size": 1639}, "module.reportlab.pdfbase._fontdata_enc_symbol.const": {"blob_name": "reportlab.pdfbase._fontdata_enc_symbol", "blob_size": 2072, "input_size": 2401}, "module.reportlab.pdfbase._fontdata_enc_winansi.const": {"blob_name": "reportlab.pdfbase._fontdata_enc_winansi", "blob_size": 1943, "input_size": 2359}, "module.reportlab.pdfbase._fontdata_enc_zapfdingbats.const": {"blob_name": "reportlab.pdfbase._fontdata_enc_zapfdingbats", "blob_size": 1356, "input_size": 1698}, "module.reportlab.pdfbase._fontdata_widths_courier.const": {"blob_name": "reportlab.pdfbase._fontdata_widths_courier", "blob_size": 2636, "input_size": 3058}, "module.reportlab.pdfbase._fontdata_widths_courierbold.const": {"blob_name": "reportlab.pdfbase._fontdata_widths_courierbold", "blob_size": 2644, "input_size": 3066}, "module.reportlab.pdfbase._fontdata_widths_courierboldoblique.const": {"blob_name": "reportlab.pdfbase._fontdata_widths_courierboldoblique", "blob_size": 2658, "input_size": 3080}, "module.reportlab.pdfbase._fontdata_widths_courieroblique.const": {"blob_name": "reportlab.pdfbase._fontdata_widths_courieroblique", "blob_size": 2650, "input_size": 3072}, "module.reportlab.pdfbase._fontdata_widths_helvetica.const": {"blob_name": "reportlab.pdfbase._fontdata_widths_helvetica", "blob_size": 2636, "input_size": 3053}, "module.reportlab.pdfbase._fontdata_widths_helveticabold.const": {"blob_name": "reportlab.pdfbase._fontdata_widths_helveticabold", "blob_size": 2648, "input_size": 3068}, "module.reportlab.pdfbase._fontdata_widths_helveticaboldoblique.const": {"blob_name": "reportlab.pdfbase._fontdata_widths_helveticaboldoblique", "blob_size": 2662, "input_size": 3082}, "module.reportlab.pdfbase._fontdata_widths_helveticaoblique.const": {"blob_name": "reportlab.pdfbase._fontdata_widths_helveticaoblique", "blob_size": 2650, "input_size": 3067}, "module.reportlab.pdfbase._fontdata_widths_symbol.const": {"blob_name": "reportlab.pdfbase._fontdata_widths_symbol", "blob_size": 2580, "input_size": 2904}, "module.reportlab.pdfbase._fontdata_widths_timesbold.const": {"blob_name": "reportlab.pdfbase._fontdata_widths_timesbold", "blob_size": 2638, "input_size": 3055}, "module.reportlab.pdfbase._fontdata_widths_timesbolditalic.const": {"blob_name": "reportlab.pdfbase._fontdata_widths_timesbolditalic", "blob_size": 2650, "input_size": 3067}, "module.reportlab.pdfbase._fontdata_widths_timesitalic.const": {"blob_name": "reportlab.pdfbase._fontdata_widths_timesitalic", "blob_size": 2642, "input_size": 3060}, "module.reportlab.pdfbase._fontdata_widths_timesroman.const": {"blob_name": "reportlab.pdfbase._fontdata_widths_timesroman", "blob_size": 2640, "input_size": 3056}, "module.reportlab.pdfbase._fontdata_widths_zapfdingbats.const": {"blob_name": "reportlab.pdfbase._fontdata_widths_zapfdingbats", "blob_size": 1898, "input_size": 2240}, "module.reportlab.pdfbase._glyphlist.const": {"blob_name": "reportlab.pdfbase._glyphlist", "blob_size": 72444, "input_size": 76225}, "module.reportlab.pdfbase.acroform.const": {"blob_name": "reportlab.pdfbase.acroform", "blob_size": 15613, "input_size": 21104}, "module.reportlab.pdfbase.const": {"blob_name": "reportlab.pdfbase", "blob_size": 388, "input_size": 674}, "module.reportlab.pdfbase.pdfdoc.const": {"blob_name": "reportlab.pdfbase.pdfdoc", "blob_size": 31217, "input_size": 46008}, "module.reportlab.pdfbase.pdfmetrics.const": {"blob_name": "reportlab.pdfbase.pdfmetrics", "blob_size": 10391, "input_size": 15459}, "module.reportlab.pdfbase.pdfutils.const": {"blob_name": "reportlab.pdfbase.pdfutils", "blob_size": 3570, "input_size": 5544}, "module.reportlab.pdfbase.rl_codecs.const": {"blob_name": "reportlab.pdfbase.rl_codecs", "blob_size": 7533, "input_size": 7864}, "module.reportlab.pdfbase.ttfonts.const": {"blob_name": "reportlab.pdfbase.ttfonts", "blob_size": 19715, "input_size": 28446}, "module.reportlab.pdfgen.canvas.const": {"blob_name": "reportlab.pdfgen.canvas", "blob_size": 42339, "input_size": 54783}, "module.reportlab.pdfgen.const": {"blob_name": "reportlab.pdfgen", "blob_size": 380, "input_size": 666}, "module.reportlab.pdfgen.pathobject.const": {"blob_name": "reportlab.pdfgen.pathobject", "blob_size": 2903, "input_size": 4136}, "module.reportlab.pdfgen.pdfgeom.const": {"blob_name": "reportlab.pdfgen.pdfgeom", "blob_size": 1305, "input_size": 1759}, "module.reportlab.pdfgen.pdfimages.const": {"blob_name": "reportlab.pdfgen.pdfimages", "blob_size": 3033, "input_size": 4740}, "module.reportlab.pdfgen.textobject.const": {"blob_name": "reportlab.pdfgen.textobject", "blob_size": 11907, "input_size": 17713}, "module.reportlab.platypus.const": {"blob_name": "reportlab.platypus", "blob_size": 509, "input_size": 913}, "module.reportlab.platypus.doctemplate.const": {"blob_name": "reportlab.platypus.doctemplate", "blob_size": 23056, "input_size": 31298}, "module.reportlab.platypus.flowables.const": {"blob_name": "reportlab.platypus.flowables", "blob_size": 29564, "input_size": 42246}, "module.reportlab.platypus.frames.const": {"blob_name": "reportlab.platypus.frames", "blob_size": 3975, "input_size": 5766}, "module.reportlab.platypus.multicol.const": {"blob_name": "reportlab.platypus.multicol", "blob_size": 1369, "input_size": 2365}, "module.reportlab.platypus.paragraph.const": {"blob_name": "reportlab.platypus.paragraph", "blob_size": 24135, "input_size": 32882}, "module.reportlab.platypus.paraparser.const": {"blob_name": "reportlab.platypus.paraparser", "blob_size": 41170, "input_size": 55148}, "module.reportlab.platypus.tables.const": {"blob_name": "reportlab.platypus.tables", "blob_size": 21659, "input_size": 31008}, "module.reportlab.platypus.xpreformatted.const": {"blob_name": "reportlab.platypus.xpreformatted", "blob_size": 4387, "input_size": 5740}, "module.reportlab.rl_config.const": {"blob_name": "reportlab.rl_config", "blob_size": 2097, "input_size": 3536}, "module.reportlab.rl_settings.const": {"blob_name": "reportlab.rl_settings", "blob_size": 5061, "input_size": 6551}, "module.rethink_config.const": {"blob_name": "rethink_config", "blob_size": 1261, "input_size": 2184}, "module.rethink_db.const": {"blob_name": "rethink_db", "blob_size": 3773, "input_size": 5295}, "module.rethinkdb._dump.const": {"blob_name": "rethinkdb._dump", "blob_size": 3286, "input_size": 4848}, "module.rethinkdb._export.const": {"blob_name": "rethinkdb._export", "blob_size": 6653, "input_size": 9712}, "module.rethinkdb._import.const": {"blob_name": "rethinkdb._import", "blob_size": 15585, "input_size": 21681}, "module.rethinkdb._index_rebuild.const": {"blob_name": "rethinkdb._index_rebuild", "blob_size": 2986, "input_size": 4400}, "module.rethinkdb._restore.const": {"blob_name": "rethinkdb._restore", "blob_size": 4413, "input_size": 6134}, "module.rethinkdb.ast.const": {"blob_name": "rethinkdb.ast", "blob_size": 13361, "input_size": 26005}, "module.rethinkdb.asyncio_net.const": {"blob_name": "rethinkdb.asyncio_net", "blob_size": 318, "input_size": 565}, "module.rethinkdb.asyncio_net.net_asyncio.const": {"blob_name": "rethinkdb.asyncio_net.net_asyncio", "blob_size": 4150, "input_size": 6861}, "module.rethinkdb.backports.const": {"blob_name": "rethinkdb.backports", "blob_size": 344, "input_size": 617}, "module.rethinkdb.backports.ssl_match_hostname.const": {"blob_name": "rethinkdb.backports.ssl_match_hostname", "blob_size": 1784, "input_size": 2778}, "module.rethinkdb.const": {"blob_name": "rethinkdb", "blob_size": 1296, "input_size": 1985}, "module.rethinkdb.errors.const": {"blob_name": "rethinkdb.errors", "blob_size": 2378, "input_size": 3837}, "module.rethinkdb.gevent_net.const": {"blob_name": "rethinkdb.gevent_net", "blob_size": 314, "input_size": 561}, "module.rethinkdb.gevent_net.net_gevent.const": {"blob_name": "rethinkdb.gevent_net.net_gevent", "blob_size": 3402, "input_size": 5747}, "module.rethinkdb.handshake.const": {"blob_name": "rethinkdb.handshake", "blob_size": 6878, "input_size": 9311}, "module.rethinkdb.helpers.const": {"blob_name": "rethinkdb.helpers", "blob_size": 217, "input_size": 464}, "module.rethinkdb.logger.const": {"blob_name": "rethinkdb.logger", "blob_size": 2055, "input_size": 2961}, "module.rethinkdb.net.const": {"blob_name": "rethinkdb.net", "blob_size": 7373, "input_size": 11681}, "module.rethinkdb.ql2_pb2.const": {"blob_name": "rethinkdb.ql2_pb2", "blob_size": 3191, "input_size": 7347}, "module.rethinkdb.query.const": {"blob_name": "rethinkdb.query", "blob_size": 2723, "input_size": 5824}, "module.rethinkdb.tornado_net.const": {"blob_name": "rethinkdb.tornado_net", "blob_size": 318, "input_size": 565}, "module.rethinkdb.tornado_net.net_tornado.const": {"blob_name": "rethinkdb.tornado_net.net_tornado", "blob_size": 3350, "input_size": 5614}, "module.rethinkdb.trio_net.const": {"blob_name": "rethinkdb.trio_net", "blob_size": 306, "input_size": 553}, "module.rethinkdb.trio_net.net_trio.const": {"blob_name": "rethinkdb.trio_net.net_trio", "blob_size": 6664, "input_size": 10258}, "module.rethinkdb.twisted_net.const": {"blob_name": "rethinkdb.twisted_net", "blob_size": 318, "input_size": 565}, "module.rethinkdb.twisted_net.net_twisted.const": {"blob_name": "rethinkdb.twisted_net.net_twisted", "blob_size": 5493, "input_size": 8580}, "module.rethinkdb.utils_common.const": {"blob_name": "rethinkdb.utils_common", "blob_size": 6392, "input_size": 9456}, "module.rethinkdb.version.const": {"blob_name": "rethinkdb.version", "blob_size": 147, "input_size": 288}, "module.screen_mode_manager.const": {"blob_name": "screen_mode_manager", "blob_size": 5116, "input_size": 6442}, "module.sdl_display_fix.const": {"blob_name": "sdl_display_fix", "blob_size": 3249, "input_size": 4134}, "module.service_identity._common.const": {"blob_name": "service_identity._common", "blob_size": 4999, "input_size": 7167}, "module.service_identity._compat.const": {"blob_name": "service_identity._compat", "blob_size": 233, "input_size": 413}, "module.service_identity.const": {"blob_name": "service_identity", "blob_size": 894, "input_size": 1375}, "module.service_identity.cryptography.const": {"blob_name": "service_identity.cryptography", "blob_size": 3328, "input_size": 4062}, "module.service_identity.exceptions.const": {"blob_name": "service_identity.exceptions", "blob_size": 1237, "input_size": 1890}, "module.service_identity.pyopenssl.const": {"blob_name": "service_identity.pyopenssl", "blob_size": 3151, "input_size": 4068}, "module.settings_manager.const": {"blob_name": "settings_manager", "blob_size": 11645, "input_size": 16144}, "module.settings_page.const": {"blob_name": "settings_page", "blob_size": 21831, "input_size": 30185}, "module.settings_window.const": {"blob_name": "settings_window", "blob_size": 33637, "input_size": 42380}, "module.shiboken2.const": {"blob_name": "shiboken2", "blob_size": 446, "input_size": 946}, "module.shiboken6.const": {"blob_name": "shiboken6", "blob_size": 489, "input_size": 1026}, "module.simple_text_input.const": {"blob_name": "simple_text_input", "blob_size": 4522, "input_size": 6897}, "module.six.const": {"blob_name": "six", "blob_size": 14311, "input_size": 21425}, "module.sniffio._impl.const": {"blob_name": "sniffio._impl", "blob_size": 2107, "input_size": 2685}, "module.sniffio._version.const": {"blob_name": "sniffio._version", "blob_size": 135, "input_size": 276}, "module.sniffio.const": {"blob_name": "sniffio", "blob_size": 486, "input_size": 797}, "module.sortedcontainers.const": {"blob_name": "sortedcontainers", "blob_size": 2368, "input_size": 2786}, "module.sortedcontainers.sorteddict.const": {"blob_name": "sortedcontainers.sorteddict", "blob_size": 17102, "input_size": 19767}, "module.sortedcontainers.sortedlist.const": {"blob_name": "sortedcontainers.sortedlist", "blob_size": 39004, "input_size": 43058}, "module.sortedcontainers.sortedset.const": {"blob_name": "sortedcontainers.sortedset", "blob_size": 15066, "input_size": 17519}, "module.sqlalchemy.connectors.aioodbc.const": {"blob_name": "sqlalchemy.connectors.aioodbc", "blob_size": 2599, "input_size": 3889}, "module.sqlalchemy.connectors.asyncio.const": {"blob_name": "sqlalchemy.connectors.asyncio", "blob_size": 2918, "input_size": 4535}, "module.sqlalchemy.connectors.const": {"blob_name": "sqlalchemy.connectors", "blob_size": 686, "input_size": 1101}, "module.sqlalchemy.connectors.pyodbc.const": {"blob_name": "sqlalchemy.connectors.pyodbc", "blob_size": 3671, "input_size": 5555}, "module.sqlalchemy.const": {"blob_name": "sqlalchemy", "blob_size": 7081, "input_size": 11030}, "module.sqlalchemy.cyextension.const": {"blob_name": "sqlalchemy.cyextension", "blob_size": 322, "input_size": 569}, "module.sqlalchemy.dialects._typing.const": {"blob_name": "sqlalchemy.dialects._typing", "blob_size": 519, "input_size": 867}, "module.sqlalchemy.dialects.const": {"blob_name": "sqlalchemy.dialects", "blob_size": 895, "input_size": 1547}, "module.sqlalchemy.dialects.mssql.aioodbc.const": {"blob_name": "sqlalchemy.dialects.mssql.aioodbc", "blob_size": 1962, "input_size": 2438}, "module.sqlalchemy.dialects.mssql.base.const": {"blob_name": "sqlalchemy.dialects.mssql.base", "blob_size": 71927, "input_size": 86426}, "module.sqlalchemy.dialects.mssql.const": {"blob_name": "sqlalchemy.dialects.mssql", "blob_size": 1527, "input_size": 2224}, "module.sqlalchemy.dialects.mssql.information_schema.const": {"blob_name": "sqlalchemy.dialects.mssql.information_schema", "blob_size": 3468, "input_size": 5538}, "module.sqlalchemy.dialects.mssql.json.const": {"blob_name": "sqlalchemy.dialects.mssql.json", "blob_size": 3371, "input_size": 3964}, "module.sqlalchemy.dialects.mssql.pymssql.const": {"blob_name": "sqlalchemy.dialects.mssql.pymssql", "blob_size": 2676, "input_size": 4004}, "module.sqlalchemy.dialects.mssql.pyodbc.const": {"blob_name": "sqlalchemy.dialects.mssql.pyodbc", "blob_size": 19285, "input_size": 21586}, "module.sqlalchemy.dialects.mysql.aiomysql.const": {"blob_name": "sqlalchemy.dialects.mysql.aiomysql", "blob_size": 5475, "input_size": 7814}, "module.sqlalchemy.dialects.mysql.asyncmy.const": {"blob_name": "sqlalchemy.dialects.mysql.asyncmy", "blob_size": 5297, "input_size": 7844}, "module.sqlalchemy.dialects.mysql.base.const": {"blob_name": "sqlalchemy.dialects.mysql.base", "blob_size": 67080, "input_size": 81840}, "module.sqlalchemy.dialects.mysql.const": {"blob_name": "sqlalchemy.dialects.mysql", "blob_size": 1702, "input_size": 2545}, "module.sqlalchemy.dialects.mysql.cymysql.const": {"blob_name": "sqlalchemy.dialects.mysql.cymysql", "blob_size": 1690, "input_size": 2489}, "module.sqlalchemy.dialects.mysql.dml.const": {"blob_name": "sqlalchemy.dialects.mysql.dml", "blob_size": 6048, "input_size": 7066}, "module.sqlalchemy.dialects.mysql.enumerated.const": {"blob_name": "sqlalchemy.dialects.mysql.enumerated", "blob_size": 5250, "input_size": 6489}, "module.sqlalchemy.dialects.mysql.expression.const": {"blob_name": "sqlalchemy.dialects.mysql.expression", "blob_size": 3257, "input_size": 4285}, "module.sqlalchemy.dialects.mysql.json.const": {"blob_name": "sqlalchemy.dialects.mysql.json", "blob_size": 1589, "input_size": 2182}, "module.sqlalchemy.dialects.mysql.mariadb.const": {"blob_name": "sqlalchemy.dialects.mysql.mariadb", "blob_size": 1064, "input_size": 1722}, "module.sqlalchemy.dialects.mysql.mariadbconnector.const": {"blob_name": "sqlalchemy.dialects.mysql.mariadbconnector", "blob_size": 4570, "input_size": 6703}, "module.sqlalchemy.dialects.mysql.mysqlconnector.const": {"blob_name": "sqlalchemy.dialects.mysql.mysqlconnector", "blob_size": 3722, "input_size": 5601}, "module.sqlalchemy.dialects.mysql.mysqldb.const": {"blob_name": "sqlalchemy.dialects.mysql.mysqldb", "blob_size": 5959, "input_size": 8005}, "module.sqlalchemy.dialects.mysql.pymysql.const": {"blob_name": "sqlalchemy.dialects.mysql.pymysql", "blob_size": 2986, "input_size": 3927}, "module.sqlalchemy.dialects.mysql.pyodbc.const": {"blob_name": "sqlalchemy.dialects.mysql.pyodbc", "blob_size": 2953, "input_size": 4079}, "module.sqlalchemy.dialects.mysql.reflection.const": {"blob_name": "sqlalchemy.dialects.mysql.reflection", "blob_size": 8318, "input_size": 12010}, "module.sqlalchemy.dialects.mysql.reserved_words.const": {"blob_name": "sqlalchemy.dialects.mysql.reserved_words", "blob_size": 5211, "input_size": 5946}, "module.sqlalchemy.dialects.mysql.types.const": {"blob_name": "sqlalchemy.dialects.mysql.types", "blob_size": 17545, "input_size": 19997}, "module.sqlalchemy.dialects.oracle.base.const": {"blob_name": "sqlalchemy.dialects.oracle.base", "blob_size": 51351, "input_size": 63539}, "module.sqlalchemy.dialects.oracle.const": {"blob_name": "sqlalchemy.dialects.oracle", "blob_size": 1179, "input_size": 1798}, "module.sqlalchemy.dialects.oracle.cx_oracle.const": {"blob_name": "sqlalchemy.dialects.oracle.cx_oracle", "blob_size": 31622, "input_size": 36726}, "module.sqlalchemy.dialects.oracle.dictionary.const": {"blob_name": "sqlalchemy.dialects.oracle.dictionary", "blob_size": 4981, "input_size": 9685}, "module.sqlalchemy.dialects.oracle.oracledb.const": {"blob_name": "sqlalchemy.dialects.oracle.oracledb", "blob_size": 28366, "input_size": 30932}, "module.sqlalchemy.dialects.oracle.types.const": {"blob_name": "sqlalchemy.dialects.oracle.types", "blob_size": 6139, "input_size": 7913}, "module.sqlalchemy.dialects.postgresql._psycopg_common.const": {"blob_name": "sqlalchemy.dialects.postgresql._psycopg_common", "blob_size": 2793, "input_size": 4345}, "module.sqlalchemy.dialects.postgresql.array.const": {"blob_name": "sqlalchemy.dialects.postgresql.array", "blob_size": 9693, "input_size": 11572}, "module.sqlalchemy.dialects.postgresql.asyncpg.const": {"blob_name": "sqlalchemy.dialects.postgresql.asyncpg", "blob_size": 20641, "input_size": 27092}, "module.sqlalchemy.dialects.postgresql.base.const": {"blob_name": "sqlalchemy.dialects.postgresql.base", "blob_size": 90365, "input_size": 108123}, "module.sqlalchemy.dialects.postgresql.const": {"blob_name": "sqlalchemy.dialects.postgresql", "blob_size": 3042, "input_size": 4162}, "module.sqlalchemy.dialects.postgresql.dml.const": {"blob_name": "sqlalchemy.dialects.postgresql.dml", "blob_size": 8446, "input_size": 9346}, "module.sqlalchemy.dialects.postgresql.ext.const": {"blob_name": "sqlalchemy.dialects.postgresql.ext", "blob_size": 11463, "input_size": 13166}, "module.sqlalchemy.dialects.postgresql.hstore.const": {"blob_name": "sqlalchemy.dialects.postgresql.hstore", "blob_size": 7731, "input_size": 9991}, "module.sqlalchemy.dialects.postgresql.json.const": {"blob_name": "sqlalchemy.dialects.postgresql.json", "blob_size": 9187, "input_size": 10703}, "module.sqlalchemy.dialects.postgresql.named_types.const": {"blob_name": "sqlalchemy.dialects.postgresql.named_types", "blob_size": 12147, "input_size": 14260}, "module.sqlalchemy.dialects.postgresql.operators.const": {"blob_name": "sqlalchemy.dialects.postgresql.operators", "blob_size": 754, "input_size": 1385}, "module.sqlalchemy.dialects.postgresql.pg8000.const": {"blob_name": "sqlalchemy.dialects.postgresql.pg8000", "blob_size": 9782, "input_size": 14079}, "module.sqlalchemy.dialects.postgresql.pg_catalog.const": {"blob_name": "sqlalchemy.dialects.postgresql.pg_catalog", "blob_size": 3570, "input_size": 6800}, "module.sqlalchemy.dialects.postgresql.psycopg.const": {"blob_name": "sqlalchemy.dialects.postgresql.psycopg", "blob_size": 12314, "input_size": 16969}, "module.sqlalchemy.dialects.postgresql.psycopg2.const": {"blob_name": "sqlalchemy.dialects.postgresql.psycopg2", "blob_size": 24961, "input_size": 27932}, "module.sqlalchemy.dialects.postgresql.psycopg2cffi.const": {"blob_name": "sqlalchemy.dialects.postgresql.psycopg2cffi", "blob_size": 1518, "input_size": 2020}, "module.sqlalchemy.dialects.postgresql.ranges.const": {"blob_name": "sqlalchemy.dialects.postgresql.ranges", "blob_size": 11713, "input_size": 15282}, "module.sqlalchemy.dialects.postgresql.types.const": {"blob_name": "sqlalchemy.dialects.postgresql.types", "blob_size": 5157, "input_size": 6852}, "module.sqlalchemy.dialects.sqlite.aiosqlite.const": {"blob_name": "sqlalchemy.dialects.sqlite.aiosqlite", "blob_size": 6727, "input_size": 8793}, "module.sqlalchemy.dialects.sqlite.base.const": {"blob_name": "sqlalchemy.dialects.sqlite.base", "blob_size": 57631, "input_size": 65815}, "module.sqlalchemy.dialects.sqlite.const": {"blob_name": "sqlalchemy.dialects.sqlite", "blob_size": 980, "input_size": 1540}, "module.sqlalchemy.dialects.sqlite.dml.const": {"blob_name": "sqlalchemy.dialects.sqlite.dml", "blob_size": 7083, "input_size": 7939}, "module.sqlalchemy.dialects.sqlite.json.const": {"blob_name": "sqlalchemy.dialects.sqlite.json", "blob_size": 1924, "input_size": 2517}, "module.sqlalchemy.dialects.sqlite.pysqlcipher.const": {"blob_name": "sqlalchemy.dialects.sqlite.pysqlcipher", "blob_size": 4855, "input_size": 5557}, "module.sqlalchemy.dialects.sqlite.pysqlite.const": {"blob_name": "sqlalchemy.dialects.sqlite.pysqlite", "blob_size": 23056, "input_size": 25190}, "module.sqlalchemy.engine._py_processors.const": {"blob_name": "sqlalchemy.engine._py_processors", "blob_size": 1904, "input_size": 2623}, "module.sqlalchemy.engine._py_row.const": {"blob_name": "sqlalchemy.engine._py_row", "blob_size": 1677, "input_size": 2636}, "module.sqlalchemy.engine._py_util.const": {"blob_name": "sqlalchemy.engine._py_util", "blob_size": 632, "input_size": 1026}, "module.sqlalchemy.engine.base.const": {"blob_name": "sqlalchemy.engine.base", "blob_size": 71970, "input_size": 77975}, "module.sqlalchemy.engine.characteristics.const": {"blob_name": "sqlalchemy.engine.characteristics", "blob_size": 3704, "input_size": 4438}, "module.sqlalchemy.engine.const": {"blob_name": "sqlalchemy.engine", "blob_size": 2085, "input_size": 2662}, "module.sqlalchemy.engine.create.const": {"blob_name": "sqlalchemy.engine.create", "blob_size": 26938, "input_size": 28159}, "module.sqlalchemy.engine.cursor.const": {"blob_name": "sqlalchemy.engine.cursor", "blob_size": 38265, "input_size": 43431}, "module.sqlalchemy.engine.default.const": {"blob_name": "sqlalchemy.engine.default", "blob_size": 32166, "input_size": 40972}, "module.sqlalchemy.engine.events.const": {"blob_name": "sqlalchemy.engine.events", "blob_size": 34018, "input_size": 35085}, "module.sqlalchemy.engine.interfaces.const": {"blob_name": "sqlalchemy.engine.interfaces", "blob_size": 75786, "input_size": 83388}, "module.sqlalchemy.engine.mock.const": {"blob_name": "sqlalchemy.engine.mock", "blob_size": 3153, "input_size": 3892}, "module.sqlalchemy.engine.processors.const": {"blob_name": "sqlalchemy.engine.processors", "blob_size": 815, "input_size": 1057}, "module.sqlalchemy.engine.reflection.const": {"blob_name": "sqlalchemy.engine.reflection", "blob_size": 47298, "input_size": 51115}, "module.sqlalchemy.engine.result.const": {"blob_name": "sqlalchemy.engine.result", "blob_size": 47142, "input_size": 53338}, "module.sqlalchemy.engine.row.const": {"blob_name": "sqlalchemy.engine.row", "blob_size": 8841, "input_size": 11323}, "module.sqlalchemy.engine.url.const": {"blob_name": "sqlalchemy.engine.url", "blob_size": 19623, "input_size": 22634}, "module.sqlalchemy.engine.util.const": {"blob_name": "sqlalchemy.engine.util", "blob_size": 2951, "input_size": 4038}, "module.sqlalchemy.event.api.const": {"blob_name": "sqlalchemy.event.api", "blob_size": 7606, "input_size": 8047}, "module.sqlalchemy.event.attr.const": {"blob_name": "sqlalchemy.event.attr", "blob_size": 10931, "input_size": 14617}, "module.sqlalchemy.event.base.const": {"blob_name": "sqlalchemy.event.base", "blob_size": 7549, "input_size": 9933}, "module.sqlalchemy.event.const": {"blob_name": "sqlalchemy.event", "blob_size": 788, "input_size": 1196}, "module.sqlalchemy.event.legacy.const": {"blob_name": "sqlalchemy.event.legacy", "blob_size": 4549, "input_size": 5266}, "module.sqlalchemy.event.registry.const": {"blob_name": "sqlalchemy.event.registry", "blob_size": 4570, "input_size": 5973}, "module.sqlalchemy.exc.const": {"blob_name": "sqlalchemy.exc", "blob_size": 13924, "input_size": 16490}, "module.sqlalchemy.ext.associationproxy.const": {"blob_name": "sqlalchemy.ext.associationproxy", "blob_size": 34899, "input_size": 41370}, "module.sqlalchemy.ext.asyncio.base.const": {"blob_name": "sqlalchemy.ext.asyncio.base", "blob_size": 4417, "input_size": 6096}, "module.sqlalchemy.ext.asyncio.const": {"blob_name": "sqlalchemy.ext.asyncio", "blob_size": 1135, "input_size": 1442}, "module.sqlalchemy.ext.asyncio.engine.const": {"blob_name": "sqlalchemy.ext.asyncio.engine", "blob_size": 34769, "input_size": 38388}, "module.sqlalchemy.ext.asyncio.exc.const": {"blob_name": "sqlalchemy.ext.asyncio.exc", "blob_size": 590, "input_size": 929}, "module.sqlalchemy.ext.asyncio.result.const": {"blob_name": "sqlalchemy.ext.asyncio.result", "blob_size": 22075, "input_size": 25373}, "module.sqlalchemy.ext.asyncio.scoping.const": {"blob_name": "sqlalchemy.ext.asyncio.scoping", "blob_size": 42355, "input_size": 44130}, "module.sqlalchemy.ext.asyncio.session.const": {"blob_name": "sqlalchemy.ext.asyncio.session", "blob_size": 48027, "input_size": 51270}, "module.sqlalchemy.ext.automap.const": {"blob_name": "sqlalchemy.ext.automap", "blob_size": 44445, "input_size": 45315}, "module.sqlalchemy.ext.baked.const": {"blob_name": "sqlalchemy.ext.baked", "blob_size": 10452, "input_size": 13321}, "module.sqlalchemy.ext.compiler.const": {"blob_name": "sqlalchemy.ext.compiler", "blob_size": 18487, "input_size": 19244}, "module.sqlalchemy.ext.const": {"blob_name": "sqlalchemy.ext", "blob_size": 360, "input_size": 674}, "module.sqlalchemy.ext.declarative.const": {"blob_name": "sqlalchemy.ext.declarative", "blob_size": 1445, "input_size": 1934}, "module.sqlalchemy.ext.declarative.extensions.const": {"blob_name": "sqlalchemy.ext.declarative.extensions", "blob_size": 14145, "input_size": 16010}, "module.sqlalchemy.ext.horizontal_shard.const": {"blob_name": "sqlalchemy.ext.horizontal_shard", "blob_size": 10618, "input_size": 11587}, "module.sqlalchemy.ext.hybrid.const": {"blob_name": "sqlalchemy.ext.hybrid", "blob_size": 46105, "input_size": 49067}, "module.sqlalchemy.ext.indexable.const": {"blob_name": "sqlalchemy.ext.indexable", "blob_size": 9190, "input_size": 9863}, "module.sqlalchemy.ext.instrumentation.const": {"blob_name": "sqlalchemy.ext.instrumentation", "blob_size": 7815, "input_size": 9953}, "module.sqlalchemy.ext.mutable.const": {"blob_name": "sqlalchemy.ext.mutable", "blob_size": 29546, "input_size": 32739}, "module.sqlalchemy.ext.mypy.const": {"blob_name": "sqlalchemy.ext.mypy", "blob_size": 364, "input_size": 638}, "module.sqlalchemy.ext.orderinglist.const": {"blob_name": "sqlalchemy.ext.orderinglist", "blob_size": 11580, "input_size": 12935}, "module.sqlalchemy.ext.serializer.const": {"blob_name": "sqlalchemy.ext.serializer", "blob_size": 3769, "input_size": 5062}, "module.sqlalchemy.future.const": {"blob_name": "sqlalchemy.future", "blob_size": 510, "input_size": 828}, "module.sqlalchemy.future.engine.const": {"blob_name": "sqlalchemy.future.engine", "blob_size": 291, "input_size": 454}, "module.sqlalchemy.inspection.const": {"blob_name": "sqlalchemy.inspection", "blob_size": 3918, "input_size": 4945}, "module.sqlalchemy.log.const": {"blob_name": "sqlalchemy.log", "blob_size": 4563, "input_size": 6190}, "module.sqlalchemy.orm._orm_constructors.const": {"blob_name": "sqlalchemy.orm._orm_constructors", "blob_size": 95147, "input_size": 94689}, "module.sqlalchemy.orm._typing.const": {"blob_name": "sqlalchemy.orm._typing", "blob_size": 2319, "input_size": 3693}, "module.sqlalchemy.orm.attributes.const": {"blob_name": "sqlalchemy.orm.attributes", "blob_size": 39686, "input_size": 45175}, "module.sqlalchemy.orm.base.const": {"blob_name": "sqlalchemy.orm.base", "blob_size": 14487, "input_size": 17882}, "module.sqlalchemy.orm.bulk_persistence.const": {"blob_name": "sqlalchemy.orm.bulk_persistence", "blob_size": 22703, "input_size": 25602}, "module.sqlalchemy.orm.clsregistry.const": {"blob_name": "sqlalchemy.orm.clsregistry", "blob_size": 7816, "input_size": 10633}, "module.sqlalchemy.orm.collections.const": {"blob_name": "sqlalchemy.orm.collections", "blob_size": 28532, "input_size": 33646}, "module.sqlalchemy.orm.const": {"blob_name": "sqlalchemy.orm", "blob_size": 5395, "input_size": 6881}, "module.sqlalchemy.orm.context.const": {"blob_name": "sqlalchemy.orm.context", "blob_size": 30895, "input_size": 39358}, "module.sqlalchemy.orm.decl_api.const": {"blob_name": "sqlalchemy.orm.decl_api", "blob_size": 43768, "input_size": 48978}, "module.sqlalchemy.orm.decl_base.const": {"blob_name": "sqlalchemy.orm.decl_base", "blob_size": 23937, "input_size": 29179}, "module.sqlalchemy.orm.dependency.const": {"blob_name": "sqlalchemy.orm.dependency", "blob_size": 9632, "input_size": 12074}, "module.sqlalchemy.orm.descriptor_props.const": {"blob_name": "sqlalchemy.orm.descriptor_props", "blob_size": 17422, "input_size": 21873}, "module.sqlalchemy.orm.dynamic.const": {"blob_name": "sqlalchemy.orm.dynamic", "blob_size": 5489, "input_size": 7272}, "module.sqlalchemy.orm.evaluator.const": {"blob_name": "sqlalchemy.orm.evaluator", "blob_size": 5986, "input_size": 8357}, "module.sqlalchemy.orm.events.const": {"blob_name": "sqlalchemy.orm.events", "blob_size": 112704, "input_size": 117068}, "module.sqlalchemy.orm.exc.const": {"blob_name": "sqlalchemy.orm.exc", "blob_size": 4560, "input_size": 5798}, "module.sqlalchemy.orm.identity.const": {"blob_name": "sqlalchemy.orm.identity", "blob_size": 3745, "input_size": 5551}, "module.sqlalchemy.orm.instrumentation.const": {"blob_name": "sqlalchemy.orm.instrumentation", "blob_size": 12332, "input_size": 16034}, "module.sqlalchemy.orm.interfaces.const": {"blob_name": "sqlalchemy.orm.interfaces", "blob_size": 33679, "input_size": 38846}, "module.sqlalchemy.orm.loading.const": {"blob_name": "sqlalchemy.orm.loading", "blob_size": 17110, "input_size": 19564}, "module.sqlalchemy.orm.mapped_collection.const": {"blob_name": "sqlalchemy.orm.mapped_collection", "blob_size": 13470, "input_size": 15239}, "module.sqlalchemy.orm.mapper.const": {"blob_name": "sqlalchemy.orm.mapper", "blob_size": 77792, "input_size": 89972}, "module.sqlalchemy.orm.path_registry.const": {"blob_name": "sqlalchemy.orm.path_registry", "blob_size": 9316, "input_size": 12549}, "module.sqlalchemy.orm.persistence.const": {"blob_name": "sqlalchemy.orm.persistence", "blob_size": 13800, "input_size": 15673}, "module.sqlalchemy.orm.properties.const": {"blob_name": "sqlalchemy.orm.properties", "blob_size": 13161, "input_size": 16735}, "module.sqlalchemy.orm.query.const": {"blob_name": "sqlalchemy.orm.query", "blob_size": 83506, "input_size": 93030}, "module.sqlalchemy.orm.relationships.const": {"blob_name": "sqlalchemy.orm.relationships", "blob_size": 54647, "input_size": 63344}, "module.sqlalchemy.orm.scoping.const": {"blob_name": "sqlalchemy.orm.scoping", "blob_size": 68928, "input_size": 71054}, "module.sqlalchemy.orm.session.const": {"blob_name": "sqlalchemy.orm.session", "blob_size": 123780, "input_size": 133498}, "module.sqlalchemy.orm.state.const": {"blob_name": "sqlalchemy.orm.state", "blob_size": 20790, "input_size": 25198}, "module.sqlalchemy.orm.state_changes.const": {"blob_name": "sqlalchemy.orm.state_changes", "blob_size": 3290, "input_size": 4180}, "module.sqlalchemy.orm.strategies.const": {"blob_name": "sqlalchemy.orm.strategies", "blob_size": 29458, "input_size": 36532}, "module.sqlalchemy.orm.strategy_options.const": {"blob_name": "sqlalchemy.orm.strategy_options", "blob_size": 44754, "input_size": 49753}, "module.sqlalchemy.orm.sync.const": {"blob_name": "sqlalchemy.orm.sync", "blob_size": 2048, "input_size": 2649}, "module.sqlalchemy.orm.unitofwork.const": {"blob_name": "sqlalchemy.orm.unitofwork", "blob_size": 8150, "input_size": 11437}, "module.sqlalchemy.orm.util.const": {"blob_name": "sqlalchemy.orm.util", "blob_size": 38606, "input_size": 45691}, "module.sqlalchemy.orm.writeonly.const": {"blob_name": "sqlalchemy.orm.writeonly", "blob_size": 11189, "input_size": 14093}, "module.sqlalchemy.pool.base.const": {"blob_name": "sqlalchemy.pool.base", "blob_size": 25617, "input_size": 30119}, "module.sqlalchemy.pool.const": {"blob_name": "sqlalchemy.pool", "blob_size": 1519, "input_size": 1829}, "module.sqlalchemy.pool.events.const": {"blob_name": "sqlalchemy.pool.events", "blob_size": 11819, "input_size": 12532}, "module.sqlalchemy.pool.impl.const": {"blob_name": "sqlalchemy.pool.impl", "blob_size": 10958, "input_size": 13140}, "module.sqlalchemy.schema.const": {"blob_name": "sqlalchemy.schema", "blob_size": 1937, "input_size": 2288}, "module.sqlalchemy.sql._dml_constructors.const": {"blob_name": "sqlalchemy.sql._dml_constructors", "blob_size": 3462, "input_size": 3738}, "module.sqlalchemy.sql._elements_constructors.const": {"blob_name": "sqlalchemy.sql._elements_constructors", "blob_size": 59364, "input_size": 61229}, "module.sqlalchemy.sql._orm_types.const": {"blob_name": "sqlalchemy.sql._orm_types", "blob_size": 455, "input_size": 680}, "module.sqlalchemy.sql._py_util.const": {"blob_name": "sqlalchemy.sql._py_util", "blob_size": 1499, "input_size": 2136}, "module.sqlalchemy.sql._selectable_constructors.const": {"blob_name": "sqlalchemy.sql._selectable_constructors", "blob_size": 16053, "input_size": 16711}, "module.sqlalchemy.sql._typing.const": {"blob_name": "sqlalchemy.sql._typing", "blob_size": 5104, "input_size": 7903}, "module.sqlalchemy.sql.annotation.const": {"blob_name": "sqlalchemy.sql.annotation", "blob_size": 7837, "input_size": 9399}, "module.sqlalchemy.sql.base.const": {"blob_name": "sqlalchemy.sql.base", "blob_size": 44795, "input_size": 53577}, "module.sqlalchemy.sql.cache_key.const": {"blob_name": "sqlalchemy.sql.cache_key", "blob_size": 12869, "input_size": 16273}, "module.sqlalchemy.sql.coercions.const": {"blob_name": "sqlalchemy.sql.coercions", "blob_size": 14029, "input_size": 18221}, "module.sqlalchemy.sql.compiler.const": {"blob_name": "sqlalchemy.sql.compiler", "blob_size": 83483, "input_size": 108236}, "module.sqlalchemy.sql.const": {"blob_name": "sqlalchemy.sql", "blob_size": 3320, "input_size": 4352}, "module.sqlalchemy.sql.crud.const": {"blob_name": "sqlalchemy.sql.crud", "blob_size": 12606, "input_size": 14686}, "module.sqlalchemy.sql.ddl.const": {"blob_name": "sqlalchemy.sql.ddl", "blob_size": 28299, "input_size": 31934}, "module.sqlalchemy.sql.default_comparator.const": {"blob_name": "sqlalchemy.sql.default_comparator", "blob_size": 5270, "input_size": 7328}, "module.sqlalchemy.sql.dml.const": {"blob_name": "sqlalchemy.sql.dml", "blob_size": 45777, "input_size": 50884}, "module.sqlalchemy.sql.elements.const": {"blob_name": "sqlalchemy.sql.elements", "blob_size": 88167, "input_size": 103941}, "module.sqlalchemy.sql.events.const": {"blob_name": "sqlalchemy.sql.events", "blob_size": 17723, "input_size": 18424}, "module.sqlalchemy.sql.expression.const": {"blob_name": "sqlalchemy.sql.expression", "blob_size": 3878, "input_size": 5261}, "module.sqlalchemy.sql.functions.const": {"blob_name": "sqlalchemy.sql.functions", "blob_size": 40396, "input_size": 45660}, "module.sqlalchemy.sql.lambdas.const": {"blob_name": "sqlalchemy.sql.lambdas", "blob_size": 22048, "input_size": 25717}, "module.sqlalchemy.sql.naming.const": {"blob_name": "sqlalchemy.sql.naming", "blob_size": 2223, "input_size": 3581}, "module.sqlalchemy.sql.operators.const": {"blob_name": "sqlalchemy.sql.operators", "blob_size": 55136, "input_size": 60857}, "module.sqlalchemy.sql.roles.const": {"blob_name": "sqlalchemy.sql.roles", "blob_size": 3867, "input_size": 5746}, "module.sqlalchemy.sql.schema.const": {"blob_name": "sqlalchemy.sql.schema", "blob_size": 144694, "input_size": 154911}, "module.sqlalchemy.sql.selectable.const": {"blob_name": "sqlalchemy.sql.selectable", "blob_size": 145273, "input_size": 160221}, "module.sqlalchemy.sql.sqltypes.const": {"blob_name": "sqlalchemy.sql.sqltypes", "blob_size": 77957, "input_size": 88493}, "module.sqlalchemy.sql.traversals.const": {"blob_name": "sqlalchemy.sql.traversals", "blob_size": 13159, "input_size": 17099}, "module.sqlalchemy.sql.type_api.const": {"blob_name": "sqlalchemy.sql.type_api", "blob_size": 54379, "input_size": 60143}, "module.sqlalchemy.sql.util.const": {"blob_name": "sqlalchemy.sql.util", "blob_size": 20026, "input_size": 24739}, "module.sqlalchemy.sql.visitors.const": {"blob_name": "sqlalchemy.sql.visitors", "blob_size": 20612, "input_size": 24083}, "module.sqlalchemy.types.const": {"blob_name": "sqlalchemy.types", "blob_size": 1501, "input_size": 2200}, "module.sqlalchemy.util._collections.const": {"blob_name": "sqlalchemy.util._collections", "blob_size": 10981, "input_size": 15306}, "module.sqlalchemy.util._concurrency_py3k.const": {"blob_name": "sqlalchemy.util._concurrency_py3k", "blob_size": 4129, "input_size": 5747}, "module.sqlalchemy.util._has_cy.const": {"blob_name": "sqlalchemy.util._has_cy", "blob_size": 522, "input_size": 804}, "module.sqlalchemy.util._py_collections.const": {"blob_name": "sqlalchemy.util._py_collections", "blob_size": 6909, "input_size": 10027}, "module.sqlalchemy.util.compat.const": {"blob_name": "sqlalchemy.util.compat", "blob_size": 4399, "input_size": 6412}, "module.sqlalchemy.util.concurrency.const": {"blob_name": "sqlalchemy.util.concurrency", "blob_size": 1204, "input_size": 1902}, "module.sqlalchemy.util.const": {"blob_name": "sqlalchemy.util", "blob_size": 5055, "input_size": 6086}, "module.sqlalchemy.util.deprecations.const": {"blob_name": "sqlalchemy.util.deprecations", "blob_size": 5984, "input_size": 6929}, "module.sqlalchemy.util.langhelpers.const": {"blob_name": "sqlalchemy.util.langhelpers", "blob_size": 31387, "input_size": 41473}, "module.sqlalchemy.util.preloaded.const": {"blob_name": "sqlalchemy.util.preloaded", "blob_size": 2032, "input_size": 2695}, "module.sqlalchemy.util.queue.const": {"blob_name": "sqlalchemy.util.queue", "blob_size": 4855, "input_size": 6692}, "module.sqlalchemy.util.topological.const": {"blob_name": "sqlalchemy.util.topological", "blob_size": 1470, "input_size": 2064}, "module.sqlalchemy.util.typing.const": {"blob_name": "sqlalchemy.util.typing", "blob_size": 9523, "input_size": 12225}, "module.stats_data_provider.const": {"blob_name": "stats_data_provider", "blob_size": 8431, "input_size": 11995}, "module.stats_db.const": {"blob_name": "stats_db", "blob_size": 25067, "input_size": 29966}, "module.stats_event_hooks.const": {"blob_name": "stats_event_hooks", "blob_size": 9264, "input_size": 12158}, "module.stats_export.const": {"blob_name": "stats_export", "blob_size": 3468, "input_size": 5050}, "module.stats_integration.const": {"blob_name": "stats_integration", "blob_size": 6760, "input_size": 8743}, "module.stats_models.const": {"blob_name": "stats_models", "blob_size": 2212, "input_size": 3866}, "module.stats_page.const": {"blob_name": "stats_page", "blob_size": 74624, "input_size": 96630}, "module.stats_preloader.const": {"blob_name": "stats_preloader", "blob_size": 8858, "input_size": 11862}, "module.stats_summary.const": {"blob_name": "stats_summary", "blob_size": 986, "input_size": 1542}, "module.sync_manager.const": {"blob_name": "sync_manager", "blob_size": 9471, "input_size": 13255}, "module.thread_safe_db.const": {"blob_name": "thread_safe_db", "blob_size": 13503, "input_size": 17930}, "module.threadpoolctl.const": {"blob_name": "threadpoolctl", "blob_size": 26186, "input_size": 32698}, "module.tornado.concurrent.const": {"blob_name": "tornado.concurrent", "blob_size": 5606, "input_size": 7215}, "module.tornado.const": {"blob_name": "tornado", "blob_size": 695, "input_size": 1161}, "module.tornado.escape.const": {"blob_name": "tornado.escape", "blob_size": 7708, "input_size": 9366}, "module.tornado.gen.const": {"blob_name": "tornado.gen", "blob_size": 17824, "input_size": 20813}, "module.tornado.ioloop.const": {"blob_name": "tornado.ioloop", "blob_size": 22957, "input_size": 26866}, "module.tornado.iostream.const": {"blob_name": "tornado.iostream", "blob_size": 25091, "input_size": 30460}, "module.tornado.log.const": {"blob_name": "tornado.log", "blob_size": 5815, "input_size": 7924}, "module.tornado.netutil.const": {"blob_name": "tornado.netutil", "blob_size": 11724, "input_size": 14628}, "module.tornado.options.const": {"blob_name": "tornado.options", "blob_size": 14812, "input_size": 17901}, "module.tornado.platform.asyncio.const": {"blob_name": "tornado.platform.asyncio", "blob_size": 9979, "input_size": 13386}, "module.tornado.platform.const": {"blob_name": "tornado.platform", "blob_size": 298, "input_size": 545}, "module.tornado.process.const": {"blob_name": "tornado.process", "blob_size": 6288, "input_size": 8060}, "module.tornado.tcpclient.const": {"blob_name": "tornado.tcpclient", "blob_size": 4931, "input_size": 6727}, "module.tornado.util.const": {"blob_name": "tornado.util", "blob_size": 8475, "input_size": 11254}, "module.trio._abc.const": {"blob_name": "trio._abc", "blob_size": 22982, "input_size": 24998}, "module.trio._channel.const": {"blob_name": "trio._channel", "blob_size": 9846, "input_size": 11643}, "module.trio._core._asyncgens.const": {"blob_name": "trio._core._asyncgens", "blob_size": 2710, "input_size": 4040}, "module.trio._core._concat_tb.const": {"blob_name": "trio._core._concat_tb", "blob_size": 1140, "input_size": 1860}, "module.trio._core._entry_queue.const": {"blob_name": "trio._core._entry_queue", "blob_size": 4476, "input_size": 5720}, "module.trio._core._exceptions.const": {"blob_name": "trio._core._exceptions", "blob_size": 4275, "input_size": 4890}, "module.trio._core._generated_instrumentation.const": {"blob_name": "trio._core._generated_instrumentation", "blob_size": 1109, "input_size": 1442}, "module.trio._core._generated_io_windows.const": {"blob_name": "trio._core._generated_io_windows", "blob_size": 4045, "input_size": 4591}, "module.trio._core._generated_run.const": {"blob_name": "trio._core._generated_run", "blob_size": 8220, "input_size": 8795}, "module.trio._core._instrumentation.const": {"blob_name": "trio._core._instrumentation", "blob_size": 2384, "input_size": 3240}, "module.trio._core._io_common.const": {"blob_name": "trio._core._io_common", "blob_size": 406, "input_size": 685}, "module.trio._core._io_windows.const": {"blob_name": "trio._core._io_windows", "blob_size": 10655, "input_size": 13887}, "module.trio._core._ki.const": {"blob_name": "trio._core._ki", "blob_size": 2803, "input_size": 3812}, "module.trio._core._local.const": {"blob_name": "trio._core._local", "blob_size": 1718, "input_size": 2776}, "module.trio._core._mock_clock.const": {"blob_name": "trio._core._mock_clock", "blob_size": 4074, "input_size": 5213}, "module.trio._core._parking_lot.const": {"blob_name": "trio._core._parking_lot", "blob_size": 4339, "input_size": 5459}, "module.trio._core._run.const": {"blob_name": "trio._core._run", "blob_size": 52272, "input_size": 59793}, "module.trio._core._thread_cache.const": {"blob_name": "trio._core._thread_cache", "blob_size": 4514, "input_size": 5793}, "module.trio._core._traps.const": {"blob_name": "trio._core._traps", "blob_size": 10014, "input_size": 10992}, "module.trio._core._unbounded_queue.const": {"blob_name": "trio._core._unbounded_queue", "blob_size": 4139, "input_size": 5324}, "module.trio._core._wakeup_socketpair.const": {"blob_name": "trio._core._wakeup_socketpair", "blob_size": 1258, "input_size": 2149}, "module.trio._core._windows_cffi.const": {"blob_name": "trio._core._windows_cffi", "blob_size": 10857, "input_size": 12316}, "module.trio._core.const": {"blob_name": "trio._core", "blob_size": 2741, "input_size": 2581}, "module.trio._deprecate.const": {"blob_name": "trio._deprecate", "blob_size": 3517, "input_size": 4269}, "module.trio._dtls.const": {"blob_name": "trio._dtls", "blob_size": 17912, "input_size": 22452}, "module.trio._file_io.const": {"blob_name": "trio._file_io", "blob_size": 8395, "input_size": 10597}, "module.trio._highlevel_generic.const": {"blob_name": "trio._highlevel_generic", "blob_size": 4264, "input_size": 5176}, "module.trio._highlevel_open_tcp_listeners.const": {"blob_name": "trio._highlevel_open_tcp_listeners", "blob_size": 6022, "input_size": 6584}, "module.trio._highlevel_open_tcp_stream.const": {"blob_name": "trio._highlevel_open_tcp_stream", "blob_size": 5399, "input_size": 6211}, "module.trio._highlevel_open_unix_stream.const": {"blob_name": "trio._highlevel_open_unix_stream", "blob_size": 1434, "input_size": 2117}, "module.trio._highlevel_serve_listeners.const": {"blob_name": "trio._highlevel_serve_listeners", "blob_size": 3635, "input_size": 4345}, "module.trio._highlevel_socket.const": {"blob_name": "trio._highlevel_socket", "blob_size": 5378, "input_size": 6927}, "module.trio._highlevel_ssl_helpers.const": {"blob_name": "trio._highlevel_ssl_helpers", "blob_size": 5248, "input_size": 5463}, "module.trio._path.const": {"blob_name": "trio._path", "blob_size": 4416, "input_size": 6029}, "module.trio._signals.const": {"blob_name": "trio._signals", "blob_size": 3210, "input_size": 4096}, "module.trio._socket.const": {"blob_name": "trio._socket", "blob_size": 14623, "input_size": 18849}, "module.trio._ssl.const": {"blob_name": "trio._ssl", "blob_size": 13562, "input_size": 15747}, "module.trio._subprocess.const": {"blob_name": "trio._subprocess", "blob_size": 21878, "input_size": 24089}, "module.trio._subprocess_platform.const": {"blob_name": "trio._subprocess_platform", "blob_size": 2304, "input_size": 2979}, "module.trio._subprocess_platform.windows.const": {"blob_name": "trio._subprocess_platform.windows", "blob_size": 325, "input_size": 541}, "module.trio._sync.const": {"blob_name": "trio._sync", "blob_size": 22254, "input_size": 25467}, "module.trio._threads.const": {"blob_name": "trio._threads", "blob_size": 15679, "input_size": 17468}, "module.trio._timeouts.const": {"blob_name": "trio._timeouts", "blob_size": 4170, "input_size": 4952}, "module.trio._util.const": {"blob_name": "trio._util", "blob_size": 6288, "input_size": 8106}, "module.trio._version.const": {"blob_name": "trio._version", "blob_size": 130, "input_size": 271}, "module.trio._wait_for_object.const": {"blob_name": "trio._wait_for_object", "blob_size": 999, "input_size": 1475}, "module.trio._windows_pipes.const": {"blob_name": "trio._windows_pipes", "blob_size": 1860, "input_size": 2941}, "module.trio.abc.const": {"blob_name": "trio.abc", "blob_size": 449, "input_size": 497}, "module.trio.const": {"blob_name": "trio", "blob_size": 2950, "input_size": 3176}, "module.trio.from_thread.const": {"blob_name": "trio.from_thread", "blob_size": 461, "input_size": 616}, "module.trio.lowlevel.const": {"blob_name": "trio.lowlevel", "blob_size": 1913, "input_size": 1638}, "module.trio.socket.const": {"blob_name": "trio.socket", "blob_size": 1055, "input_size": 1400}, "module.trio.to_thread.const": {"blob_name": "trio.to_thread", "blob_size": 246, "input_size": 372}, "module.twisted._threads._convenience.const": {"blob_name": "twisted._threads._convenience", "blob_size": 773, "input_size": 1136}, "module.twisted._threads._ithreads.const": {"blob_name": "twisted._threads._ithreads", "blob_size": 1873, "input_size": 2395}, "module.twisted._threads._memory.const": {"blob_name": "twisted._threads._memory", "blob_size": 1251, "input_size": 1855}, "module.twisted._threads._pool.const": {"blob_name": "twisted._threads._pool", "blob_size": 2582, "input_size": 3402}, "module.twisted._threads._team.const": {"blob_name": "twisted._threads._team", "blob_size": 5721, "input_size": 6813}, "module.twisted._threads._threadworker.const": {"blob_name": "twisted._threads._threadworker", "blob_size": 2422, "input_size": 3224}, "module.twisted._threads.const": {"blob_name": "twisted._threads", "blob_size": 662, "input_size": 1043}, "module.twisted._version.const": {"blob_name": "twisted._version", "blob_size": 228, "input_size": 418}, "module.twisted.const": {"blob_name": "twisted", "blob_size": 320, "input_size": 584}, "module.twisted.internet._baseprocess.const": {"blob_name": "twisted.internet._baseprocess", "blob_size": 1222, "input_size": 1875}, "module.twisted.internet._dumbwin32proc.const": {"blob_name": "twisted.internet._dumbwin32proc", "blob_size": 6427, "input_size": 9063}, "module.twisted.internet._idna.const": {"blob_name": "twisted.internet._idna", "blob_size": 1197, "input_size": 1482}, "module.twisted.internet._newtls.const": {"blob_name": "twisted.internet._newtls", "blob_size": 6276, "input_size": 7353}, "module.twisted.internet._pollingfile.const": {"blob_name": "twisted.internet._pollingfile", "blob_size": 3769, "input_size": 5489}, "module.twisted.internet._posixstdio.const": {"blob_name": "twisted.internet._posixstdio", "blob_size": 2111, "input_size": 3294}, "module.twisted.internet._producer_helpers.const": {"blob_name": "twisted.internet._producer_helpers", "blob_size": 2688, "input_size": 3485}, "module.twisted.internet._resolver.const": {"blob_name": "twisted.internet._resolver", "blob_size": 5894, "input_size": 7321}, "module.twisted.internet._signals.const": {"blob_name": "twisted.internet._signals", "blob_size": 9994, "input_size": 12604}, "module.twisted.internet._sslverify.const": {"blob_name": "twisted.internet._sslverify", "blob_size": 50238, "input_size": 57688}, "module.twisted.internet._win32stdio.const": {"blob_name": "twisted.internet._win32stdio", "blob_size": 1939, "input_size": 3116}, "module.twisted.internet.abstract.const": {"blob_name": "twisted.internet.abstract", "blob_size": 12045, "input_size": 14614}, "module.twisted.internet.address.const": {"blob_name": "twisted.internet.address", "blob_size": 3329, "input_size": 4566}, "module.twisted.internet.base.const": {"blob_name": "twisted.internet.base", "blob_size": 23205, "input_size": 30190}, "module.twisted.internet.const": {"blob_name": "twisted.internet", "blob_size": 741, "input_size": 1004}, "module.twisted.internet.default.const": {"blob_name": "twisted.internet.default", "blob_size": 824, "input_size": 1112}, "module.twisted.internet.defer.const": {"blob_name": "twisted.internet.defer", "blob_size": 56996, "input_size": 63685}, "module.twisted.internet.endpoints.const": {"blob_name": "twisted.internet.endpoints", "blob_size": 54717, "input_size": 62865}, "module.twisted.internet.epollreactor.const": {"blob_name": "twisted.internet.epollreactor", "blob_size": 4091, "input_size": 5451}, "module.twisted.internet.error.const": {"blob_name": "twisted.internet.error", "blob_size": 8450, "input_size": 11042}, "module.twisted.internet.fdesc.const": {"blob_name": "twisted.internet.fdesc", "blob_size": 1956, "input_size": 2555}, "module.twisted.internet.interfaces.const": {"blob_name": "twisted.internet.interfaces", "blob_size": 93808, "input_size": 102399}, "module.twisted.internet.main.const": {"blob_name": "twisted.internet.main", "blob_size": 715, "input_size": 1078}, "module.twisted.internet.pollreactor.const": {"blob_name": "twisted.internet.pollreactor", "blob_size": 3500, "input_size": 4731}, "module.twisted.internet.posixbase.const": {"blob_name": "twisted.internet.posixbase", "blob_size": 10367, "input_size": 13287}, "module.twisted.internet.process.const": {"blob_name": "twisted.internet.process", "blob_size": 18041, "input_size": 23108}, "module.twisted.internet.protocol.const": {"blob_name": "twisted.internet.protocol", "blob_size": 19793, "input_size": 24080}, "module.twisted.internet.reactor.const": {"blob_name": "twisted.internet.reactor", "blob_size": 1842, "input_size": 2047}, "module.twisted.internet.selectreactor.const": {"blob_name": "twisted.internet.selectreactor", "blob_size": 2891, "input_size": 4316}, "module.twisted.internet.ssl.const": {"blob_name": "twisted.internet.ssl", "blob_size": 6440, "input_size": 7591}, "module.twisted.internet.stdio.const": {"blob_name": "twisted.internet.stdio", "blob_size": 902, "input_size": 1104}, "module.twisted.internet.task.const": {"blob_name": "twisted.internet.task", "blob_size": 21257, "input_size": 25552}, "module.twisted.internet.tcp.const": {"blob_name": "twisted.internet.tcp", "blob_size": 30291, "input_size": 36716}, "module.twisted.internet.threads.const": {"blob_name": "twisted.internet.threads", "blob_size": 3757, "input_size": 4534}, "module.twisted.internet.udp.const": {"blob_name": "twisted.internet.udp", "blob_size": 10024, "input_size": 13142}, "module.twisted.internet.unix.const": {"blob_name": "twisted.internet.unix", "blob_size": 10384, "input_size": 13917}, "module.twisted.internet.win32eventreactor.const": {"blob_name": "twisted.internet.win32eventreactor", "blob_size": 9441, "input_size": 11552}, "module.twisted.logger._buffer.const": {"blob_name": "twisted.logger._buffer", "blob_size": 1409, "input_size": 1931}, "module.twisted.logger._capture.const": {"blob_name": "twisted.logger._capture", "blob_size": 446, "input_size": 762}, "module.twisted.logger._file.const": {"blob_name": "twisted.logger._file", "blob_size": 1834, "input_size": 2451}, "module.twisted.logger._filter.const": {"blob_name": "twisted.logger._filter", "blob_size": 4832, "input_size": 5940}, "module.twisted.logger._flatten.const": {"blob_name": "twisted.logger._flatten", "blob_size": 2900, "input_size": 3608}, "module.twisted.logger._format.const": {"blob_name": "twisted.logger._format", "blob_size": 8793, "input_size": 10404}, "module.twisted.logger._global.const": {"blob_name": "twisted.logger._global", "blob_size": 6350, "input_size": 7646}, "module.twisted.logger._interfaces.const": {"blob_name": "twisted.logger._interfaces", "blob_size": 2456, "input_size": 2943}, "module.twisted.logger._io.const": {"blob_name": "twisted.logger._io", "blob_size": 3166, "input_size": 4620}, "module.twisted.logger._json.const": {"blob_name": "twisted.logger._json", "blob_size": 5304, "input_size": 6808}, "module.twisted.logger._legacy.const": {"blob_name": "twisted.logger._legacy", "blob_size": 2587, "input_size": 3499}, "module.twisted.logger._levels.const": {"blob_name": "twisted.logger._levels", "blob_size": 2833, "input_size": 3422}, "module.twisted.logger._logger.const": {"blob_name": "twisted.logger._logger", "blob_size": 7796, "input_size": 9039}, "module.twisted.logger._observer.const": {"blob_name": "twisted.logger._observer", "blob_size": 2190, "input_size": 2980}, "module.twisted.logger._stdlib.const": {"blob_name": "twisted.logger._stdlib", "blob_size": 3535, "input_size": 4780}, "module.twisted.logger.const": {"blob_name": "twisted.logger", "blob_size": 3364, "input_size": 3713}, "module.twisted.plugin.const": {"blob_name": "twisted.plugin", "blob_size": 3964, "input_size": 5437}, "module.twisted.plugins.const": {"blob_name": "twisted.plugins", "blob_size": 667, "input_size": 1003}, "module.twisted.protocols.const": {"blob_name": "twisted.protocols", "blob_size": 375, "input_size": 635}, "module.twisted.protocols.policies.const": {"blob_name": "twisted.protocols.policies", "blob_size": 11131, "input_size": 14666}, "module.twisted.protocols.tls.const": {"blob_name": "twisted.protocols.tls", "blob_size": 21516, "input_size": 24566}, "module.twisted.python._inotify.const": {"blob_name": "twisted.python._inotify", "blob_size": 1773, "input_size": 2654}, "module.twisted.python._tzhelper.const": {"blob_name": "twisted.python._tzhelper", "blob_size": 2486, "input_size": 3410}, "module.twisted.python.compat.const": {"blob_name": "twisted.python.compat", "blob_size": 10423, "input_size": 12980}, "module.twisted.python.components.const": {"blob_name": "twisted.python.components", "blob_size": 10228, "input_size": 12118}, "module.twisted.python.const": {"blob_name": "twisted.python", "blob_size": 594, "input_size": 973}, "module.twisted.python.context.const": {"blob_name": "twisted.python.context", "blob_size": 3217, "input_size": 3919}, "module.twisted.python.deprecate.const": {"blob_name": "twisted.python.deprecate", "blob_size": 19705, "input_size": 21966}, "module.twisted.python.failure.const": {"blob_name": "twisted.python.failure", "blob_size": 11990, "input_size": 15306}, "module.twisted.python.filepath.const": {"blob_name": "twisted.python.filepath", "blob_size": 41858, "input_size": 48674}, "module.twisted.python.lockfile.const": {"blob_name": "twisted.python.lockfile", "blob_size": 2531, "input_size": 3642}, "module.twisted.python.log.const": {"blob_name": "twisted.python.log", "blob_size": 14741, "input_size": 18308}, "module.twisted.python.modules.const": {"blob_name": "twisted.python.modules", "blob_size": 17508, "input_size": 20591}, "module.twisted.python.randbytes.const": {"blob_name": "twisted.python.randbytes", "blob_size": 2414, "input_size": 3359}, "module.twisted.python.reflect.const": {"blob_name": "twisted.python.reflect", "blob_size": 13257, "input_size": 15904}, "module.twisted.python.runtime.const": {"blob_name": "twisted.python.runtime", "blob_size": 3389, "input_size": 4696}, "module.twisted.python.sendmsg.const": {"blob_name": "twisted.python.sendmsg", "blob_size": 2046, "input_size": 2529}, "module.twisted.python.systemd.const": {"blob_name": "twisted.python.systemd", "blob_size": 3309, "input_size": 4083}, "module.twisted.python.threadable.const": {"blob_name": "twisted.python.threadable", "blob_size": 1769, "input_size": 2678}, "module.twisted.python.threadpool.const": {"blob_name": "twisted.python.threadpool", "blob_size": 7942, "input_size": 9978}, "module.twisted.python.util.const": {"blob_name": "twisted.python.util", "blob_size": 15857, "input_size": 20578}, "module.twisted.python.versions.const": {"blob_name": "twisted.python.versions", "blob_size": 305, "input_size": 440}, "module.twisted.python.win32.const": {"blob_name": "twisted.python.win32", "blob_size": 3403, "input_size": 4350}, "module.twisted.python.zippath.const": {"blob_name": "twisted.python.zippath", "blob_size": 6469, "input_size": 8609}, "module.typing_extensions.const": {"blob_name": "typing_extensions", "blob_size": 64892, "input_size": 77306}, "module.view_players.const": {"blob_name": "view_players", "blob_size": 7176, "input_size": 11047}, "module.voucher_validator.const": {"blob_name": "voucher_validator", "blob_size": 4796, "input_size": 6537}, "module.win32com.client.CLSIDToClass.const": {"blob_name": "win32com.client.CLSIDToClass", "blob_size": 1684, "input_size": 1979}, "module.win32com.client.build.const": {"blob_name": "win32com.client.build", "blob_size": 6472, "input_size": 9969}, "module.win32com.client.const": {"blob_name": "win32com.client", "blob_size": 10765, "input_size": 13711}, "module.win32com.client.dynamic.const": {"blob_name": "win32com.client.dynamic", "blob_size": 7124, "input_size": 10519}, "module.win32com.client.gencache.const": {"blob_name": "win32com.client.gencache", "blob_size": 9949, "input_size": 12223}, "module.win32com.client.genpy.const": {"blob_name": "win32com.client.genpy", "blob_size": 12889, "input_size": 19055}, "module.win32com.client.makepy.const": {"blob_name": "win32com.client.makepy", "blob_size": 4884, "input_size": 6879}, "module.win32com.client.selecttlb.const": {"blob_name": "win32com.client.selecttlb", "blob_size": 1690, "input_size": 2845}, "module.win32com.client.util.const": {"blob_name": "win32com.client.util", "blob_size": 1964, "input_size": 2865}, "module.win32com.const": {"blob_name": "win32com", "blob_size": 754, "input_size": 1471}, "module.win32con.const": {"blob_name": "win32con", "blob_size": 86040, "input_size": 156190}, "module.win32ctypes.const": {"blob_name": "win32ctypes", "blob_size": 263, "input_size": 488}, "module.win32ctypes.core._winerrors.const": {"blob_name": "win32ctypes.core._winerrors", "blob_size": 157, "input_size": 297}, "module.win32ctypes.core.const": {"blob_name": "win32ctypes.core", "blob_size": 1170, "input_size": 1961}, "module.win32ctypes.pywin32.const": {"blob_name": "win32ctypes.pywin32", "blob_size": 412, "input_size": 706}, "module.win32ctypes.pywin32.pywintypes.const": {"blob_name": "win32ctypes.pywin32.pywintypes", "blob_size": 559, "input_size": 1027}, "module.win32ctypes.pywin32.win32api.const": {"blob_name": "win32ctypes.pywin32.win32api", "blob_size": 5802, "input_size": 6773}, "module.win32ctypes.pywin32.win32cred.const": {"blob_name": "win32ctypes.pywin32.win32cred", "blob_size": 2710, "input_size": 3567}, "module.win32ctypes.version.const": {"blob_name": "win32ctypes.version", "blob_size": 141, "input_size": 282}, "module.win32evtlogutil.const": {"blob_name": "win32evtlogutil", "blob_size": 2815, "input_size": 3924}, "module.win32timezone.const": {"blob_name": "win32timezone", "blob_size": 13780, "input_size": 18051}, "module.win32ui-preLoad.const": {"blob_name": "win32ui-preLoad", "blob_size": 131, "input_size": 259}, "module.winerror.const": {"blob_name": "winerror", "blob_size": 76642, "input_size": 138083}, "module.wmi.const": {"blob_name": "wmi", "blob_size": 29156, "input_size": 35501}, "module.xmlrpc.server.const": {"blob_name": "xmlrpc.server", "blob_size": 16636, "input_size": 20347}, "module.zipp._functools.const": {"blob_name": "zipp._functools", "blob_size": 524, "input_size": 781}, "module.zipp.compat.const": {"blob_name": "zipp.compat", "blob_size": 278, "input_size": 525}, "module.zipp.compat.py310.const": {"blob_name": "zipp.compat.py310", "blob_size": 185, "input_size": 378}, "module.zipp.const": {"blob_name": "zipp", "blob_size": 7894, "input_size": 10694}, "module.zipp.glob.const": {"blob_name": "zipp.glob", "blob_size": 2637, "input_size": 3652}, "module.zope.const": {"blob_name": "zope", "blob_size": 205, "input_size": 412}, "module.zope.interface._compat.const": {"blob_name": "zope.interface._compat", "blob_size": 2705, "input_size": 3323}, "module.zope.interface.adapter.const": {"blob_name": "zope.interface.adapter", "blob_size": 12552, "input_size": 14708}, "module.zope.interface.const": {"blob_name": "zope.interface", "blob_size": 2385, "input_size": 2867}, "module.zope.interface.declarations.const": {"blob_name": "zope.interface.declarations", "blob_size": 15367, "input_size": 18777}, "module.zope.interface.exceptions.const": {"blob_name": "zope.interface.exceptions", "blob_size": 3835, "input_size": 5281}, "module.zope.interface.interface.const": {"blob_name": "zope.interface.interface", "blob_size": 11153, "input_size": 15821}, "module.zope.interface.interfaces.const": {"blob_name": "zope.interface.interfaces", "blob_size": 46356, "input_size": 51441}, "module.zope.interface.ro.const": {"blob_name": "zope.interface.ro", "blob_size": 9729, "input_size": 12517}, "total": 275321}