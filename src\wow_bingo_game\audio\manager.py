"""
WOW Bingo Game - Audio Manager
=============================

Comprehensive audio management system for the game including:
- Background music playback
- Sound effects management
- Voice announcements
- Audio mixing and volume control
- Cross-platform audio support

Features:
- Pygame-based audio engine
- Async audio operations
- Audio caching for performance
- Volume control per audio type
- Audio format support (MP3, WAV, OGG)
"""

import asyncio
import os
from pathlib import Path
from typing import Dict, Optional, Union
import threading
import time

import pygame
from loguru import logger

from ..core.config import AudioConfig


class AudioManager:
    """Centralized audio management system."""
    
    def __init__(self, config: Optional[AudioConfig] = None):
        """Initialize audio manager.
        
        Args:
            config: Audio configuration, uses default if None
        """
        self.config = config or AudioConfig()
        self.initialized = False
        self.mixer_initialized = False
        
        # Audio channels
        self.music_channel: Optional[pygame.mixer.Channel] = None
        self.effects_channels: Dict[str, pygame.mixer.Channel] = {}
        self.voice_channel: Optional[pygame.mixer.Channel] = None
        
        # Audio cache
        self.sound_cache: Dict[str, pygame.mixer.Sound] = {}
        self.music_cache: Dict[str, str] = {}
        
        # Current playback state
        self.current_music: Optional[str] = None
        self.music_playing = False
        self.music_paused = False
        
        # Threading for async operations
        self.audio_thread: Optional[threading.Thread] = None
        self.audio_queue: asyncio.Queue = asyncio.Queue()
        
        logger.info("Audio manager created")
    
    def initialize(self) -> bool:
        """Initialize the audio system.
        
        Returns:
            True if initialization successful
        """
        try:
            if self.initialized:
                return True
                
            logger.info("Initializing audio system...")
            
            # Initialize pygame mixer
            pygame.mixer.pre_init(
                frequency=44100,
                size=-16,
                channels=2,
                buffer=1024
            )
            pygame.mixer.init()
            
            # Set number of channels for mixing
            pygame.mixer.set_num_channels(16)
            
            # Reserve channels for different audio types
            self.music_channel = pygame.mixer.Channel(0)
            self.voice_channel = pygame.mixer.Channel(1)
            
            # Reserve channels for sound effects
            for i in range(2, 8):
                self.effects_channels[f"effect_{i-2}"] = pygame.mixer.Channel(i)
            
            self.mixer_initialized = True
            self.initialized = True
            
            logger.info("Audio system initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize audio system: {e}")
            return False
    
    def cleanup(self) -> None:
        """Cleanup audio resources."""
        try:
            logger.info("Cleaning up audio system...")
            
            # Stop all audio
            self.stop_all_audio()
            
            # Clear caches
            self.sound_cache.clear()
            self.music_cache.clear()
            
            # Quit pygame mixer
            if self.mixer_initialized:
                pygame.mixer.quit()
                self.mixer_initialized = False
            
            self.initialized = False
            logger.info("Audio system cleanup completed")
            
        except Exception as e:
            logger.error(f"Audio cleanup error: {e}")
    
    async def load_background_music(self, file_path: Union[str, Path]) -> bool:
        """Load background music file.
        
        Args:
            file_path: Path to music file
            
        Returns:
            True if loaded successfully
        """
        try:
            if not self.initialized:
                if not self.initialize():
                    return False
            
            file_path = Path(file_path)
            if not file_path.exists():
                logger.warning(f"Music file not found: {file_path}")
                return False
            
            # Load music with pygame
            pygame.mixer.music.load(str(file_path))
            self.current_music = str(file_path)
            
            logger.info(f"Background music loaded: {file_path.name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load background music: {e}")
            return False
    
    async def play_background_music(self, loops: int = 0, fade_in_ms: int = 0) -> bool:
        """Play background music.
        
        Args:
            loops: Number of loops (-1 for infinite)
            fade_in_ms: Fade in duration in milliseconds
            
        Returns:
            True if playback started successfully
        """
        try:
            if not self.config.music_enabled:
                logger.debug("Background music disabled in config")
                return False
                
            if not self.current_music:
                logger.warning("No background music loaded")
                return False
            
            # Set volume
            pygame.mixer.music.set_volume(self.config.music_volume)
            
            # Play music
            if fade_in_ms > 0:
                pygame.mixer.music.play(loops=loops, fade_ms=fade_in_ms)
            else:
                pygame.mixer.music.play(loops=loops)
            
            self.music_playing = True
            self.music_paused = False
            
            logger.info("Background music playback started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to play background music: {e}")
            return False
    
    async def stop_background_music(self, fade_out_ms: int = 0) -> None:
        """Stop background music.
        
        Args:
            fade_out_ms: Fade out duration in milliseconds
        """
        try:
            if not self.music_playing:
                return
            
            if fade_out_ms > 0:
                pygame.mixer.music.fadeout(fade_out_ms)
                # Wait for fade out to complete
                await asyncio.sleep(fade_out_ms / 1000.0)
            else:
                pygame.mixer.music.stop()
            
            self.music_playing = False
            self.music_paused = False
            
            logger.info("Background music stopped")
            
        except Exception as e:
            logger.error(f"Failed to stop background music: {e}")
    
    async def pause_background_music(self) -> None:
        """Pause background music."""
        try:
            if self.music_playing and not self.music_paused:
                pygame.mixer.music.pause()
                self.music_paused = True
                logger.info("Background music paused")
                
        except Exception as e:
            logger.error(f"Failed to pause background music: {e}")
    
    async def resume_background_music(self) -> None:
        """Resume background music."""
        try:
            if self.music_playing and self.music_paused:
                pygame.mixer.music.unpause()
                self.music_paused = False
                logger.info("Background music resumed")
                
        except Exception as e:
            logger.error(f"Failed to resume background music: {e}")
    
    def set_music_volume(self, volume: float) -> None:
        """Set background music volume.
        
        Args:
            volume: Volume level (0.0 to 1.0)
        """
        try:
            volume = max(0.0, min(1.0, volume))
            pygame.mixer.music.set_volume(volume)
            self.config.music_volume = volume
            logger.debug(f"Music volume set to {volume}")
            
        except Exception as e:
            logger.error(f"Failed to set music volume: {e}")
    
    async def load_sound_effect(self, name: str, file_path: Union[str, Path]) -> bool:
        """Load a sound effect.
        
        Args:
            name: Unique name for the sound effect
            file_path: Path to sound file
            
        Returns:
            True if loaded successfully
        """
        try:
            if not self.initialized:
                if not self.initialize():
                    return False
            
            file_path = Path(file_path)
            if not file_path.exists():
                logger.warning(f"Sound file not found: {file_path}")
                return False
            
            # Load sound
            sound = pygame.mixer.Sound(str(file_path))
            self.sound_cache[name] = sound
            
            logger.debug(f"Sound effect loaded: {name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load sound effect '{name}': {e}")
            return False
    
    async def play_sound_effect(self, name: str, volume: Optional[float] = None) -> bool:
        """Play a sound effect.
        
        Args:
            name: Name of the sound effect
            volume: Volume override (uses config default if None)
            
        Returns:
            True if playback started successfully
        """
        try:
            if not self.config.sound_effects_enabled:
                return False
                
            if name not in self.sound_cache:
                logger.warning(f"Sound effect not loaded: {name}")
                return False
            
            sound = self.sound_cache[name]
            
            # Set volume
            if volume is None:
                volume = self.config.sound_effects_volume
            volume = max(0.0, min(1.0, volume))
            sound.set_volume(volume)
            
            # Find available channel
            channel = None
            for ch in self.effects_channels.values():
                if not ch.get_busy():
                    channel = ch
                    break
            
            if channel:
                channel.play(sound)
                logger.debug(f"Sound effect played: {name}")
                return True
            else:
                logger.warning("No available audio channels for sound effect")
                return False
                
        except Exception as e:
            logger.error(f"Failed to play sound effect '{name}': {e}")
            return False
    
    def stop_all_audio(self) -> None:
        """Stop all audio playback."""
        try:
            # Stop background music
            if self.music_playing:
                pygame.mixer.music.stop()
                self.music_playing = False
                self.music_paused = False
            
            # Stop all channels
            pygame.mixer.stop()
            
            logger.info("All audio stopped")
            
        except Exception as e:
            logger.error(f"Failed to stop all audio: {e}")
    
    def get_audio_info(self) -> Dict[str, any]:
        """Get current audio system information.
        
        Returns:
            Dictionary with audio system status
        """
        return {
            "initialized": self.initialized,
            "mixer_initialized": self.mixer_initialized,
            "music_enabled": self.config.music_enabled,
            "effects_enabled": self.config.sound_effects_enabled,
            "music_playing": self.music_playing,
            "music_paused": self.music_paused,
            "current_music": self.current_music,
            "loaded_sounds": list(self.sound_cache.keys()),
            "music_volume": self.config.music_volume,
            "effects_volume": self.config.sound_effects_volume,
        }


# Global audio manager instance
_audio_manager: Optional[AudioManager] = None


def get_audio_manager() -> AudioManager:
    """Get the global audio manager instance."""
    global _audio_manager
    if _audio_manager is None:
        _audio_manager = AudioManager()
    return _audio_manager
