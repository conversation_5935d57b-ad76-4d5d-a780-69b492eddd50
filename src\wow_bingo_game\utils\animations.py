"""
WOW Bingo Game - Animation Utilities
===================================

Animation utilities for smooth transitions and effects in the Flet UI.

Features:
- Fade in/out animations
- Slide transitions
- Scale animations
- Rotation effects
- Easing functions
- Performance optimized animations
"""

import asyncio
import math
from typing import Optional, Callable, Union
import time

import flet as ft
from loguru import logger


class EasingFunctions:
    """Collection of easing functions for smooth animations."""
    
    @staticmethod
    def linear(t: float) -> float:
        """Linear easing (no acceleration)."""
        return t
    
    @staticmethod
    def ease_in_quad(t: float) -> float:
        """Quadratic ease-in."""
        return t * t
    
    @staticmethod
    def ease_out_quad(t: float) -> float:
        """Quadratic ease-out."""
        return 1 - (1 - t) * (1 - t)
    
    @staticmethod
    def ease_in_out_quad(t: float) -> float:
        """Quadratic ease-in-out."""
        if t < 0.5:
            return 2 * t * t
        return 1 - pow(-2 * t + 2, 2) / 2
    
    @staticmethod
    def ease_in_cubic(t: float) -> float:
        """Cubic ease-in."""
        return t * t * t
    
    @staticmethod
    def ease_out_cubic(t: float) -> float:
        """Cubic ease-out."""
        return 1 - pow(1 - t, 3)
    
    @staticmethod
    def ease_in_out_cubic(t: float) -> float:
        """Cubic ease-in-out."""
        if t < 0.5:
            return 4 * t * t * t
        return 1 - pow(-2 * t + 2, 3) / 2
    
    @staticmethod
    def ease_in_sine(t: float) -> float:
        """Sine ease-in."""
        return 1 - math.cos((t * math.pi) / 2)
    
    @staticmethod
    def ease_out_sine(t: float) -> float:
        """Sine ease-out."""
        return math.sin((t * math.pi) / 2)
    
    @staticmethod
    def ease_in_out_sine(t: float) -> float:
        """Sine ease-in-out."""
        return -(math.cos(math.pi * t) - 1) / 2


class FadeTransition:
    """Fade transition animations."""
    
    @staticmethod
    async def fade_in(
        control: ft.Control,
        page: ft.Page,
        duration: float = 1.0,
        easing: Callable[[float], float] = EasingFunctions.ease_out_cubic,
        fps: int = 60
    ) -> None:
        """Fade in a control.
        
        Args:
            control: Flet control to animate
            page: Flet page for updates
            duration: Animation duration in seconds
            easing: Easing function
            fps: Animation frame rate
        """
        try:
            if not hasattr(control, 'opacity'):
                logger.warning(f"Control {type(control)} does not support opacity")
                return
            
            start_time = time.time()
            frame_duration = 1.0 / fps
            
            while True:
                elapsed = time.time() - start_time
                progress = min(elapsed / duration, 1.0)
                
                # Apply easing
                eased_progress = easing(progress)
                
                # Set opacity
                control.opacity = eased_progress
                await page.update_async()
                
                # Check if animation complete
                if progress >= 1.0:
                    break
                
                # Wait for next frame
                await asyncio.sleep(frame_duration)
                
        except Exception as e:
            logger.error(f"Fade in animation error: {e}")
    
    @staticmethod
    async def fade_out(
        control: ft.Control,
        page: ft.Page,
        duration: float = 1.0,
        easing: Callable[[float], float] = EasingFunctions.ease_in_cubic,
        fps: int = 60
    ) -> None:
        """Fade out a control.
        
        Args:
            control: Flet control to animate
            page: Flet page for updates
            duration: Animation duration in seconds
            easing: Easing function
            fps: Animation frame rate
        """
        try:
            if not hasattr(control, 'opacity'):
                logger.warning(f"Control {type(control)} does not support opacity")
                return
            
            start_time = time.time()
            frame_duration = 1.0 / fps
            
            while True:
                elapsed = time.time() - start_time
                progress = min(elapsed / duration, 1.0)
                
                # Apply easing
                eased_progress = easing(progress)
                
                # Set opacity (reverse for fade out)
                control.opacity = 1.0 - eased_progress
                await page.update_async()
                
                # Check if animation complete
                if progress >= 1.0:
                    break
                
                # Wait for next frame
                await asyncio.sleep(frame_duration)
                
        except Exception as e:
            logger.error(f"Fade out animation error: {e}")
    
    @staticmethod
    async def crossfade(
        control_out: ft.Control,
        control_in: ft.Control,
        page: ft.Page,
        duration: float = 1.0,
        easing: Callable[[float], float] = EasingFunctions.ease_in_out_cubic,
        fps: int = 60
    ) -> None:
        """Crossfade between two controls.
        
        Args:
            control_out: Control to fade out
            control_in: Control to fade in
            page: Flet page for updates
            duration: Animation duration in seconds
            easing: Easing function
            fps: Animation frame rate
        """
        try:
            # Run fade out and fade in concurrently
            await asyncio.gather(
                FadeTransition.fade_out(control_out, page, duration, easing, fps),
                FadeTransition.fade_in(control_in, page, duration, easing, fps)
            )
            
        except Exception as e:
            logger.error(f"Crossfade animation error: {e}")


class SlideTransition:
    """Slide transition animations."""
    
    @staticmethod
    async def slide_in_from_left(
        control: ft.Control,
        page: ft.Page,
        distance: float = 100.0,
        duration: float = 0.5,
        easing: Callable[[float], float] = EasingFunctions.ease_out_cubic,
        fps: int = 60
    ) -> None:
        """Slide control in from the left.
        
        Args:
            control: Flet control to animate
            page: Flet page for updates
            distance: Slide distance in pixels
            duration: Animation duration in seconds
            easing: Easing function
            fps: Animation frame rate
        """
        try:
            if not hasattr(control, 'offset'):
                logger.warning(f"Control {type(control)} does not support offset")
                return
            
            start_time = time.time()
            frame_duration = 1.0 / fps
            
            while True:
                elapsed = time.time() - start_time
                progress = min(elapsed / duration, 1.0)
                
                # Apply easing
                eased_progress = easing(progress)
                
                # Calculate offset
                offset_x = -distance * (1.0 - eased_progress)
                control.offset = ft.transform.Offset(offset_x, 0)
                await page.update_async()
                
                # Check if animation complete
                if progress >= 1.0:
                    break
                
                # Wait for next frame
                await asyncio.sleep(frame_duration)
                
        except Exception as e:
            logger.error(f"Slide in animation error: {e}")
    
    @staticmethod
    async def slide_out_to_right(
        control: ft.Control,
        page: ft.Page,
        distance: float = 100.0,
        duration: float = 0.5,
        easing: Callable[[float], float] = EasingFunctions.ease_in_cubic,
        fps: int = 60
    ) -> None:
        """Slide control out to the right.
        
        Args:
            control: Flet control to animate
            page: Flet page for updates
            distance: Slide distance in pixels
            duration: Animation duration in seconds
            easing: Easing function
            fps: Animation frame rate
        """
        try:
            if not hasattr(control, 'offset'):
                logger.warning(f"Control {type(control)} does not support offset")
                return
            
            start_time = time.time()
            frame_duration = 1.0 / fps
            
            while True:
                elapsed = time.time() - start_time
                progress = min(elapsed / duration, 1.0)
                
                # Apply easing
                eased_progress = easing(progress)
                
                # Calculate offset
                offset_x = distance * eased_progress
                control.offset = ft.transform.Offset(offset_x, 0)
                await page.update_async()
                
                # Check if animation complete
                if progress >= 1.0:
                    break
                
                # Wait for next frame
                await asyncio.sleep(frame_duration)
                
        except Exception as e:
            logger.error(f"Slide out animation error: {e}")


class ScaleTransition:
    """Scale transition animations."""
    
    @staticmethod
    async def scale_in(
        control: ft.Control,
        page: ft.Page,
        from_scale: float = 0.0,
        to_scale: float = 1.0,
        duration: float = 0.5,
        easing: Callable[[float], float] = EasingFunctions.ease_out_cubic,
        fps: int = 60
    ) -> None:
        """Scale control in.
        
        Args:
            control: Flet control to animate
            page: Flet page for updates
            from_scale: Starting scale
            to_scale: Ending scale
            duration: Animation duration in seconds
            easing: Easing function
            fps: Animation frame rate
        """
        try:
            if not hasattr(control, 'scale'):
                logger.warning(f"Control {type(control)} does not support scale")
                return
            
            start_time = time.time()
            frame_duration = 1.0 / fps
            scale_range = to_scale - from_scale
            
            while True:
                elapsed = time.time() - start_time
                progress = min(elapsed / duration, 1.0)
                
                # Apply easing
                eased_progress = easing(progress)
                
                # Calculate scale
                current_scale = from_scale + (scale_range * eased_progress)
                control.scale = ft.transform.Scale(current_scale)
                await page.update_async()
                
                # Check if animation complete
                if progress >= 1.0:
                    break
                
                # Wait for next frame
                await asyncio.sleep(frame_duration)
                
        except Exception as e:
            logger.error(f"Scale in animation error: {e}")


class AnimationSequence:
    """Sequence multiple animations."""
    
    def __init__(self):
        """Initialize animation sequence."""
        self.animations = []
    
    def add_animation(
        self,
        animation_func: Callable,
        *args,
        delay: float = 0.0,
        **kwargs
    ) -> 'AnimationSequence':
        """Add animation to sequence.
        
        Args:
            animation_func: Animation function to call
            *args: Animation function arguments
            delay: Delay before starting this animation
            **kwargs: Animation function keyword arguments
            
        Returns:
            Self for method chaining
        """
        self.animations.append({
            'func': animation_func,
            'args': args,
            'kwargs': kwargs,
            'delay': delay
        })
        return self
    
    async def play(self, concurrent: bool = False) -> None:
        """Play the animation sequence.
        
        Args:
            concurrent: If True, play all animations concurrently
        """
        try:
            if concurrent:
                # Play all animations concurrently
                tasks = []
                for anim in self.animations:
                    if anim['delay'] > 0:
                        tasks.append(self._delayed_animation(anim))
                    else:
                        tasks.append(anim['func'](*anim['args'], **anim['kwargs']))
                
                await asyncio.gather(*tasks)
            else:
                # Play animations sequentially
                for anim in self.animations:
                    if anim['delay'] > 0:
                        await asyncio.sleep(anim['delay'])
                    await anim['func'](*anim['args'], **anim['kwargs'])
                    
        except Exception as e:
            logger.error(f"Animation sequence error: {e}")
    
    async def _delayed_animation(self, anim: dict) -> None:
        """Play animation with delay."""
        await asyncio.sleep(anim['delay'])
        await anim['func'](*anim['args'], **anim['kwargs'])
