"""
WOW Bingo Game - Image Loader Utility
=====================================

Optimized image loading and caching system for the game.

Features:
- Async image loading
- Image caching for performance
- Format conversion and optimization
- Thumbnail generation
- Memory management
- Error handling and fallbacks
"""

import asyncio
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import hashlib
import time

from PIL import Image, ImageOps
import flet as ft
from loguru import logger


class ImageCache:
    """Image caching system for performance optimization."""
    
    def __init__(self, max_size_mb: int = 100, max_items: int = 1000):
        """Initialize image cache.
        
        Args:
            max_size_mb: Maximum cache size in megabytes
            max_items: Maximum number of cached items
        """
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.max_items = max_items
        self.cache: Dict[str, dict] = {}
        self.access_times: Dict[str, float] = {}
        self.current_size = 0
        
        logger.info(f"Image cache initialized: {max_size_mb}MB, {max_items} items")
    
    def _generate_key(self, file_path: Union[str, Path], size: Optional[Tuple[int, int]] = None) -> str:
        """Generate cache key for image.
        
        Args:
            file_path: Path to image file
            size: Optional size tuple for resized images
            
        Returns:
            Cache key string
        """
        path_str = str(file_path)
        size_str = f"_{size[0]}x{size[1]}" if size else ""
        key_string = f"{path_str}{size_str}"
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def get(self, file_path: Union[str, Path], size: Optional[Tuple[int, int]] = None) -> Optional[dict]:
        """Get cached image.
        
        Args:
            file_path: Path to image file
            size: Optional size tuple
            
        Returns:
            Cached image data or None if not found
        """
        key = self._generate_key(file_path, size)
        if key in self.cache:
            self.access_times[key] = time.time()
            return self.cache[key]
        return None
    
    def put(self, file_path: Union[str, Path], image_data: dict, size: Optional[Tuple[int, int]] = None) -> None:
        """Cache image data.
        
        Args:
            file_path: Path to image file
            image_data: Image data to cache
            size: Optional size tuple
        """
        key = self._generate_key(file_path, size)
        
        # Estimate size (rough approximation)
        estimated_size = len(str(image_data)) * 2  # Rough estimate
        
        # Check if we need to make space
        while (self.current_size + estimated_size > self.max_size_bytes or 
               len(self.cache) >= self.max_items) and self.cache:
            self._evict_oldest()
        
        # Add to cache
        self.cache[key] = image_data
        self.access_times[key] = time.time()
        self.current_size += estimated_size
        
        logger.debug(f"Cached image: {Path(file_path).name}")
    
    def _evict_oldest(self) -> None:
        """Evict the oldest accessed item from cache."""
        if not self.access_times:
            return
            
        oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        
        if oldest_key in self.cache:
            del self.cache[oldest_key]
            del self.access_times[oldest_key]
            # Rough size reduction
            self.current_size = max(0, self.current_size - 1000)
    
    def clear(self) -> None:
        """Clear all cached images."""
        self.cache.clear()
        self.access_times.clear()
        self.current_size = 0
        logger.info("Image cache cleared")
    
    def get_stats(self) -> Dict[str, any]:
        """Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        return {
            "items": len(self.cache),
            "max_items": self.max_items,
            "size_mb": self.current_size / (1024 * 1024),
            "max_size_mb": self.max_size_bytes / (1024 * 1024),
            "hit_rate": getattr(self, '_hit_count', 0) / max(getattr(self, '_access_count', 1), 1)
        }


class ImageLoader:
    """Optimized image loader with caching and format support."""
    
    def __init__(self, cache_size_mb: int = 100):
        """Initialize image loader.
        
        Args:
            cache_size_mb: Cache size in megabytes
        """
        self.cache = ImageCache(cache_size_mb)
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp'}
        
        logger.info("Image loader initialized")
    
    async def load_image(
        self,
        file_path: Union[str, Path],
        size: Optional[Tuple[int, int]] = None,
        fit: ft.ImageFit = ft.ImageFit.COVER,
        quality: int = 85
    ) -> Optional[ft.Image]:
        """Load image with optional resizing and caching.
        
        Args:
            file_path: Path to image file
            size: Optional target size (width, height)
            fit: Image fit mode
            quality: JPEG quality (1-100)
            
        Returns:
            Flet Image control or None if failed
        """
        try:
            file_path = Path(file_path)
            
            # Check if file exists
            if not file_path.exists():
                logger.warning(f"Image file not found: {file_path}")
                return None
            
            # Check format support
            if file_path.suffix.lower() not in self.supported_formats:
                logger.warning(f"Unsupported image format: {file_path.suffix}")
                return None
            
            # Check cache first
            cached_data = self.cache.get(file_path, size)
            if cached_data:
                return ft.Image(
                    src=cached_data['src'],
                    width=cached_data.get('width'),
                    height=cached_data.get('height'),
                    fit=fit
                )
            
            # Load and process image
            processed_path = await self._process_image(file_path, size, quality)
            if not processed_path:
                return None
            
            # Create Flet image
            image_data = {
                'src': str(processed_path),
                'width': size[0] if size else None,
                'height': size[1] if size else None,
                'original_path': str(file_path)
            }
            
            # Cache the processed image data
            self.cache.put(file_path, image_data, size)
            
            return ft.Image(
                src=image_data['src'],
                width=image_data['width'],
                height=image_data['height'],
                fit=fit
            )
            
        except Exception as e:
            logger.error(f"Failed to load image {file_path}: {e}")
            return None
    
    async def _process_image(
        self,
        file_path: Path,
        size: Optional[Tuple[int, int]],
        quality: int
    ) -> Optional[Path]:
        """Process image (resize, optimize) if needed.
        
        Args:
            file_path: Original image path
            size: Target size
            quality: JPEG quality
            
        Returns:
            Path to processed image or original if no processing needed
        """
        try:
            # If no size specified, return original
            if not size:
                return file_path
            
            # Create cache directory for processed images
            cache_dir = Path("cache/images")
            cache_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate processed image filename
            size_suffix = f"_{size[0]}x{size[1]}"
            processed_name = f"{file_path.stem}{size_suffix}{file_path.suffix}"
            processed_path = cache_dir / processed_name
            
            # Check if processed version already exists
            if processed_path.exists():
                return processed_path
            
            # Process image with PIL
            with Image.open(file_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # Resize image maintaining aspect ratio
                img.thumbnail(size, Image.Resampling.LANCZOS)
                
                # Save processed image
                save_kwargs = {}
                if file_path.suffix.lower() in ['.jpg', '.jpeg']:
                    save_kwargs['quality'] = quality
                    save_kwargs['optimize'] = True
                
                img.save(processed_path, **save_kwargs)
                
                logger.debug(f"Processed image: {file_path.name} -> {processed_name}")
                return processed_path
                
        except Exception as e:
            logger.error(f"Failed to process image {file_path}: {e}")
            return file_path  # Return original on error
    
    async def load_images_batch(
        self,
        file_paths: List[Union[str, Path]],
        size: Optional[Tuple[int, int]] = None,
        fit: ft.ImageFit = ft.ImageFit.COVER,
        max_concurrent: int = 5
    ) -> List[Optional[ft.Image]]:
        """Load multiple images concurrently.
        
        Args:
            file_paths: List of image file paths
            size: Optional target size
            fit: Image fit mode
            max_concurrent: Maximum concurrent loads
            
        Returns:
            List of Flet Image controls (None for failed loads)
        """
        try:
            # Create semaphore to limit concurrent loads
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def load_with_semaphore(path):
                async with semaphore:
                    return await self.load_image(path, size, fit)
            
            # Load all images concurrently
            tasks = [load_with_semaphore(path) for path in file_paths]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Filter out exceptions and return results
            images = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Failed to load image {file_paths[i]}: {result}")
                    images.append(None)
                else:
                    images.append(result)
            
            logger.info(f"Batch loaded {len([img for img in images if img])} of {len(file_paths)} images")
            return images
            
        except Exception as e:
            logger.error(f"Batch image loading failed: {e}")
            return [None] * len(file_paths)
    
    async def preload_directory(
        self,
        directory: Union[str, Path],
        size: Optional[Tuple[int, int]] = None,
        recursive: bool = False
    ) -> int:
        """Preload all images in a directory.
        
        Args:
            directory: Directory path
            size: Optional target size
            recursive: Search subdirectories
            
        Returns:
            Number of images successfully preloaded
        """
        try:
            directory = Path(directory)
            if not directory.exists():
                logger.warning(f"Directory not found: {directory}")
                return 0
            
            # Find all image files
            pattern = "**/*" if recursive else "*"
            image_files = []
            
            for file_path in directory.glob(pattern):
                if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                    image_files.append(file_path)
            
            if not image_files:
                logger.info(f"No images found in {directory}")
                return 0
            
            # Load images in batches
            images = await self.load_images_batch(image_files, size)
            success_count = len([img for img in images if img])
            
            logger.info(f"Preloaded {success_count} images from {directory}")
            return success_count
            
        except Exception as e:
            logger.error(f"Failed to preload directory {directory}: {e}")
            return 0
    
    def clear_cache(self) -> None:
        """Clear image cache."""
        self.cache.clear()
    
    def get_cache_stats(self) -> Dict[str, any]:
        """Get cache statistics."""
        return self.cache.get_stats()
