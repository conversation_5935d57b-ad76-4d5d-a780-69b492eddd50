"""
WOW Bingo Game - Splash Screen Component
=======================================

Modern Flet-based splash screen with animated image transitions,
background music, and loading animations.

Features:
- Animated image slideshow with smooth transitions
- Background music playback
- Loading animation with dots
- Version and creator information
- Fullscreen display
- Skip functionality
- Performance optimized
"""

import asyncio
import os
from pathlib import Path
from typing import List, Optional
import time

import flet as ft
from loguru import logger

from ..core.config import GameConfig
from ..audio.manager import AudioManager
from ..utils.image_loader import ImageLoader
from ..utils.animations import FadeTransition


class SplashScreen:
    """Modern splash screen with animations and music."""
    
    def __init__(self, page: ft.Page, config: GameConfig, dev_mode: bool = False):
        """Initialize splash screen.
        
        Args:
            page: Flet page object
            config: Game configuration
            dev_mode: Development mode flag
        """
        self.page = page
        self.config = config
        self.dev_mode = dev_mode
        self.audio_manager = AudioManager()
        self.image_loader = ImageLoader()
        
        # Splash screen settings
        self.splash_duration = 5.0 if dev_mode else 8.0  # Shorter in dev mode
        self.image_display_duration = 2.0
        self.fade_duration = 1.0
        self.skip_enabled = True
        
        # UI components
        self.container: Optional[ft.Container] = None
        self.image_container: Optional[ft.Container] = None
        self.loading_text: Optional[ft.Text] = None
        self.progress_bar: Optional[ft.ProgressBar] = None
        
        # Animation state
        self.splash_images: List[ft.Image] = []
        self.current_image_index = 0
        self.animation_running = False
        self.skip_requested = False
        
        # Timing
        self.start_time = 0.0
        self.last_image_change = 0.0
        
        logger.info("Splash screen initialized")
    
    async def load_splash_assets(self) -> bool:
        """Load splash screen assets (images and music).
        
        Returns:
            True if assets loaded successfully
        """
        try:
            logger.info("Loading splash screen assets...")
            
            # Load splash images
            splash_dir = Path("assets/Splash_screen")
            if splash_dir.exists():
                image_files = sorted([
                    f for f in splash_dir.iterdir()
                    if f.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']
                ])
                
                for img_file in image_files:
                    try:
                        # Load image with Flet
                        img = ft.Image(
                            src=str(img_file),
                            fit=ft.ImageFit.COVER,
                            width=self.page.window_width,
                            height=self.page.window_height,
                            opacity=0.0  # Start invisible for fade effect
                        )
                        self.splash_images.append(img)
                        logger.debug(f"Loaded splash image: {img_file.name}")
                        
                    except Exception as e:
                        logger.warning(f"Failed to load image {img_file}: {e}")
                        
                logger.info(f"Loaded {len(self.splash_images)} splash images")
            else:
                logger.warning("Splash screen directory not found")
            
            # Load background music
            music_file = Path("assets/splash_screen-background-music/Welcome-music.mp3")
            if music_file.exists():
                try:
                    await self.audio_manager.load_background_music(str(music_file))
                    logger.info("Splash screen music loaded")
                except Exception as e:
                    logger.warning(f"Failed to load splash music: {e}")
            else:
                logger.warning("Splash screen music not found")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to load splash assets: {e}")
            return False
    
    def create_ui(self) -> ft.Container:
        """Create the splash screen UI.
        
        Returns:
            Main container with splash screen content
        """
        # Version text
        version_text = ft.Text(
            f"WOW Bingo Game v{self.config.version}",
            size=16,
            color=ft.colors.WHITE70,
            weight=ft.FontWeight.BOLD
        )
        
        # Creator text
        creator_text = ft.Text(
            "Created with ♥",
            size=14,
            color=ft.colors.WHITE54
        )
        
        # Loading text with animated dots
        self.loading_text = ft.Text(
            "Loading",
            size=18,
            color=ft.colors.WHITE,
            weight=ft.FontWeight.BOLD
        )
        
        # Progress bar
        self.progress_bar = ft.ProgressBar(
            width=300,
            height=4,
            color=ft.colors.BLUE_400,
            bgcolor=ft.colors.WHITE24
        )
        
        # Skip button (if enabled)
        skip_button = None
        if self.skip_enabled:
            skip_button = ft.ElevatedButton(
                "Skip",
                on_click=self._on_skip_clicked,
                style=ft.ButtonStyle(
                    color=ft.colors.WHITE,
                    bgcolor=ft.colors.BLACK26
                )
            )
        
        # Image container for slideshow
        self.image_container = ft.Container(
            width=self.page.window_width,
            height=self.page.window_height,
            content=ft.Stack(self.splash_images) if self.splash_images else None
        )
        
        # Overlay with text and controls
        overlay = ft.Container(
            width=self.page.window_width,
            height=self.page.window_height,
            content=ft.Column([
                # Top spacer
                ft.Container(expand=True),
                
                # Loading section
                ft.Container(
                    content=ft.Column([
                        self.loading_text,
                        ft.Container(height=10),
                        self.progress_bar
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                    alignment=ft.alignment.center
                ),
                
                # Bottom section with version and skip
                ft.Container(
                    content=ft.Row([
                        ft.Column([
                            version_text,
                            creator_text
                        ], spacing=5),
                        
                        ft.Container(expand=True),
                        
                        skip_button if skip_button else ft.Container()
                    ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                    padding=ft.padding.all(20)
                )
            ]),
            bgcolor=ft.colors.with_opacity(0.3, ft.colors.BLACK)
        )
        
        # Main container with background and overlay
        self.container = ft.Container(
            width=self.page.window_width,
            height=self.page.window_height,
            content=ft.Stack([
                # Background (solid color if no images)
                ft.Container(
                    width=self.page.window_width,
                    height=self.page.window_height,
                    bgcolor=ft.colors.BLUE_GREY_900
                ),
                
                # Image slideshow
                self.image_container,
                
                # Text overlay
                overlay
            ]),
            alignment=ft.alignment.center
        )
        
        return self.container
    
    async def _on_skip_clicked(self, e) -> None:
        """Handle skip button click."""
        logger.info("Splash screen skip requested")
        self.skip_requested = True
    
    async def animate_loading_text(self) -> None:
        """Animate the loading text with dots."""
        dots_states = ["Loading", "Loading.", "Loading..", "Loading..."]
        dots_index = 0
        
        while self.animation_running and not self.skip_requested:
            self.loading_text.value = dots_states[dots_index]
            dots_index = (dots_index + 1) % len(dots_states)
            await self.page.update_async()
            await asyncio.sleep(0.5)
    
    async def animate_images(self) -> None:
        """Animate the image slideshow with fade transitions."""
        if not self.splash_images:
            return
            
        while self.animation_running and not self.skip_requested:
            current_time = time.time()
            
            # Check if it's time to change image
            if current_time - self.last_image_change >= self.image_display_duration:
                # Fade out current image
                if self.current_image_index < len(self.splash_images):
                    current_img = self.splash_images[self.current_image_index]
                    await FadeTransition.fade_out(current_img, self.page, duration=0.5)
                
                # Move to next image
                self.current_image_index = (self.current_image_index + 1) % len(self.splash_images)
                next_img = self.splash_images[self.current_image_index]
                
                # Fade in next image
                await FadeTransition.fade_in(next_img, self.page, duration=0.5)
                
                self.last_image_change = current_time
                logger.debug(f"Switched to splash image {self.current_image_index + 1}")
            
            await asyncio.sleep(0.1)
    
    async def animate_progress(self) -> None:
        """Animate the progress bar."""
        progress = 0.0
        progress_step = 1.0 / (self.splash_duration * 10)  # Update 10 times per second
        
        while self.animation_running and not self.skip_requested and progress < 1.0:
            self.progress_bar.value = progress
            await self.page.update_async()
            progress += progress_step
            await asyncio.sleep(0.1)
        
        # Ensure progress reaches 100%
        self.progress_bar.value = 1.0
        await self.page.update_async()
    
    async def show(self) -> bool:
        """Show the splash screen with animations.
        
        Returns:
            True if splash completed successfully
        """
        try:
            logger.info("Starting splash screen...")
            self.start_time = time.time()
            self.last_image_change = self.start_time
            
            # Load assets
            if not await self.load_splash_assets():
                logger.warning("Failed to load splash assets, showing minimal splash")
            
            # Create and show UI
            splash_ui = self.create_ui()
            self.page.add(splash_ui)
            await self.page.update_async()
            
            # Start background music
            try:
                await self.audio_manager.play_background_music()
            except Exception as e:
                logger.warning(f"Failed to play splash music: {e}")
            
            # Start animations
            self.animation_running = True
            
            # Run animations concurrently
            animation_tasks = [
                self.animate_loading_text(),
                self.animate_images(),
                self.animate_progress()
            ]
            
            # Wait for splash duration or skip
            splash_task = asyncio.create_task(asyncio.sleep(self.splash_duration))
            animation_task = asyncio.create_task(asyncio.gather(*animation_tasks))
            
            # Wait for either splash duration or skip
            done, pending = await asyncio.wait(
                [splash_task, animation_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # Cancel pending tasks
            for task in pending:
                task.cancel()
            
            # Stop animations
            self.animation_running = False
            
            # Stop background music
            try:
                await self.audio_manager.stop_background_music()
            except Exception as e:
                logger.warning(f"Failed to stop splash music: {e}")
            
            # Remove splash screen
            self.page.remove(splash_ui)
            await self.page.update_async()
            
            elapsed_time = time.time() - self.start_time
            logger.info(f"Splash screen completed in {elapsed_time:.2f} seconds")
            
            return True
            
        except Exception as e:
            logger.error(f"Splash screen error: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Cleanup splash screen resources."""
        try:
            self.animation_running = False
            
            # Cleanup audio
            if self.audio_manager:
                await self.audio_manager.cleanup()
            
            # Clear references
            self.splash_images.clear()
            self.container = None
            self.image_container = None
            
            logger.info("Splash screen cleanup completed")
            
        except Exception as e:
            logger.error(f"Splash screen cleanup error: {e}")
