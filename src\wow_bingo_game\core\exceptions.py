"""
WOW Bingo Game - Custom Exceptions
==================================

Custom exception classes for the WOW Bingo Game application.

Features:
- Hierarchical exception structure
- Detailed error information
- Error categorization
- Logging integration
"""

from typing import Optional, Dict, Any


class WOWBingoError(Exception):
    """Base exception for WOW Bingo Game."""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        """Initialize WOW Bingo error.
        
        Args:
            message: Error message
            error_code: Optional error code
            details: Optional error details
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}


class ConfigurationError(WOWBingoError):
    """Configuration-related errors."""
    pass


class GameEngineError(WOWBingoError):
    """Game engine errors."""
    pass


class AudioError(WOWBingoError):
    """Audio system errors."""
    pass


class DatabaseError(WOWBingoError):
    """Database-related errors."""
    pass


class UIError(WOWBingoError):
    """User interface errors."""
    pass


class BuildError(WOWBingoError):
    """Build system errors."""
    pass


class PerformanceError(WOWBingoError):
    """Performance-related errors."""
    pass
