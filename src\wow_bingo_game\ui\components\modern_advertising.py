"""
WOW Bingo Game - Modern GPU-Optimized Advertising Component
==========================================================

Beautiful, attractive, modern and professional advertising section with
GPU acceleration for smooth performance and stunning visual effects.

Features:
- GPU-accelerated rendering for smooth animations
- Modern gradient backgrounds with glass morphism effects
- Professional typography with dynamic font scaling
- Smooth particle effects and animations
- Responsive design that adapts to screen size
- Multiple animation modes (scroll, fade, pulse, wave)
- Advanced visual effects (glow, shadow, reflection)
- Performance monitoring and automatic quality adjustment
"""

import asyncio
import math
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

import pygame
import numpy as np
from loguru import logger

from ...utils.hardware_acceleration import HardwareAccelerationManager
from ...utils.performance import PerformanceMonitor
from ...core.config import WOWBingoConfig


class AnimationMode(Enum):
    """Animation modes for advertising display."""
    SCROLL = "scroll"
    FADE = "fade"
    PULSE = "pulse"
    WAVE = "wave"
    TYPEWRITER = "typewriter"
    PARTICLE = "particle"


class VisualQuality(Enum):
    """Visual quality levels based on hardware capabilities."""
    ULTRA = "ultra"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class AdvertisingSettings:
    """Advertising display settings."""
    text: str = "WOW Games - Premium Bingo Experience"
    font_family: str = "Arial"
    font_size: int = 32
    animation_mode: AnimationMode = AnimationMode.SCROLL
    animation_speed: float = 1.0
    text_color: Tuple[int, int, int] = (255, 215, 0)  # Gold
    background_enabled: bool = True
    glow_enabled: bool = True
    particles_enabled: bool = True
    auto_quality: bool = True


class ModernAdvertisingComponent:
    """Modern GPU-optimized advertising component."""

    def __init__(self, config: WOWBingoConfig):
        """Initialize the modern advertising component.

        Args:
            config: Application configuration
        """
        self.config = config
        self.settings = AdvertisingSettings()

        # Hardware acceleration
        self.hw_accel_manager: Optional[HardwareAccelerationManager] = None
        self.performance_monitor: Optional[PerformanceMonitor] = None
        self.gpu_available = False
        self.visual_quality = VisualQuality.MEDIUM

        # Rendering state
        self.initialized = False
        self.surface_cache: Dict[str, pygame.Surface] = {}
        self.font_cache: Dict[str, pygame.font.Font] = {}
        self.animation_time = 0.0
        self.last_frame_time = time.time()

        # Visual effects
        self.particles: List[Dict[str, Any]] = []
        self.gradient_cache: Dict[str, pygame.Surface] = {}
        self.glow_cache: Dict[str, pygame.Surface] = {}

        # Performance tracking
        self.frame_count = 0
        self.performance_samples = []

        logger.info("Modern advertising component created")

    async def initialize(self) -> bool:
        """Initialize the advertising component with hardware detection.

        Returns:
            True if initialization successful
        """
        try:
            logger.info("Initializing modern advertising component...")

            # Initialize hardware acceleration
            self.hw_accel_manager = HardwareAccelerationManager()
            await self.hw_accel_manager.initialize()

            # Get optimization profile
            optimization_profile = self.hw_accel_manager.get_optimization_profile()
            self.gpu_available = optimization_profile.get('gpu_acceleration', False)

            # Set visual quality based on hardware
            render_quality = optimization_profile.get('render_quality', 'medium')
            self.visual_quality = VisualQuality(render_quality)

            # Initialize performance monitoring
            self.performance_monitor = PerformanceMonitor(
                target_fps=optimization_profile.get('max_fps', 60),
                memory_limit_mb=optimization_profile.get('cache_size_mb', 256)
            )
            self.performance_monitor.start()

            # Apply hardware-specific optimizations
            self._apply_hardware_optimizations(optimization_profile)

            # Initialize visual effects based on quality
            self._initialize_visual_effects()

            self.initialized = True
            logger.info(f"Modern advertising initialized: GPU={self.gpu_available}, Quality={self.visual_quality.value}")

            return True

        except Exception as e:
            logger.error(f"Failed to initialize modern advertising: {e}")
            # Fallback to basic mode
            self.gpu_available = False
            self.visual_quality = VisualQuality.LOW
            self.initialized = True
            return False

    def _apply_hardware_optimizations(self, profile: Dict[str, Any]) -> None:
        """Apply hardware-specific optimizations.

        Args:
            profile: Hardware optimization profile
        """
        try:
            # Adjust settings based on GPU availability
            if self.gpu_available:
                # GPU acceleration available - enable all effects
                self.settings.glow_enabled = True
                self.settings.particles_enabled = True
                self.settings.background_enabled = True

                # Use advanced animation modes
                if profile.get('render_quality') == 'ultra':
                    self.settings.animation_mode = AnimationMode.PARTICLE
                elif profile.get('render_quality') == 'high':
                    self.settings.animation_mode = AnimationMode.WAVE
                else:
                    self.settings.animation_mode = AnimationMode.SCROLL

                logger.info("🚀 GPU optimizations applied: All effects enabled")

            else:
                # CPU fallback - optimize for performance
                self.settings.glow_enabled = False
                self.settings.particles_enabled = False
                self.settings.background_enabled = True
                self.settings.animation_mode = AnimationMode.SCROLL

                logger.info("🔄 CPU optimizations applied: Effects reduced for performance")

            # Adjust animation speed based on target FPS
            target_fps = profile.get('max_fps', 60)
            if target_fps >= 90:
                self.settings.animation_speed = 1.5
            elif target_fps >= 60:
                self.settings.animation_speed = 1.0
            else:
                self.settings.animation_speed = 0.7

        except Exception as e:
            logger.error(f"Failed to apply hardware optimizations: {e}")

    def _initialize_visual_effects(self) -> None:
        """Initialize visual effects based on quality level."""
        try:
            # Initialize particles for high-quality modes
            if self.settings.particles_enabled and self.visual_quality in [VisualQuality.ULTRA, VisualQuality.HIGH]:
                self._initialize_particles()

            # Pre-generate gradient backgrounds
            if self.settings.background_enabled:
                self._generate_gradient_backgrounds()

            logger.info(f"Visual effects initialized for {self.visual_quality.value} quality")

        except Exception as e:
            logger.error(f"Failed to initialize visual effects: {e}")

    def _initialize_particles(self) -> None:
        """Initialize particle system for advanced visual effects."""
        try:
            particle_count = 50 if self.visual_quality == VisualQuality.ULTRA else 25

            for i in range(particle_count):
                particle = {
                    'x': np.random.uniform(0, 1),
                    'y': np.random.uniform(0, 1),
                    'vx': np.random.uniform(-0.001, 0.001),
                    'vy': np.random.uniform(-0.001, 0.001),
                    'size': np.random.uniform(1, 3),
                    'alpha': np.random.uniform(0.1, 0.3),
                    'color': (255, 215, 0),  # Gold
                    'life': np.random.uniform(0.5, 1.0)
                }
                self.particles.append(particle)

            logger.debug(f"Initialized {len(self.particles)} particles")

        except Exception as e:
            logger.error(f"Failed to initialize particles: {e}")

    def _generate_gradient_backgrounds(self) -> None:
        """Pre-generate gradient backgrounds for different sizes."""
        try:
            # Common sizes for caching
            sizes = [(800, 100), (1200, 120), (1600, 150), (1920, 180)]

            for width, height in sizes:
                gradient_key = f"gradient_{width}_{height}"

                if self.gpu_available and self.visual_quality in [VisualQuality.ULTRA, VisualQuality.HIGH]:
                    # Advanced gradient with glass morphism effect
                    gradient_surf = self._create_glass_morphism_background(width, height)
                else:
                    # Simple gradient for performance
                    gradient_surf = self._create_simple_gradient(width, height)

                self.gradient_cache[gradient_key] = gradient_surf

            logger.debug(f"Generated {len(self.gradient_cache)} gradient backgrounds")

        except Exception as e:
            logger.error(f"Failed to generate gradient backgrounds: {e}")

    def _create_glass_morphism_background(self, width: int, height: int) -> pygame.Surface:
        """Create modern glass morphism background effect.

        Args:
            width: Background width
            height: Background height

        Returns:
            Surface with glass morphism effect
        """
        try:
            # Create surface with alpha
            surface = pygame.Surface((width, height), pygame.SRCALPHA)

            # Create gradient layers
            for i in range(height):
                # Calculate gradient colors
                ratio = i / height

                # Primary gradient (dark blue to light blue)
                r1 = int(20 * (1 - ratio) + 60 * ratio)
                g1 = int(30 * (1 - ratio) + 120 * ratio)
                b1 = int(80 * (1 - ratio) + 200 * ratio)

                # Secondary gradient (purple tint)
                r2 = int(40 * (1 - ratio) + 100 * ratio)
                g2 = int(20 * (1 - ratio) + 60 * ratio)
                b2 = int(100 * (1 - ratio) + 180 * ratio)

                # Blend gradients
                blend_factor = 0.7
                r = int(r1 * blend_factor + r2 * (1 - blend_factor))
                g = int(g1 * blend_factor + g2 * (1 - blend_factor))
                b = int(b1 * blend_factor + b2 * (1 - blend_factor))

                # Add transparency for glass effect
                alpha = int(180 * (0.8 + 0.2 * ratio))

                color = (r, g, b, alpha)
                pygame.draw.line(surface, color, (0, i), (width, i))

            # Add subtle noise for texture
            if self.visual_quality == VisualQuality.ULTRA:
                self._add_noise_texture(surface)

            # Add border glow
            self._add_border_glow(surface)

            return surface

        except Exception as e:
            logger.error(f"Failed to create glass morphism background: {e}")
            return self._create_simple_gradient(width, height)

    def _create_simple_gradient(self, width: int, height: int) -> pygame.Surface:
        """Create simple gradient background for performance.

        Args:
            width: Background width
            height: Background height

        Returns:
            Surface with simple gradient
        """
        try:
            surface = pygame.Surface((width, height), pygame.SRCALPHA)

            # Simple two-color gradient
            for i in range(height):
                ratio = i / height
                r = int(30 * (1 - ratio) + 80 * ratio)
                g = int(50 * (1 - ratio) + 120 * ratio)
                b = int(100 * (1 - ratio) + 180 * ratio)
                alpha = 200

                color = (r, g, b, alpha)
                pygame.draw.line(surface, color, (0, i), (width, i))

            return surface

        except Exception as e:
            logger.error(f"Failed to create simple gradient: {e}")
            # Return solid color fallback
            surface = pygame.Surface((width, height), pygame.SRCALPHA)
            surface.fill((50, 80, 150, 200))
            return surface

    def _add_noise_texture(self, surface: pygame.Surface) -> None:
        """Add subtle noise texture for glass effect.

        Args:
            surface: Surface to add noise to
        """
        try:
            width, height = surface.get_size()
            noise_density = 0.02  # 2% of pixels

            for _ in range(int(width * height * noise_density)):
                x = np.random.randint(0, width)
                y = np.random.randint(0, height)
                brightness = np.random.randint(-20, 20)

                # Get current pixel color
                try:
                    current_color = surface.get_at((x, y))
                    new_color = (
                        max(0, min(255, current_color[0] + brightness)),
                        max(0, min(255, current_color[1] + brightness)),
                        max(0, min(255, current_color[2] + brightness)),
                        current_color[3]
                    )
                    surface.set_at((x, y), new_color)
                except:
                    pass  # Skip if pixel access fails

        except Exception as e:
            logger.debug(f"Failed to add noise texture: {e}")

    def _add_border_glow(self, surface: pygame.Surface) -> None:
        """Add border glow effect.

        Args:
            surface: Surface to add glow to
        """
        try:
            width, height = surface.get_size()
            glow_color = (255, 215, 0, 50)  # Gold glow

            # Top and bottom borders
            for i in range(3):
                pygame.draw.line(surface, glow_color, (0, i), (width, i))
                pygame.draw.line(surface, glow_color, (0, height - 1 - i), (width, height - 1 - i))

            # Left and right borders
            for i in range(3):
                pygame.draw.line(surface, glow_color, (i, 0), (i, height))
                pygame.draw.line(surface, glow_color, (width - 1 - i, 0), (width - 1 - i, height))

        except Exception as e:
            logger.debug(f"Failed to add border glow: {e}")

    def update(self, delta_time: float) -> None:
        """Update animation state and effects.

        Args:
            delta_time: Time since last update in seconds
        """
        try:
            # Update animation time
            self.animation_time += delta_time * self.settings.animation_speed

            # Update particles
            if self.settings.particles_enabled:
                self._update_particles(delta_time)

            # Monitor performance
            if self.performance_monitor:
                self.performance_monitor.record_frame()

                # Auto-adjust quality based on performance
                if self.settings.auto_quality:
                    self._auto_adjust_quality()

            self.frame_count += 1

        except Exception as e:
            logger.error(f"Failed to update advertising component: {e}")

    def _update_particles(self, delta_time: float) -> None:
        """Update particle system.

        Args:
            delta_time: Time since last update
        """
        try:
            for particle in self.particles:
                # Update position
                particle['x'] += particle['vx'] * delta_time * 60  # 60 FPS reference
                particle['y'] += particle['vy'] * delta_time * 60

                # Update life
                particle['life'] -= delta_time * 0.5

                # Wrap around screen
                if particle['x'] < 0:
                    particle['x'] = 1
                elif particle['x'] > 1:
                    particle['x'] = 0

                if particle['y'] < 0:
                    particle['y'] = 1
                elif particle['y'] > 1:
                    particle['y'] = 0

                # Reset particle if life expired
                if particle['life'] <= 0:
                    particle['x'] = np.random.uniform(0, 1)
                    particle['y'] = np.random.uniform(0, 1)
                    particle['life'] = np.random.uniform(0.5, 1.0)

        except Exception as e:
            logger.debug(f"Failed to update particles: {e}")

    def _auto_adjust_quality(self) -> None:
        """Automatically adjust quality based on performance."""
        try:
            if self.frame_count % 60 == 0:  # Check every 60 frames
                current_fps = self.performance_monitor.metrics.current_fps
                target_fps = self.performance_monitor.metrics.target_fps

                # Adjust quality if performance is poor
                if current_fps < target_fps * 0.8:  # Less than 80% of target
                    if self.visual_quality == VisualQuality.ULTRA:
                        self.visual_quality = VisualQuality.HIGH
                        self.settings.particles_enabled = True
                        logger.info("🔽 Quality reduced to HIGH for better performance")
                    elif self.visual_quality == VisualQuality.HIGH:
                        self.visual_quality = VisualQuality.MEDIUM
                        self.settings.particles_enabled = False
                        logger.info("🔽 Quality reduced to MEDIUM for better performance")
                    elif self.visual_quality == VisualQuality.MEDIUM:
                        self.visual_quality = VisualQuality.LOW
                        self.settings.glow_enabled = False
                        logger.info("🔽 Quality reduced to LOW for better performance")

                # Increase quality if performance is good
                elif current_fps > target_fps * 0.95 and self.gpu_available:  # More than 95% of target
                    if self.visual_quality == VisualQuality.LOW:
                        self.visual_quality = VisualQuality.MEDIUM
                        self.settings.glow_enabled = True
                        logger.info("🔼 Quality increased to MEDIUM")
                    elif self.visual_quality == VisualQuality.MEDIUM:
                        self.visual_quality = VisualQuality.HIGH
                        self.settings.particles_enabled = True
                        logger.info("🔼 Quality increased to HIGH")
                    elif self.visual_quality == VisualQuality.HIGH:
                        self.visual_quality = VisualQuality.ULTRA
                        logger.info("🔼 Quality increased to ULTRA")

        except Exception as e:
            logger.debug(f"Failed to auto-adjust quality: {e}")

    def render(self, surface: pygame.Surface, rect: pygame.Rect) -> None:
        """Render the modern advertising component.

        Args:
            surface: Target surface to render to
            rect: Rectangle defining the advertising area
        """
        try:
            if not self.initialized:
                return

            # Get or create background
            background = self._get_background(rect.width, rect.height)

            # Draw background
            if background and self.settings.background_enabled:
                surface.blit(background, rect.topleft)

            # Draw particles
            if self.settings.particles_enabled:
                self._render_particles(surface, rect)

            # Draw text with animation
            self._render_animated_text(surface, rect)

        except Exception as e:
            logger.error(f"Failed to render advertising component: {e}")

    def _get_background(self, width: int, height: int) -> Optional[pygame.Surface]:
        """Get cached or create background surface.

        Args:
            width: Background width
            height: Background height

        Returns:
            Background surface or None
        """
        try:
            gradient_key = f"gradient_{width}_{height}"

            # Check cache first
            if gradient_key in self.gradient_cache:
                return self.gradient_cache[gradient_key]

            # Create new background
            if self.gpu_available and self.visual_quality in [VisualQuality.ULTRA, VisualQuality.HIGH]:
                background = self._create_glass_morphism_background(width, height)
            else:
                background = self._create_simple_gradient(width, height)

            # Cache it (with size limit)
            if len(self.gradient_cache) < 10:
                self.gradient_cache[gradient_key] = background

            return background

        except Exception as e:
            logger.error(f"Failed to get background: {e}")
            return None

    def _render_particles(self, surface: pygame.Surface, rect: pygame.Rect) -> None:
        """Render particle effects.

        Args:
            surface: Target surface
            rect: Rendering area
        """
        try:
            for particle in self.particles:
                # Calculate screen position
                x = int(rect.x + particle['x'] * rect.width)
                y = int(rect.y + particle['y'] * rect.height)

                # Calculate alpha based on life
                alpha = int(particle['alpha'] * particle['life'] * 255)
                alpha = max(0, min(255, alpha))

                if alpha > 10:  # Only draw visible particles
                    # Create particle surface
                    size = int(particle['size'])
                    if size > 0:
                        particle_surf = pygame.Surface((size * 2, size * 2), pygame.SRCALPHA)
                        color = (*particle['color'], alpha)
                        pygame.draw.circle(particle_surf, color, (size, size), size)

                        # Add glow effect for ultra quality
                        if self.visual_quality == VisualQuality.ULTRA:
                            glow_color = (*particle['color'], alpha // 3)
                            pygame.draw.circle(particle_surf, glow_color, (size, size), size + 1)

                        surface.blit(particle_surf, (x - size, y - size))

        except Exception as e:
            logger.debug(f"Failed to render particles: {e}")

    def _render_animated_text(self, surface: pygame.Surface, rect: pygame.Rect) -> None:
        """Render animated text based on current animation mode.

        Args:
            surface: Target surface
            rect: Rendering area
        """
        try:
            # Get or create font
            font = self._get_font()
            if not font:
                return

            # Render text based on animation mode
            if self.settings.animation_mode == AnimationMode.SCROLL:
                self._render_scrolling_text(surface, rect, font)
            elif self.settings.animation_mode == AnimationMode.FADE:
                self._render_fading_text(surface, rect, font)
            elif self.settings.animation_mode == AnimationMode.PULSE:
                self._render_pulsing_text(surface, rect, font)
            elif self.settings.animation_mode == AnimationMode.WAVE:
                self._render_wave_text(surface, rect, font)
            elif self.settings.animation_mode == AnimationMode.TYPEWRITER:
                self._render_typewriter_text(surface, rect, font)
            elif self.settings.animation_mode == AnimationMode.PARTICLE:
                self._render_particle_text(surface, rect, font)
            else:
                # Fallback to simple static text
                self._render_static_text(surface, rect, font)

        except Exception as e:
            logger.error(f"Failed to render animated text: {e}")

    def _get_font(self) -> Optional[pygame.font.Font]:
        """Get cached or create font.

        Returns:
            Font object or None
        """
        try:
            font_key = f"{self.settings.font_family}_{self.settings.font_size}"

            if font_key in self.font_cache:
                return self.font_cache[font_key]

            # Create new font
            font = pygame.font.SysFont(self.settings.font_family, self.settings.font_size, bold=True)

            # Cache it (with size limit)
            if len(self.font_cache) < 20:
                self.font_cache[font_key] = font

            return font

        except Exception as e:
            logger.error(f"Failed to get font: {e}")
            return None

    def _render_scrolling_text(self, surface: pygame.Surface, rect: pygame.Rect, font: pygame.font.Font) -> None:
        """Render horizontally scrolling text.

        Args:
            surface: Target surface
            rect: Rendering area
            font: Font to use
        """
        try:
            # Render text surface
            text_color = self._get_animated_color()
            text_surf = font.render(self.settings.text, True, text_color)

            # Add glow effect
            if self.settings.glow_enabled:
                text_surf = self._add_text_glow(text_surf, font)

            # Calculate scroll position
            text_width = text_surf.get_width()
            scroll_distance = (self.animation_time * 100) % (rect.width + text_width)
            text_x = rect.x + rect.width - scroll_distance
            text_y = rect.y + (rect.height - text_surf.get_height()) // 2

            # Set clipping to prevent overflow
            original_clip = surface.get_clip()
            surface.set_clip(rect)

            # Draw text
            surface.blit(text_surf, (text_x, text_y))

            # Draw repeated text for seamless scrolling
            if text_x + text_width < rect.x + rect.width:
                repeat_x = text_x + text_width + 50  # Gap between repetitions
                surface.blit(text_surf, (repeat_x, text_y))

            # Reset clipping
            surface.set_clip(original_clip)

        except Exception as e:
            logger.error(f"Failed to render scrolling text: {e}")

    def _render_fading_text(self, surface: pygame.Surface, rect: pygame.Rect, font: pygame.font.Font) -> None:
        """Render fading in/out text.

        Args:
            surface: Target surface
            rect: Rendering area
            font: Font to use
        """
        try:
            # Calculate fade alpha
            fade_cycle = math.sin(self.animation_time * 2) * 0.5 + 0.5  # 0 to 1
            alpha = int(fade_cycle * 255)

            # Render text with alpha
            text_color = self._get_animated_color()
            text_surf = font.render(self.settings.text, True, text_color)

            # Apply alpha
            text_surf.set_alpha(alpha)

            # Add glow effect
            if self.settings.glow_enabled:
                text_surf = self._add_text_glow(text_surf, font, alpha)

            # Center text
            text_x = rect.x + (rect.width - text_surf.get_width()) // 2
            text_y = rect.y + (rect.height - text_surf.get_height()) // 2

            surface.blit(text_surf, (text_x, text_y))

        except Exception as e:
            logger.error(f"Failed to render fading text: {e}")

    def _render_pulsing_text(self, surface: pygame.Surface, rect: pygame.Rect, font: pygame.font.Font) -> None:
        """Render pulsing text with size animation.

        Args:
            surface: Target surface
            rect: Rendering area
            font: Font to use
        """
        try:
            # Calculate pulse scale
            pulse = math.sin(self.animation_time * 4) * 0.2 + 1.0  # 0.8 to 1.2
            scaled_size = int(self.settings.font_size * pulse)

            # Create scaled font
            scaled_font = pygame.font.SysFont(self.settings.font_family, scaled_size, bold=True)

            # Render text
            text_color = self._get_animated_color()
            text_surf = scaled_font.render(self.settings.text, True, text_color)

            # Add glow effect
            if self.settings.glow_enabled:
                text_surf = self._add_text_glow(text_surf, scaled_font)

            # Center text
            text_x = rect.x + (rect.width - text_surf.get_width()) // 2
            text_y = rect.y + (rect.height - text_surf.get_height()) // 2

            surface.blit(text_surf, (text_x, text_y))

        except Exception as e:
            logger.error(f"Failed to render pulsing text: {e}")

    def _render_wave_text(self, surface: pygame.Surface, rect: pygame.Rect, font: pygame.font.Font) -> None:
        """Render text with wave animation.

        Args:
            surface: Target surface
            rect: Rendering area
            font: Font to use
        """
        try:
            text = self.settings.text
            text_color = self._get_animated_color()

            # Calculate total text width for centering
            total_width = font.size(text)[0]
            start_x = rect.x + (rect.width - total_width) // 2

            # Render each character with wave offset
            char_x = start_x
            for i, char in enumerate(text):
                if char == ' ':
                    char_x += font.size(' ')[0]
                    continue

                # Calculate wave offset
                wave_offset = math.sin(self.animation_time * 3 + i * 0.5) * 10
                char_y = rect.y + (rect.height - font.get_height()) // 2 + wave_offset

                # Render character
                char_surf = font.render(char, True, text_color)

                # Add glow effect
                if self.settings.glow_enabled:
                    char_surf = self._add_text_glow(char_surf, font)

                surface.blit(char_surf, (char_x, char_y))
                char_x += font.size(char)[0]

        except Exception as e:
            logger.error(f"Failed to render wave text: {e}")

    def _render_typewriter_text(self, surface: pygame.Surface, rect: pygame.Rect, font: pygame.font.Font) -> None:
        """Render text with typewriter animation.

        Args:
            surface: Target surface
            rect: Rendering area
            font: Font to use
        """
        try:
            # Calculate how many characters to show
            chars_per_second = 10
            total_chars = len(self.settings.text)
            visible_chars = int((self.animation_time * chars_per_second) % (total_chars + 20))

            if visible_chars > total_chars:
                visible_chars = total_chars

            # Get visible text
            visible_text = self.settings.text[:visible_chars]

            if visible_text:
                # Render visible text
                text_color = self._get_animated_color()
                text_surf = font.render(visible_text, True, text_color)

                # Add glow effect
                if self.settings.glow_enabled:
                    text_surf = self._add_text_glow(text_surf, font)

                # Position text
                text_x = rect.x + 20  # Left aligned with padding
                text_y = rect.y + (rect.height - text_surf.get_height()) // 2

                surface.blit(text_surf, (text_x, text_y))

                # Add cursor
                if visible_chars < total_chars:
                    cursor_x = text_x + text_surf.get_width()
                    cursor_y = text_y
                    cursor_height = text_surf.get_height()

                    # Blinking cursor
                    if int(self.animation_time * 2) % 2:
                        pygame.draw.line(surface, text_color,
                                       (cursor_x, cursor_y),
                                       (cursor_x, cursor_y + cursor_height), 2)

        except Exception as e:
            logger.error(f"Failed to render typewriter text: {e}")

    def _render_particle_text(self, surface: pygame.Surface, rect: pygame.Rect, font: pygame.font.Font) -> None:
        """Render text with particle effects.

        Args:
            surface: Target surface
            rect: Rendering area
            font: Font to use
        """
        try:
            # Render base text
            text_color = self._get_animated_color()
            text_surf = font.render(self.settings.text, True, text_color)

            # Center text
            text_x = rect.x + (rect.width - text_surf.get_width()) // 2
            text_y = rect.y + (rect.height - text_surf.get_height()) // 2

            # Add particle trail effect
            if self.visual_quality in [VisualQuality.ULTRA, VisualQuality.HIGH]:
                self._add_particle_trail(surface, text_x, text_y, text_surf.get_size())

            # Add glow effect
            if self.settings.glow_enabled:
                text_surf = self._add_text_glow(text_surf, font)

            surface.blit(text_surf, (text_x, text_y))

        except Exception as e:
            logger.error(f"Failed to render particle text: {e}")

    def _render_static_text(self, surface: pygame.Surface, rect: pygame.Rect, font: pygame.font.Font) -> None:
        """Render static text (fallback).

        Args:
            surface: Target surface
            rect: Rendering area
            font: Font to use
        """
        try:
            text_color = self.settings.text_color
            text_surf = font.render(self.settings.text, True, text_color)

            # Center text
            text_x = rect.x + (rect.width - text_surf.get_width()) // 2
            text_y = rect.y + (rect.height - text_surf.get_height()) // 2

            surface.blit(text_surf, (text_x, text_y))

        except Exception as e:
            logger.error(f"Failed to render static text: {e}")

    def _get_animated_color(self) -> Tuple[int, int, int]:
        """Get animated color based on current time.

        Returns:
            RGB color tuple
        """
        try:
            if self.visual_quality in [VisualQuality.ULTRA, VisualQuality.HIGH]:
                # Smooth color cycling for high quality
                cycle = math.sin(self.animation_time * 0.5) * 0.5 + 0.5  # 0 to 1

                # Golden color palette
                gold_colors = [
                    (255, 215, 0),    # Gold
                    (255, 223, 50),   # Light gold
                    (255, 193, 37),   # Dark gold
                    (255, 235, 100)   # Bright gold
                ]

                # Interpolate between colors
                color_index = cycle * (len(gold_colors) - 1)
                base_index = int(color_index)
                blend_factor = color_index - base_index

                if base_index >= len(gold_colors) - 1:
                    return gold_colors[-1]

                color1 = gold_colors[base_index]
                color2 = gold_colors[base_index + 1]

                return (
                    int(color1[0] * (1 - blend_factor) + color2[0] * blend_factor),
                    int(color1[1] * (1 - blend_factor) + color2[1] * blend_factor),
                    int(color1[2] * (1 - blend_factor) + color2[2] * blend_factor)
                )
            else:
                # Static color for performance
                return self.settings.text_color

        except Exception as e:
            logger.debug(f"Failed to get animated color: {e}")
            return self.settings.text_color

    def _add_text_glow(self, text_surf: pygame.Surface, font: pygame.font.Font, alpha: int = 255) -> pygame.Surface:
        """Add glow effect to text surface.

        Args:
            text_surf: Original text surface
            font: Font used for text
            alpha: Alpha value for glow

        Returns:
            Surface with glow effect
        """
        try:
            if not self.settings.glow_enabled:
                return text_surf

            # Create glow surface
            glow_padding = 8
            glow_width = text_surf.get_width() + glow_padding * 2
            glow_height = text_surf.get_height() + glow_padding * 2
            glow_surf = pygame.Surface((glow_width, glow_height), pygame.SRCALPHA)

            # Glow color (lighter version of text color)
            glow_color = (255, 235, 100, min(alpha // 2, 80))  # Light gold glow

            # Create glow by rendering text with offsets
            if self.visual_quality == VisualQuality.ULTRA:
                # High quality glow with multiple layers
                offsets = [
                    (2, 2), (2, -2), (-2, 2), (-2, -2),
                    (3, 0), (-3, 0), (0, 3), (0, -3),
                    (1, 1), (1, -1), (-1, 1), (-1, -1)
                ]
            elif self.visual_quality == VisualQuality.HIGH:
                # Medium quality glow
                offsets = [(2, 2), (2, -2), (-2, 2), (-2, -2)]
            else:
                # Simple glow
                offsets = [(1, 1), (1, -1), (-1, 1), (-1, -1)]

            # Render glow text
            glow_text = font.render(self.settings.text, True, glow_color)

            # Apply glow offsets
            for offset_x, offset_y in offsets:
                glow_surf.blit(glow_text, (glow_padding + offset_x, glow_padding + offset_y))

            # Blit original text on top
            glow_surf.blit(text_surf, (glow_padding, glow_padding))

            return glow_surf

        except Exception as e:
            logger.debug(f"Failed to add text glow: {e}")
            return text_surf

    def _add_particle_trail(self, surface: pygame.Surface, text_x: int, text_y: int, text_size: Tuple[int, int]) -> None:
        """Add particle trail effect around text.

        Args:
            surface: Target surface
            text_x: Text X position
            text_y: Text Y position
            text_size: Text dimensions (width, height)
        """
        try:
            # Create particles around text area
            particle_count = 20 if self.visual_quality == VisualQuality.ULTRA else 10

            for i in range(particle_count):
                # Random position around text
                margin = 20
                x = text_x + np.random.randint(-margin, text_size[0] + margin)
                y = text_y + np.random.randint(-margin, text_size[1] + margin)

                # Particle properties
                size = np.random.randint(1, 4)
                alpha = int(np.random.uniform(0.1, 0.4) * 255)

                # Color variation
                base_color = (255, 215, 0)  # Gold
                color_variation = np.random.randint(-30, 30)
                color = (
                    max(0, min(255, base_color[0] + color_variation)),
                    max(0, min(255, base_color[1] + color_variation)),
                    max(0, min(255, base_color[2] + color_variation)),
                    alpha
                )

                # Draw particle
                particle_surf = pygame.Surface((size * 2, size * 2), pygame.SRCALPHA)
                pygame.draw.circle(particle_surf, color, (size, size), size)
                surface.blit(particle_surf, (x - size, y - size))

        except Exception as e:
            logger.debug(f"Failed to add particle trail: {e}")

    def set_text(self, text: str) -> None:
        """Set advertising text.

        Args:
            text: New advertising text
        """
        try:
            self.settings.text = text
            # Clear font cache to ensure proper sizing
            self.font_cache.clear()
            logger.debug(f"Advertising text updated: {text}")

        except Exception as e:
            logger.error(f"Failed to set advertising text: {e}")

    def set_animation_mode(self, mode: AnimationMode) -> None:
        """Set animation mode.

        Args:
            mode: New animation mode
        """
        try:
            self.settings.animation_mode = mode
            logger.debug(f"Animation mode updated: {mode.value}")

        except Exception as e:
            logger.error(f"Failed to set animation mode: {e}")

    def set_visual_quality(self, quality: VisualQuality) -> None:
        """Manually set visual quality.

        Args:
            quality: New visual quality level
        """
        try:
            self.visual_quality = quality
            self.settings.auto_quality = False  # Disable auto adjustment

            # Update effects based on quality
            if quality in [VisualQuality.ULTRA, VisualQuality.HIGH]:
                self.settings.glow_enabled = True
                self.settings.particles_enabled = True
            elif quality == VisualQuality.MEDIUM:
                self.settings.glow_enabled = True
                self.settings.particles_enabled = False
            else:  # LOW
                self.settings.glow_enabled = False
                self.settings.particles_enabled = False

            logger.info(f"Visual quality manually set to: {quality.value}")

        except Exception as e:
            logger.error(f"Failed to set visual quality: {e}")

    def get_performance_info(self) -> Dict[str, Any]:
        """Get performance information.

        Returns:
            Dictionary with performance metrics
        """
        try:
            info = {
                'gpu_available': self.gpu_available,
                'visual_quality': self.visual_quality.value,
                'animation_mode': self.settings.animation_mode.value,
                'glow_enabled': self.settings.glow_enabled,
                'particles_enabled': self.settings.particles_enabled,
                'frame_count': self.frame_count,
                'cache_sizes': {
                    'surfaces': len(self.surface_cache),
                    'fonts': len(self.font_cache),
                    'gradients': len(self.gradient_cache),
                    'glows': len(self.glow_cache)
                }
            }

            if self.performance_monitor:
                perf_settings = self.performance_monitor.get_hardware_optimized_settings()
                info.update({
                    'current_fps': perf_settings.get('current_fps', 0),
                    'target_fps': perf_settings.get('target_fps', 60),
                    'memory_usage_mb': perf_settings.get('memory_usage_mb', 0),
                    'optimization_level': perf_settings.get('optimization_level', 'unknown')
                })

            return info

        except Exception as e:
            logger.error(f"Failed to get performance info: {e}")
            return {'error': str(e)}

    def cleanup(self) -> None:
        """Clean up resources."""
        try:
            # Clear caches
            self.surface_cache.clear()
            self.font_cache.clear()
            self.gradient_cache.clear()
            self.glow_cache.clear()

            # Clear particles
            self.particles.clear()

            # Stop performance monitoring
            if self.performance_monitor:
                self.performance_monitor.stop()

            logger.info("Modern advertising component cleaned up")

        except Exception as e:
            logger.error(f"Failed to cleanup advertising component: {e}")

    def __del__(self):
        """Destructor to ensure cleanup."""
        try:
            self.cleanup()
        except:
            pass  # Ignore errors during destruction
