#ifndef __NUITKA_CALLS_H__
#define __NUITKA_CALLS_H__

extern PyObject *CALL_FUNCTION_WITH_ARGS11(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS12(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS14(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS15(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS16(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS17(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS22(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS23(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS24(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS27(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS28(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS31(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS41(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS46(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS63(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS88(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS11(PyThreadState *tstate, PyObject *called, PyObject *pos_args) ;
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS12(PyThreadState *tstate, PyObject *called, PyObject *pos_args) ;
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS26(PyThreadState *tstate, PyObject *called, PyObject *pos_args) ;
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS29(PyThreadState *tstate, PyObject *called, PyObject *pos_args) ;
#endif
