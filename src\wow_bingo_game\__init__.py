"""
WOW Bingo Game - Modern Python Application
==========================================

A comprehensive bingo game application with advanced features including:
- Modern Flet-based UI with responsive design
- Animated splash screen with background music
- Multi-language support (English, Amharic)
- Real-time database synchronization
- Payment and voucher system
- Statistics tracking and reporting
- Audio effects and voice announcements
- External display support
- Developer tools and cheat modes

This package provides a complete gaming experience with professional-grade
features suitable for commercial deployment.

Author: WOW Games Team
Version: 2.0.0
License: MIT
"""

__version__ = "2.0.0"
__author__ = "WOW Games Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

# Core imports for easy access
from .core.game_engine import BingoGameEngine
from .core.config import GameConfig
from .ui.app import WOWBingoApp
from .utils.logger import get_logger

# Version info tuple for programmatic access
VERSION_INFO = (2, 0, 0)

# Package metadata
__all__ = [
    "__version__",
    "__author__", 
    "__email__",
    "__license__",
    "VERSION_INFO",
    "BingoGameEngine",
    "GameConfig", 
    "WOWBingoApp",
    "get_logger",
]

# Initialize package-level logger
logger = get_logger(__name__)

def get_version() -> str:
    """Get the current version string."""
    return __version__

def get_version_info() -> tuple:
    """Get version info as a tuple."""
    return VERSION_INFO

# Package initialization
logger.info(f"WOW Bingo Game v{__version__} initialized")
