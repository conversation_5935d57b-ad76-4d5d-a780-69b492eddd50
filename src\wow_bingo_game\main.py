#!/usr/bin/env python3
"""
WOW Bingo Game - Main Entry Point
=================================

This is the main entry point for the WOW Bingo Game application.
It initializes the application, shows the splash screen, and launches the main game.

Features:
- Animated splash screen with background music
- Modern Flet-based UI
- Comprehensive error handling
- Development mode support
- Performance monitoring
- Cross-platform compatibility

Usage:
    python -m wow_bingo_game.main
    or
    wow-bingo (if installed as package)
"""

import sys
import os
import asyncio
import traceback
from pathlib import Path
from typing import Optional

# Add the project root to Python path for development
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# Core imports
import flet as ft
from loguru import logger

# Application imports
from .core.config import GameConfig
from .core.exceptions import WOWBingoError
from .ui.splash_screen import SplashScreen
from .ui.app import WOWBingoApp
from .utils.logger import setup_logging
from .utils.system_info import SystemInfo
from .utils.performance import PerformanceMonitor

# Version and metadata
from . import __version__, __author__


class WOWBingoLauncher:
    """Main application launcher with splash screen and error handling."""

    def __init__(self, dev_mode: bool = False):
        """Initialize the launcher.

        Args:
            dev_mode: Enable development mode with additional debugging
        """
        self.dev_mode = dev_mode
        self.config = GameConfig()
        self.performance_monitor = PerformanceMonitor()
        self.splash_screen: Optional[SplashScreen] = None
        self.main_app: Optional[WOWBingoApp] = None

        # Setup logging
        setup_logging(
            level="DEBUG" if dev_mode else "INFO",
            log_file=self.config.paths.logs_dir / "wow_bingo.log"
        )

        logger.info(f"WOW Bingo Game v{__version__} starting...")
        logger.info(f"Development mode: {dev_mode}")

    async def initialize_system(self) -> bool:
        """Initialize system components and check requirements.

        Returns:
            True if initialization successful, False otherwise
        """
        try:
            logger.info("Initializing system components...")

            # Initialize hardware acceleration detection first
            from .utils.hardware_acceleration import HardwareAccelerationManager
            self.hw_accel_manager = HardwareAccelerationManager()
            await self.hw_accel_manager.initialize()

            # Apply hardware-specific optimizations
            optimization_profile = self.hw_accel_manager.get_optimization_profile()
            logger.info(f"Hardware optimization profile: {optimization_profile['profile_name']}")

            # Check system requirements
            from .utils.system_info import SystemInfo
            system_info = SystemInfo()
            if not system_info.check_requirements():
                logger.error("System requirements not met")
                return False

            # Initialize performance monitoring with hardware-aware settings
            self.performance_monitor.start()
            self.performance_monitor.apply_hardware_profile(optimization_profile)

            # Create necessary directories
            self.config.paths.create_directories()

            # Initialize audio system
            from .audio.manager import AudioManager
            audio_manager = AudioManager()
            if not audio_manager.initialize():
                logger.warning("Audio system initialization failed")

            # Initialize database
            from .data.database import DatabaseManager
            db_manager = DatabaseManager(self.config.database)
            if not await db_manager.initialize():
                logger.error("Database initialization failed")
                return False

            logger.info("System initialization completed successfully")
            return True

        except Exception as e:
            logger.error(f"System initialization failed: {e}")
            logger.debug(traceback.format_exc())
            return False

    async def show_splash_screen(self, page: ft.Page) -> bool:
        """Show the animated splash screen.

        Args:
            page: Flet page object

        Returns:
            True if splash completed successfully, False if skipped/error
        """
        try:
            logger.info("Showing splash screen...")

            # Create and show splash screen
            self.splash_screen = SplashScreen(
                page=page,
                config=self.config,
                dev_mode=self.dev_mode
            )

            # Show splash screen and wait for completion
            success = await self.splash_screen.show()

            if success:
                logger.info("Splash screen completed successfully")
            else:
                logger.warning("Splash screen was skipped or failed")

            return success

        except Exception as e:
            logger.error(f"Splash screen error: {e}")
            logger.debug(traceback.format_exc())
            return False

    async def launch_main_app(self, page: ft.Page) -> None:
        """Launch the main application.

        Args:
            page: Flet page object
        """
        try:
            logger.info("Launching main application...")

            # Create main application
            self.main_app = WOWBingoApp(
                page=page,
                config=self.config,
                dev_mode=self.dev_mode
            )

            # Initialize and show main app
            await self.main_app.initialize()
            await self.main_app.show()

            logger.info("Main application launched successfully")

        except Exception as e:
            logger.error(f"Main application launch failed: {e}")
            logger.debug(traceback.format_exc())
            raise WOWBingoError(f"Failed to launch main application: {e}")

    async def cleanup(self) -> None:
        """Cleanup resources before exit."""
        try:
            logger.info("Cleaning up resources...")

            # Stop performance monitoring
            if self.performance_monitor:
                self.performance_monitor.stop()

            # Cleanup main app
            if self.main_app:
                await self.main_app.cleanup()

            # Cleanup splash screen
            if self.splash_screen:
                await self.splash_screen.cleanup()

            logger.info("Cleanup completed")

        except Exception as e:
            logger.error(f"Cleanup error: {e}")

    async def run(self, page: ft.Page) -> None:
        """Main application run method.

        Args:
            page: Flet page object
        """
        try:
            # Set page properties
            page.title = f"WOW Bingo Game v{__version__}"
            page.window_width = 1280
            page.window_height = 720
            page.window_min_width = 1024
            page.window_min_height = 768
            page.theme_mode = ft.ThemeMode.DARK
            page.window_center()

            # Initialize system
            if not await self.initialize_system():
                raise WOWBingoError("System initialization failed")

            # Show splash screen
            await self.show_splash_screen(page)

            # Launch main application
            await self.launch_main_app(page)

        except Exception as e:
            logger.error(f"Application run failed: {e}")
            logger.debug(traceback.format_exc())

            # Show error dialog
            await self._show_error_dialog(page, str(e))

        finally:
            await self.cleanup()

    async def _show_error_dialog(self, page: ft.Page, error_message: str) -> None:
        """Show error dialog to user.

        Args:
            page: Flet page object
            error_message: Error message to display
        """
        try:
            dialog = ft.AlertDialog(
                title=ft.Text("Application Error"),
                content=ft.Text(f"An error occurred:\n\n{error_message}"),
                actions=[
                    ft.TextButton("OK", on_click=lambda _: page.window_close())
                ]
            )
            page.dialog = dialog
            dialog.open = True
            await page.update_async()

        except Exception as e:
            logger.error(f"Failed to show error dialog: {e}")


def main(dev_mode: bool = False) -> None:
    """Main entry point for the application.

    Args:
        dev_mode: Enable development mode
    """
    try:
        # Create launcher
        launcher = WOWBingoLauncher(dev_mode=dev_mode)

        # Run the application
        ft.app(
            target=launcher.run,
            name="WOW Bingo Game",
            assets_dir="assets"
        )

    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        logger.debug(traceback.format_exc())
        sys.exit(1)


def main_dev() -> None:
    """Entry point for development mode."""
    main(dev_mode=True)


if __name__ == "__main__":
    # Check for development mode flag
    dev_mode = "--dev" in sys.argv or "-d" in sys.argv
    main(dev_mode=dev_mode)
