"""
WOW Bingo Game - Hardware Acceleration Detection and Optimization
================================================================

Advanced hardware acceleration detection with QSV (Quick Sync Video) GPU
priority and intelligent fallback to CPU optimization.

Features:
- Intel QSV (Quick Sync Video) detection and optimization
- NVIDIA NVENC detection
- AMD VCE detection
- CPU-based optimization fallback
- Automatic performance profiling
- Dynamic optimization adjustment
- Hardware-specific rendering optimizations
"""

import asyncio
import os
import platform
import subprocess
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from loguru import logger

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    import wmi
    WMI_AVAILABLE = True
except ImportError:
    WMI_AVAILABLE = False


class AccelerationType(Enum):
    """Hardware acceleration types in priority order."""
    BASIC_GPU = "Basic GPU Acceleration"
    QSV = "Intel Quick Sync Video"
    NVENC = "NVIDIA NVENC"
    VCE = "AMD VCE"
    CPU_OPTIMIZED = "CPU Optimized"
    CPU_BASIC = "CPU Basic"


@dataclass
class HardwareInfo:
    """Hardware information structure."""
    cpu_brand: str = "Unknown"
    cpu_cores: int = 1
    cpu_threads: int = 1
    cpu_base_freq: float = 0.0
    cpu_max_freq: float = 0.0
    memory_total_gb: float = 0.0
    gpu_devices: List[Dict[str, Any]] = None
    intel_gpu_present: bool = False
    nvidia_gpu_present: bool = False
    amd_gpu_present: bool = False
    basic_gpu_available: bool = False
    gpu_memory_mb: int = 0
    gpu_driver_version: str = "Unknown"
    qsv_available: bool = False
    nvenc_available: bool = False
    vce_available: bool = False

    def __post_init__(self):
        if self.gpu_devices is None:
            self.gpu_devices = []


@dataclass
class OptimizationProfile:
    """Hardware optimization profile."""
    profile_name: str
    acceleration_type: AccelerationType
    render_quality: str  # "ultra", "high", "medium", "low"
    animation_quality: str  # "ultra", "high", "medium", "low"
    texture_quality: str  # "ultra", "high", "medium", "low"
    vsync_enabled: bool
    max_fps: int
    memory_optimization: str  # "aggressive", "balanced", "conservative"
    cpu_threads: int
    gpu_acceleration: bool
    hardware_decoding: bool
    batch_rendering: bool
    cache_size_mb: int
    performance_monitoring: bool


class HardwareAccelerationManager:
    """Manages hardware acceleration detection and optimization."""

    def __init__(self):
        """Initialize hardware acceleration manager."""
        self.hardware_info = HardwareInfo()
        self.optimization_profile: Optional[OptimizationProfile] = None
        self.initialized = False
        self.detection_cache = {}

        logger.info("Hardware acceleration manager created")

    async def initialize(self) -> bool:
        """Initialize hardware detection and optimization.

        Returns:
            True if initialization successful
        """
        try:
            logger.info("Detecting hardware acceleration capabilities...")

            # Detect hardware information
            await self._detect_hardware_info()

            # Detect basic GPU acceleration first (highest priority)
            await self._detect_basic_gpu_acceleration()

            # Then detect advanced acceleration capabilities
            await self._detect_qsv_acceleration()
            await self._detect_nvenc_acceleration()
            await self._detect_vce_acceleration()

            # Create optimization profile
            self.optimization_profile = self._create_optimization_profile()

            # Log detection results
            self._log_hardware_summary()

            self.initialized = True
            logger.info("Hardware acceleration initialization completed")
            return True

        except Exception as e:
            logger.error(f"Hardware acceleration initialization failed: {e}")
            # Create fallback CPU profile
            self.optimization_profile = self._create_cpu_fallback_profile()
            return False

    async def _detect_hardware_info(self) -> None:
        """Detect basic hardware information."""
        try:
            # CPU information
            self.hardware_info.cpu_brand = platform.processor() or "Unknown"
            self.hardware_info.cpu_cores = os.cpu_count() or 1

            if PSUTIL_AVAILABLE:
                # Enhanced CPU info with psutil
                self.hardware_info.cpu_threads = psutil.cpu_count(logical=True) or 1

                # CPU frequency information
                cpu_freq = psutil.cpu_freq()
                if cpu_freq:
                    self.hardware_info.cpu_base_freq = cpu_freq.current or 0.0
                    self.hardware_info.cpu_max_freq = cpu_freq.max or 0.0

                # Memory information
                memory = psutil.virtual_memory()
                self.hardware_info.memory_total_gb = memory.total / (1024**3)
            else:
                self.hardware_info.cpu_threads = self.hardware_info.cpu_cores

            # GPU detection
            await self._detect_gpu_devices()

            logger.info(f"Hardware detected: {self.hardware_info.cpu_brand}, "
                       f"{self.hardware_info.cpu_cores}C/{self.hardware_info.cpu_threads}T, "
                       f"{self.hardware_info.memory_total_gb:.1f}GB RAM")

        except Exception as e:
            logger.error(f"Hardware detection failed: {e}")

    async def _detect_gpu_devices(self) -> None:
        """Detect GPU devices and capabilities."""
        try:
            gpu_devices = []

            # Windows GPU detection
            if platform.system() == "Windows":
                gpu_devices = await self._detect_windows_gpus()
            # Linux GPU detection
            elif platform.system() == "Linux":
                gpu_devices = await self._detect_linux_gpus()
            # macOS GPU detection
            elif platform.system() == "Darwin":
                gpu_devices = await self._detect_macos_gpus()

            self.hardware_info.gpu_devices = gpu_devices

            # Analyze GPU vendors and capabilities
            for gpu in gpu_devices:
                gpu_name = gpu.get("name", "").lower()
                gpu_memory = gpu.get("memory_mb", 0)

                # Update GPU memory info (use highest memory GPU)
                if gpu_memory > self.hardware_info.gpu_memory_mb:
                    self.hardware_info.gpu_memory_mb = gpu_memory
                    self.hardware_info.gpu_driver_version = gpu.get("driver_version", "Unknown")

                # Detect GPU vendors
                if "intel" in gpu_name:
                    self.hardware_info.intel_gpu_present = True
                elif "nvidia" in gpu_name or "geforce" in gpu_name or "quadro" in gpu_name:
                    self.hardware_info.nvidia_gpu_present = True
                elif "amd" in gpu_name or "radeon" in gpu_name:
                    self.hardware_info.amd_gpu_present = True

            logger.info(f"GPU devices detected: {len(gpu_devices)}")
            for gpu in gpu_devices:
                logger.info(f"  - {gpu.get('name', 'Unknown')}")

        except Exception as e:
            logger.error(f"GPU detection failed: {e}")

    async def _detect_windows_gpus(self) -> List[Dict[str, Any]]:
        """Detect GPU devices on Windows."""
        gpu_devices = []

        try:
            # Try WMI first (most reliable on Windows)
            if WMI_AVAILABLE:
                gpu_devices = await self._detect_windows_gpus_wmi()

            # Fallback to wmic command
            if not gpu_devices:
                gpu_devices = await self._detect_windows_gpus_wmic()

        except Exception as e:
            logger.debug(f"Windows GPU detection error: {e}")

        return gpu_devices

    async def _detect_windows_gpus_wmi(self) -> List[Dict[str, Any]]:
        """Detect Windows GPUs using WMI."""
        gpu_devices = []

        try:
            c = wmi.WMI()
            for gpu in c.Win32_VideoController():
                if gpu.Name:
                    gpu_info = {
                        "name": gpu.Name,
                        "driver_version": gpu.DriverVersion or "Unknown",
                        "memory_mb": int(gpu.AdapterRAM / (1024*1024)) if gpu.AdapterRAM else 0,
                        "vendor": self._extract_gpu_vendor(gpu.Name),
                        "pci_device_id": gpu.PNPDeviceID or "Unknown"
                    }
                    gpu_devices.append(gpu_info)

        except Exception as e:
            logger.debug(f"WMI GPU detection failed: {e}")

        return gpu_devices

    async def _detect_windows_gpus_wmic(self) -> List[Dict[str, Any]]:
        """Detect Windows GPUs using wmic command."""
        gpu_devices = []

        try:
            result = await asyncio.create_subprocess_exec(
                "wmic", "path", "win32_VideoController",
                "get", "name,driverversion,adapterram", "/format:csv",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                lines = stdout.decode('utf-8', errors='ignore').split('\n')
                for line in lines[1:]:  # Skip header
                    if line.strip() and ',' in line:
                        parts = line.split(',')
                        if len(parts) >= 4 and parts[2].strip():
                            gpu_info = {
                                "name": parts[2].strip(),
                                "driver_version": parts[1].strip() or "Unknown",
                                "memory_mb": int(parts[0]) // (1024*1024) if parts[0].strip().isdigit() else 0,
                                "vendor": self._extract_gpu_vendor(parts[2].strip()),
                                "pci_device_id": "Unknown"
                            }
                            gpu_devices.append(gpu_info)

        except Exception as e:
            logger.debug(f"WMIC GPU detection failed: {e}")

        return gpu_devices

    async def _detect_linux_gpus(self) -> List[Dict[str, Any]]:
        """Detect GPU devices on Linux."""
        gpu_devices = []

        try:
            # Use lspci to detect GPUs
            result = await asyncio.create_subprocess_exec(
                "lspci", "-nn",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                lines = stdout.decode('utf-8', errors='ignore').split('\n')
                for line in lines:
                    if any(keyword in line.lower() for keyword in ['vga', 'display', '3d']):
                        gpu_name = line.split(': ', 1)[1] if ': ' in line else line
                        gpu_info = {
                            "name": gpu_name,
                            "driver_version": "Unknown",
                            "memory_mb": 0,
                            "vendor": self._extract_gpu_vendor(gpu_name),
                            "pci_device_id": "Unknown"
                        }
                        gpu_devices.append(gpu_info)

        except Exception as e:
            logger.debug(f"Linux GPU detection failed: {e}")

        return gpu_devices

    async def _detect_macos_gpus(self) -> List[Dict[str, Any]]:
        """Detect GPU devices on macOS."""
        gpu_devices = []

        try:
            # Use system_profiler to detect GPUs
            result = await asyncio.create_subprocess_exec(
                "system_profiler", "SPDisplaysDataType",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                # Basic parsing - could be enhanced
                if "Chipset Model:" in stdout.decode('utf-8', errors='ignore'):
                    gpu_info = {
                        "name": "macOS GPU (detected)",
                        "driver_version": "Unknown",
                        "memory_mb": 0,
                        "vendor": "Apple/Intel",
                        "pci_device_id": "Unknown"
                    }
                    gpu_devices.append(gpu_info)

        except Exception as e:
            logger.debug(f"macOS GPU detection failed: {e}")

        return gpu_devices

    def _extract_gpu_vendor(self, gpu_name: str) -> str:
        """Extract GPU vendor from name."""
        gpu_name_lower = gpu_name.lower()
        if "intel" in gpu_name_lower:
            return "Intel"
        elif any(keyword in gpu_name_lower for keyword in ["nvidia", "geforce", "quadro", "tesla"]):
            return "NVIDIA"
        elif any(keyword in gpu_name_lower for keyword in ["amd", "radeon", "ati"]):
            return "AMD"
        else:
            return "Unknown"

    async def _detect_basic_gpu_acceleration(self) -> None:
        """Detect basic GPU acceleration capabilities (highest priority)."""
        try:
            logger.info("Detecting basic GPU acceleration...")

            # Check if any GPU is present
            if not self.hardware_info.gpu_devices:
                logger.info("No GPU devices detected, basic GPU acceleration not available")
                return

            # Check for basic GPU acceleration capability
            gpu_acceleration_available = False
            gpu_acceleration_reason = "No suitable GPU found"

            for gpu in self.hardware_info.gpu_devices:
                gpu_name = gpu.get("name", "").lower()
                gpu_memory = gpu.get("memory_mb", 0)
                driver_version = gpu.get("driver_version", "Unknown")

                # Check for basic GPU acceleration requirements
                is_suitable_gpu = False
                reason = ""

                # Intel GPU check
                if "intel" in gpu_name:
                    # Intel GPUs generally support basic acceleration
                    if "hd graphics" in gpu_name or "iris" in gpu_name or "xe" in gpu_name or "uhd" in gpu_name:
                        is_suitable_gpu = True
                        reason = f"Intel integrated graphics detected: {gpu['name']}"
                    elif "intel" in gpu_name:
                        is_suitable_gpu = True
                        reason = f"Intel GPU detected: {gpu['name']}"

                # NVIDIA GPU check
                elif "nvidia" in gpu_name or "geforce" in gpu_name or "quadro" in gpu_name or "gtx" in gpu_name or "rtx" in gpu_name:
                    # Most NVIDIA GPUs support basic acceleration
                    is_suitable_gpu = True
                    reason = f"NVIDIA GPU detected: {gpu['name']}"

                # AMD GPU check
                elif "amd" in gpu_name or "radeon" in gpu_name or "rx " in gpu_name:
                    # Most AMD GPUs support basic acceleration
                    is_suitable_gpu = True
                    reason = f"AMD GPU detected: {gpu['name']}"

                # Generic GPU check (fallback)
                elif any(keyword in gpu_name for keyword in ["graphics", "display", "video", "gpu"]):
                    # Generic GPU that might support basic acceleration
                    if gpu_memory >= 128:  # At least 128MB VRAM
                        is_suitable_gpu = True
                        reason = f"Generic GPU with sufficient memory: {gpu['name']} ({gpu_memory}MB)"
                    else:
                        reason = f"Generic GPU with insufficient memory: {gpu['name']} ({gpu_memory}MB)"

                if is_suitable_gpu:
                    # Verify basic GPU functionality
                    if await self._verify_basic_gpu_functionality(gpu):
                        gpu_acceleration_available = True
                        gpu_acceleration_reason = reason
                        logger.info(f"✅ Basic GPU acceleration available: {reason}")
                        break
                    else:
                        logger.warning(f"⚠️ GPU detected but functionality test failed: {gpu['name']}")
                        reason = f"GPU functionality test failed: {gpu['name']}"
                else:
                    logger.debug(f"GPU not suitable for acceleration: {gpu['name']} - {reason}")

            self.hardware_info.basic_gpu_available = gpu_acceleration_available

            if gpu_acceleration_available:
                logger.info(f"✅ Basic GPU acceleration enabled: {gpu_acceleration_reason}")
            else:
                logger.info(f"❌ Basic GPU acceleration not available: {gpu_acceleration_reason}")

        except Exception as e:
            logger.error(f"Basic GPU detection failed: {e}")
            self.hardware_info.basic_gpu_available = False

    async def _verify_basic_gpu_functionality(self, gpu: Dict[str, Any]) -> bool:
        """Verify basic GPU functionality for acceleration.

        Args:
            gpu: GPU information dictionary

        Returns:
            True if GPU supports basic acceleration
        """
        try:
            gpu_name = gpu.get("name", "").lower()
            driver_version = gpu.get("driver_version", "Unknown")

            # Basic checks for GPU functionality
            functionality_checks = []

            # Check 1: Driver version check
            if driver_version != "Unknown" and driver_version.strip():
                functionality_checks.append("Driver installed")
            else:
                logger.debug(f"No driver version found for {gpu['name']}")

            # Check 2: Memory check
            gpu_memory = gpu.get("memory_mb", 0)
            if gpu_memory >= 64:  # At least 64MB for basic operations
                functionality_checks.append(f"Sufficient memory ({gpu_memory}MB)")
            else:
                logger.debug(f"Insufficient GPU memory: {gpu_memory}MB")

            # Check 3: Platform-specific verification
            platform_check = await self._verify_gpu_platform_support(gpu)
            if platform_check:
                functionality_checks.append("Platform support verified")

            # Check 4: Exclude known problematic GPUs
            problematic_keywords = ["microsoft basic", "standard vga", "generic pnp"]
            if not any(keyword in gpu_name for keyword in problematic_keywords):
                functionality_checks.append("Not a basic/generic driver")
            else:
                logger.debug(f"Problematic GPU detected: {gpu['name']}")
                return False

            # Require at least 2 successful checks for basic GPU acceleration
            success_count = len(functionality_checks)
            logger.debug(f"GPU functionality checks for {gpu['name']}: {functionality_checks}")

            if success_count >= 2:
                logger.info(f"GPU functionality verified: {gpu['name']} ({success_count} checks passed)")
                return True
            else:
                logger.debug(f"GPU functionality insufficient: {gpu['name']} ({success_count} checks passed)")
                return False

        except Exception as e:
            logger.debug(f"GPU functionality verification failed for {gpu.get('name', 'Unknown')}: {e}")
            return False

    async def _verify_gpu_platform_support(self, gpu: Dict[str, Any]) -> bool:
        """Verify platform-specific GPU support.

        Args:
            gpu: GPU information dictionary

        Returns:
            True if platform supports the GPU
        """
        try:
            current_platform = platform.system()
            gpu_name = gpu.get("name", "").lower()

            if current_platform == "Windows":
                # Windows: Check for DirectX support (most GPUs support this)
                return await self._check_directx_support(gpu)
            elif current_platform == "Linux":
                # Linux: Check for OpenGL support
                return await self._check_opengl_support(gpu)
            elif current_platform == "Darwin":  # macOS
                # macOS: Check for Metal support
                return await self._check_metal_support(gpu)
            else:
                # Unknown platform, assume basic support
                return True

        except Exception as e:
            logger.debug(f"Platform support verification failed: {e}")
            return True  # Assume support if verification fails

    async def _check_directx_support(self, gpu: Dict[str, Any]) -> bool:
        """Check DirectX support on Windows."""
        try:
            # Try to run dxdiag to check DirectX support
            result = await asyncio.create_subprocess_exec(
                "dxdiag", "/t", "nul",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await asyncio.wait_for(result.communicate(), timeout=5.0)
            return result.returncode == 0
        except:
            # If dxdiag fails, assume DirectX is available (most Windows systems have it)
            return True

    async def _check_opengl_support(self, gpu: Dict[str, Any]) -> bool:
        """Check OpenGL support on Linux."""
        try:
            # Check for OpenGL libraries
            opengl_paths = [
                "/usr/lib/x86_64-linux-gnu/libGL.so",
                "/usr/lib/libGL.so",
                "/usr/lib64/libGL.so"
            ]
            for path in opengl_paths:
                if os.path.exists(path):
                    return True
            return False
        except:
            return True  # Assume support if check fails

    async def _check_metal_support(self, gpu: Dict[str, Any]) -> bool:
        """Check Metal support on macOS."""
        try:
            # Most modern macOS systems support Metal
            gpu_name = gpu.get("name", "").lower()
            if "intel" in gpu_name or "amd" in gpu_name or "apple" in gpu_name:
                return True
            return False
        except:
            return True  # Assume support if check fails

    async def _detect_qsv_acceleration(self) -> None:
        """Detect Intel Quick Sync Video (QSV) acceleration."""
        try:
            logger.info("Detecting Intel QSV acceleration...")

            # Check if Intel GPU is present
            if not self.hardware_info.intel_gpu_present:
                logger.info("No Intel GPU detected, QSV not available")
                return

            # Check CPU for Intel Quick Sync support
            cpu_brand = self.hardware_info.cpu_brand.lower()
            qsv_supported_families = [
                "sandy bridge", "ivy bridge", "haswell", "broadwell",
                "skylake", "kaby lake", "coffee lake", "whiskey lake",
                "comet lake", "ice lake", "tiger lake", "rocket lake",
                "alder lake", "raptor lake"
            ]

            # Check if CPU supports QSV
            cpu_supports_qsv = any(family in cpu_brand for family in qsv_supported_families)

            # Additional check for Intel processors
            if "intel" in cpu_brand:
                cpu_supports_qsv = True

            if cpu_supports_qsv:
                # Try to verify QSV availability through system calls
                qsv_available = await self._verify_qsv_availability()
                self.hardware_info.qsv_available = qsv_available

                if qsv_available:
                    logger.info("✅ Intel QSV acceleration detected and available")
                else:
                    logger.info("⚠️ Intel QSV may be supported but not currently available")
            else:
                logger.info("CPU does not support Intel QSV")

        except Exception as e:
            logger.error(f"QSV detection failed: {e}")

    async def _verify_qsv_availability(self) -> bool:
        """Verify QSV availability through system checks."""
        try:
            # On Windows, check for Intel Media SDK or Intel Graphics drivers
            if platform.system() == "Windows":
                return await self._verify_qsv_windows()
            # On Linux, check for Intel VAAPI drivers
            elif platform.system() == "Linux":
                return await self._verify_qsv_linux()
            else:
                return False

        except Exception as e:
            logger.debug(f"QSV verification failed: {e}")
            return False

    async def _verify_qsv_windows(self) -> bool:
        """Verify QSV on Windows."""
        try:
            # Check for Intel Graphics drivers in registry or system
            # This is a simplified check - could be enhanced
            intel_driver_paths = [
                "C:\\Windows\\System32\\igdumdim64.dll",
                "C:\\Windows\\System32\\igdumdim32.dll",
                "C:\\Windows\\System32\\DriverStore\\FileRepository\\*intel*"
            ]

            for path in intel_driver_paths:
                if os.path.exists(path):
                    logger.debug(f"Intel driver found: {path}")
                    return True

            return False

        except Exception as e:
            logger.debug(f"Windows QSV verification failed: {e}")
            return False

    async def _verify_qsv_linux(self) -> bool:
        """Verify QSV on Linux."""
        try:
            # Check for VAAPI devices
            vaapi_devices = ["/dev/dri/renderD128", "/dev/dri/card0"]
            for device in vaapi_devices:
                if os.path.exists(device):
                    logger.debug(f"VAAPI device found: {device}")
                    return True

            return False

        except Exception as e:
            logger.debug(f"Linux QSV verification failed: {e}")
            return False

    async def _detect_nvenc_acceleration(self) -> None:
        """Detect NVIDIA NVENC acceleration."""
        try:
            logger.info("Detecting NVIDIA NVENC acceleration...")

            if not self.hardware_info.nvidia_gpu_present:
                logger.info("No NVIDIA GPU detected, NVENC not available")
                return

            # Check for NVENC support (most modern NVIDIA GPUs support it)
            nvenc_available = await self._verify_nvenc_availability()
            self.hardware_info.nvenc_available = nvenc_available

            if nvenc_available:
                logger.info("✅ NVIDIA NVENC acceleration detected and available")
            else:
                logger.info("⚠️ NVIDIA GPU present but NVENC not available")

        except Exception as e:
            logger.error(f"NVENC detection failed: {e}")

    async def _verify_nvenc_availability(self) -> bool:
        """Verify NVENC availability."""
        try:
            # Check for NVIDIA drivers
            if platform.system() == "Windows":
                nvidia_paths = [
                    "C:\\Windows\\System32\\nvencodeapi64.dll",
                    "C:\\Windows\\System32\\nvencodeapi.dll"
                ]
                for path in nvidia_paths:
                    if os.path.exists(path):
                        return True
            elif platform.system() == "Linux":
                # Check for NVIDIA drivers
                nvidia_paths = ["/usr/lib/x86_64-linux-gnu/libnvidia-encode.so"]
                for path in nvidia_paths:
                    if os.path.exists(path):
                        return True

            return False

        except Exception as e:
            logger.debug(f"NVENC verification failed: {e}")
            return False

    async def _detect_vce_acceleration(self) -> None:
        """Detect AMD VCE acceleration."""
        try:
            logger.info("Detecting AMD VCE acceleration...")

            if not self.hardware_info.amd_gpu_present:
                logger.info("No AMD GPU detected, VCE not available")
                return

            # Check for VCE support
            vce_available = await self._verify_vce_availability()
            self.hardware_info.vce_available = vce_available

            if vce_available:
                logger.info("✅ AMD VCE acceleration detected and available")
            else:
                logger.info("⚠️ AMD GPU present but VCE not available")

        except Exception as e:
            logger.error(f"VCE detection failed: {e}")

    async def _verify_vce_availability(self) -> bool:
        """Verify VCE availability."""
        try:
            # Basic check for AMD drivers
            if platform.system() == "Windows":
                # Check for AMD drivers
                amd_paths = ["C:\\Windows\\System32\\amfrt64.dll"]
                for path in amd_paths:
                    if os.path.exists(path):
                        return True
            elif platform.system() == "Linux":
                # Check for AMD drivers
                amd_paths = ["/usr/lib/x86_64-linux-gnu/libamfrt64.so"]
                for path in amd_paths:
                    if os.path.exists(path):
                        return True

            return False

        except Exception as e:
            logger.debug(f"VCE verification failed: {e}")
            return False

    def _create_optimization_profile(self) -> OptimizationProfile:
        """Create hardware-specific optimization profile."""
        try:
            # Determine best acceleration type in priority order
            # 1. Basic GPU acceleration (highest priority)
            if self.hardware_info.basic_gpu_available:
                # Check for advanced acceleration on top of basic GPU
                if self.hardware_info.qsv_available:
                    return self._create_qsv_profile()
                elif self.hardware_info.nvenc_available:
                    return self._create_nvenc_profile()
                elif self.hardware_info.vce_available:
                    return self._create_vce_profile()
                else:
                    return self._create_basic_gpu_profile()

            # 2. Advanced acceleration without basic GPU (fallback)
            elif self.hardware_info.qsv_available:
                return self._create_qsv_profile()
            elif self.hardware_info.nvenc_available:
                return self._create_nvenc_profile()
            elif self.hardware_info.vce_available:
                return self._create_vce_profile()

            # 3. CPU-only fallback
            else:
                return self._create_cpu_optimized_profile()

        except Exception as e:
            logger.error(f"Failed to create optimization profile: {e}")
            return self._create_cpu_fallback_profile()

    def _create_basic_gpu_profile(self) -> OptimizationProfile:
        """Create basic GPU acceleration profile."""
        # Determine GPU performance tier based on memory and vendor
        gpu_memory = self.hardware_info.gpu_memory_mb
        cpu_cores = self.hardware_info.cpu_cores
        memory_gb = self.hardware_info.memory_total_gb

        # High-end GPU (dedicated cards or high-memory integrated)
        if gpu_memory >= 2048 or self.hardware_info.nvidia_gpu_present:
            return OptimizationProfile(
                profile_name="High-End GPU Accelerated",
                acceleration_type=AccelerationType.BASIC_GPU,
                render_quality="high",
                animation_quality="high",
                texture_quality="high",
                vsync_enabled=True,
                max_fps=90,
                memory_optimization="conservative",
                cpu_threads=min(cpu_cores, 6),
                gpu_acceleration=True,
                hardware_decoding=True,
                batch_rendering=True,
                cache_size_mb=384,
                performance_monitoring=True
            )

        # Mid-range GPU (decent integrated or older dedicated)
        elif gpu_memory >= 512 or memory_gb >= 8:
            return OptimizationProfile(
                profile_name="Mid-Range GPU Accelerated",
                acceleration_type=AccelerationType.BASIC_GPU,
                render_quality="medium",
                animation_quality="medium",
                texture_quality="medium",
                vsync_enabled=True,
                max_fps=75,
                memory_optimization="balanced",
                cpu_threads=min(cpu_cores, 4),
                gpu_acceleration=True,
                hardware_decoding=True,
                batch_rendering=True,
                cache_size_mb=256,
                performance_monitoring=True
            )

        # Basic GPU (low-end integrated)
        else:
            return OptimizationProfile(
                profile_name="Basic GPU Accelerated",
                acceleration_type=AccelerationType.BASIC_GPU,
                render_quality="medium",
                animation_quality="low",
                texture_quality="low",
                vsync_enabled=False,
                max_fps=60,
                memory_optimization="balanced",
                cpu_threads=min(cpu_cores, 2),
                gpu_acceleration=True,
                hardware_decoding=False,
                batch_rendering=False,
                cache_size_mb=128,
                performance_monitoring=True
            )

    def _create_qsv_profile(self) -> OptimizationProfile:
        """Create Intel QSV optimized profile."""
        return OptimizationProfile(
            profile_name="Intel QSV Optimized",
            acceleration_type=AccelerationType.QSV,
            render_quality="ultra",
            animation_quality="ultra",
            texture_quality="high",
            vsync_enabled=True,
            max_fps=120,
            memory_optimization="balanced",
            cpu_threads=min(self.hardware_info.cpu_threads, 8),
            gpu_acceleration=True,
            hardware_decoding=True,
            batch_rendering=True,
            cache_size_mb=256,
            performance_monitoring=True
        )

    def _create_nvenc_profile(self) -> OptimizationProfile:
        """Create NVIDIA NVENC optimized profile."""
        return OptimizationProfile(
            profile_name="NVIDIA NVENC Optimized",
            acceleration_type=AccelerationType.NVENC,
            render_quality="ultra",
            animation_quality="ultra",
            texture_quality="ultra",
            vsync_enabled=True,
            max_fps=144,
            memory_optimization="conservative",
            cpu_threads=min(self.hardware_info.cpu_threads, 12),
            gpu_acceleration=True,
            hardware_decoding=True,
            batch_rendering=True,
            cache_size_mb=512,
            performance_monitoring=True
        )

    def _create_vce_profile(self) -> OptimizationProfile:
        """Create AMD VCE optimized profile."""
        return OptimizationProfile(
            profile_name="AMD VCE Optimized",
            acceleration_type=AccelerationType.VCE,
            render_quality="high",
            animation_quality="high",
            texture_quality="high",
            vsync_enabled=True,
            max_fps=120,
            memory_optimization="balanced",
            cpu_threads=min(self.hardware_info.cpu_threads, 10),
            gpu_acceleration=True,
            hardware_decoding=True,
            batch_rendering=True,
            cache_size_mb=384,
            performance_monitoring=True
        )

    def _create_cpu_optimized_profile(self) -> OptimizationProfile:
        """Create CPU-optimized profile for systems without GPU acceleration."""
        # Determine CPU performance tier
        cpu_cores = self.hardware_info.cpu_cores
        memory_gb = self.hardware_info.memory_total_gb

        if cpu_cores >= 8 and memory_gb >= 16:
            # High-end CPU
            return OptimizationProfile(
                profile_name="High-End CPU Optimized",
                acceleration_type=AccelerationType.CPU_OPTIMIZED,
                render_quality="high",
                animation_quality="high",
                texture_quality="medium",
                vsync_enabled=True,
                max_fps=60,
                memory_optimization="conservative",
                cpu_threads=min(cpu_cores, 6),
                gpu_acceleration=False,
                hardware_decoding=False,
                batch_rendering=True,
                cache_size_mb=256,
                performance_monitoring=True
            )
        elif cpu_cores >= 4 and memory_gb >= 8:
            # Mid-range CPU
            return OptimizationProfile(
                profile_name="Mid-Range CPU Optimized",
                acceleration_type=AccelerationType.CPU_OPTIMIZED,
                render_quality="medium",
                animation_quality="medium",
                texture_quality="medium",
                vsync_enabled=True,
                max_fps=60,
                memory_optimization="balanced",
                cpu_threads=min(cpu_cores, 4),
                gpu_acceleration=False,
                hardware_decoding=False,
                batch_rendering=True,
                cache_size_mb=128,
                performance_monitoring=True
            )
        else:
            # Low-end CPU
            return self._create_cpu_fallback_profile()

    def _create_cpu_fallback_profile(self) -> OptimizationProfile:
        """Create basic CPU fallback profile for older/limited hardware."""
        return OptimizationProfile(
            profile_name="CPU Basic (Fallback)",
            acceleration_type=AccelerationType.CPU_BASIC,
            render_quality="low",
            animation_quality="low",
            texture_quality="low",
            vsync_enabled=False,
            max_fps=30,
            memory_optimization="aggressive",
            cpu_threads=min(self.hardware_info.cpu_threads, 2),
            gpu_acceleration=False,
            hardware_decoding=False,
            batch_rendering=False,
            cache_size_mb=64,
            performance_monitoring=False
        )

    def _log_hardware_summary(self) -> None:
        """Log comprehensive hardware detection summary."""
        logger.info("=== Hardware Acceleration Summary ===")
        logger.info(f"CPU: {self.hardware_info.cpu_brand}")
        logger.info(f"Cores/Threads: {self.hardware_info.cpu_cores}/{self.hardware_info.cpu_threads}")
        logger.info(f"Memory: {self.hardware_info.memory_total_gb:.1f}GB")
        logger.info(f"GPU Devices: {len(self.hardware_info.gpu_devices)}")

        for gpu in self.hardware_info.gpu_devices:
            logger.info(f"  - {gpu['name']} ({gpu['vendor']})")

        logger.info("Acceleration Support:")
        logger.info(f"  Basic GPU: {'✅ Available' if self.hardware_info.basic_gpu_available else '❌ Not Available'}")
        if self.hardware_info.basic_gpu_available:
            logger.info(f"    GPU Memory: {self.hardware_info.gpu_memory_mb}MB")
            logger.info(f"    Driver Version: {self.hardware_info.gpu_driver_version}")
        logger.info(f"  Intel QSV: {'✅ Available' if self.hardware_info.qsv_available else '❌ Not Available'}")
        logger.info(f"  NVIDIA NVENC: {'✅ Available' if self.hardware_info.nvenc_available else '❌ Not Available'}")
        logger.info(f"  AMD VCE: {'✅ Available' if self.hardware_info.vce_available else '❌ Not Available'}")

        if self.optimization_profile:
            logger.info(f"Selected Profile: {self.optimization_profile.profile_name}")
            logger.info(f"Acceleration Type: {self.optimization_profile.acceleration_type.value}")
            logger.info(f"Render Quality: {self.optimization_profile.render_quality}")
            logger.info(f"Max FPS: {self.optimization_profile.max_fps}")

    def get_optimization_profile(self) -> Dict[str, Any]:
        """Get current optimization profile as dictionary.

        Returns:
            Dictionary with optimization settings
        """
        if not self.optimization_profile:
            return {}

        return {
            "profile_name": self.optimization_profile.profile_name,
            "acceleration_type": self.optimization_profile.acceleration_type.value,
            "render_quality": self.optimization_profile.render_quality,
            "animation_quality": self.optimization_profile.animation_quality,
            "texture_quality": self.optimization_profile.texture_quality,
            "vsync_enabled": self.optimization_profile.vsync_enabled,
            "max_fps": self.optimization_profile.max_fps,
            "memory_optimization": self.optimization_profile.memory_optimization,
            "cpu_threads": self.optimization_profile.cpu_threads,
            "gpu_acceleration": self.optimization_profile.gpu_acceleration,
            "hardware_decoding": self.optimization_profile.hardware_decoding,
            "batch_rendering": self.optimization_profile.batch_rendering,
            "cache_size_mb": self.optimization_profile.cache_size_mb,
            "performance_monitoring": self.optimization_profile.performance_monitoring,
            "hardware_info": {
                "qsv_available": self.hardware_info.qsv_available,
                "nvenc_available": self.hardware_info.nvenc_available,
                "vce_available": self.hardware_info.vce_available,
                "cpu_cores": self.hardware_info.cpu_cores,
                "memory_gb": self.hardware_info.memory_total_gb
            }
        }

    def get_hardware_info(self) -> Dict[str, Any]:
        """Get detailed hardware information.

        Returns:
            Dictionary with hardware details
        """
        return {
            "cpu_brand": self.hardware_info.cpu_brand,
            "cpu_cores": self.hardware_info.cpu_cores,
            "cpu_threads": self.hardware_info.cpu_threads,
            "memory_total_gb": self.hardware_info.memory_total_gb,
            "gpu_devices": self.hardware_info.gpu_devices,
            "acceleration_support": {
                "basic_gpu": self.hardware_info.basic_gpu_available,
                "qsv": self.hardware_info.qsv_available,
                "nvenc": self.hardware_info.nvenc_available,
                "vce": self.hardware_info.vce_available
            },
            "gpu_memory_mb": self.hardware_info.gpu_memory_mb,
            "gpu_driver_version": self.hardware_info.gpu_driver_version
        }
