2025-05-27 12:14:52.512 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 12:14:52.515 - Using OptimizedStats<PERSON>oader as secondary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 12:14:52.534 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 12:14:52.535 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-05-27 12:14:52.537 - Initializing StatsDataProvider
2025-05-27 12:14:52.550 - Loaded 8 items from cache
2025-05-27 12:14:52.553 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-05-27 12:14:52.556 - Starting background data loading thread
2025-05-27 12:14:52.564 - Background data loading started
2025-05-27 12:14:52.564 - StatsDataProvider initialization completed
2025-05-27 12:14:52.568 - Loading summary statistics
2025-05-27 12:14:52.570 - Attempting to load summary stats from GameStatsIntegration
2025-05-27 12:14:52.570 - Created singleton instance of StatsDataProvider on module import
2025-05-27 12:14:52.571 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 12:14:52.571 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-05-27 12:14:52.579 - Successfully loaded summary stats from GameStatsIntegration
2025-05-27 12:14:52.586 - get_stats_provider called, returning provider with initialized=True
2025-05-27 12:14:52.587 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 12:14:52.592 - Successfully loaded weekly stats: 7 days
2025-05-27 12:14:52.602 - Saved 10 items to cache
2025-05-27 12:14:52.603 - Background data loading completed
2025-05-27 13:37:03.489 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 13:37:03.492 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 13:37:03.526 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 13:37:03.529 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-05-27 13:37:03.529 - Initializing StatsDataProvider
2025-05-27 13:37:03.536 - Loaded 8 items from cache
2025-05-27 13:37:03.539 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-05-27 13:37:03.545 - Starting background data loading thread
2025-05-27 13:37:03.555 - Background data loading started
2025-05-27 13:37:03.555 - StatsDataProvider initialization completed
2025-05-27 13:37:03.556 - Loading summary statistics
2025-05-27 13:37:03.559 - Created singleton instance of StatsDataProvider on module import
2025-05-27 13:37:03.560 - Attempting to load summary stats from GameStatsIntegration
2025-05-27 13:37:03.561 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-05-27 13:37:03.562 - Data from GameStatsIntegration: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 13:37:03.563 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:37:03.572 - Successfully loaded summary stats from GameStatsIntegration
2025-05-27 13:37:03.577 - Successfully loaded summary stats: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 13:37:03.583 - Successfully loaded weekly stats: 7 days
2025-05-27 13:37:03.586 - Saved 10 items to cache
2025-05-27 13:37:03.590 - Background data loading completed
2025-05-27 13:38:11.742 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:38:11.769 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:38:11.776 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:38:11.778 - Forcing refresh of all stats data
2025-05-27 13:38:11.779 - Attempting to force refresh via GameStatsIntegration
2025-05-27 13:38:11.798 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:38:11.799 - Force refresh via GameStatsIntegration successful
2025-05-27 13:38:11.800 - Clearing cached data
2025-05-27 13:38:11.801 - Posted refresh_stats event to trigger UI update
2025-05-27 13:38:11.802 - Starting background data reload
2025-05-27 13:38:11.810 - Starting background data loading thread
2025-05-27 13:38:11.812 - Background data loading started
2025-05-27 13:38:11.833 - Loading summary statistics
2025-05-27 13:38:11.866 - Attempting to load summary stats from GameStatsIntegration
2025-05-27 13:38:11.917 - Data from GameStatsIntegration: {'total_earnings': 40.0, 'daily_earnings': 40.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-27 13:38:11.922 - Successfully loaded summary stats from GameStatsIntegration
2025-05-27 13:38:11.926 - Successfully loaded summary stats: {'total_earnings': 40.0, 'daily_earnings': 40.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-27 13:38:11.927 - Successfully loaded game history: 1 entries
2025-05-27 13:38:11.928 - Successfully loaded weekly stats: 7 days
2025-05-27 13:38:11.930 - Saved 3 items to cache
2025-05-27 13:38:11.931 - Background data loading completed
2025-05-27 13:41:18.513 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:42:18.155 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:42:18.180 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:42:18.187 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:42:18.189 - Forcing refresh of all stats data
2025-05-27 13:42:18.190 - Attempting to force refresh via GameStatsIntegration
2025-05-27 13:42:18.210 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:42:18.219 - Force refresh via GameStatsIntegration successful
2025-05-27 13:42:18.220 - Clearing cached data
2025-05-27 13:42:18.221 - Posted refresh_stats event to trigger UI update
2025-05-27 13:42:18.222 - Starting background data reload
2025-05-27 13:42:18.223 - Starting background data loading thread
2025-05-27 13:42:18.223 - Background data loading started
2025-05-27 13:42:18.229 - Loading summary statistics
2025-05-27 13:42:18.277 - Attempting to load summary stats from GameStatsIntegration
2025-05-27 13:42:18.288 - Data from GameStatsIntegration: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-27 13:42:18.310 - Successfully loaded summary stats from GameStatsIntegration
2025-05-27 13:42:18.313 - Successfully loaded summary stats: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-27 13:42:18.315 - Successfully loaded game history: 2 entries
2025-05-27 13:42:18.328 - Successfully loaded weekly stats: 7 days
2025-05-27 13:42:18.330 - Saved 3 items to cache
2025-05-27 13:42:18.333 - Background data loading completed
2025-05-27 13:55:04.784 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-27 13:55:04.786 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-27 13:55:05.439 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-27 13:55:05.606 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-05-27 13:55:05.615 - Initializing StatsDataProvider
2025-05-27 13:55:05.618 - Loaded 8 items from cache
2025-05-27 13:55:05.619 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-05-27 13:55:05.620 - Starting background data loading thread
2025-05-27 13:55:05.624 - Background data loading started
2025-05-27 13:55:05.624 - StatsDataProvider initialization completed
2025-05-27 13:55:05.627 - Loading summary statistics
2025-05-27 13:55:05.627 - Created singleton instance of StatsDataProvider on module import
2025-05-27 13:55:05.628 - Attempting to load summary stats from GameStatsIntegration
2025-05-27 13:55:05.628 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-05-27 13:55:05.630 - Data from GameStatsIntegration: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-27 13:55:05.631 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:55:05.635 - Successfully loaded summary stats from GameStatsIntegration
2025-05-27 13:55:05.638 - Successfully loaded summary stats: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-27 13:55:05.643 - Successfully loaded game history: 2 entries
2025-05-27 13:55:05.647 - Successfully loaded weekly stats: 7 days
2025-05-27 13:55:05.650 - Saved 10 items to cache
2025-05-27 13:55:05.650 - Background data loading completed
2025-05-27 13:55:06.062 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:55:06.087 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:55:06.099 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:55:06.103 - Forcing refresh of all stats data
2025-05-27 13:55:06.104 - Attempting to force refresh via GameStatsIntegration
2025-05-27 13:55:06.122 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:55:06.131 - Force refresh via GameStatsIntegration successful
2025-05-27 13:55:06.136 - Clearing cached data
2025-05-27 13:55:06.137 - Starting background data reload
2025-05-27 13:55:06.138 - Starting background data loading thread
2025-05-27 13:55:06.139 - Background data loading started
2025-05-27 13:55:06.234 - Loading summary statistics
2025-05-27 13:55:06.235 - Attempting to load summary stats from GameStatsIntegration
2025-05-27 13:55:06.235 - Data from GameStatsIntegration: {'total_earnings': 220.0, 'daily_earnings': 220.0, 'daily_games': 3, 'wallet_balance': 0}
2025-05-27 13:55:06.235 - Successfully loaded summary stats from GameStatsIntegration
2025-05-27 13:55:06.236 - Successfully loaded summary stats: {'total_earnings': 220.0, 'daily_earnings': 220.0, 'daily_games': 3, 'wallet_balance': 0}
2025-05-27 13:55:06.237 - Successfully loaded game history: 3 entries
2025-05-27 13:55:06.238 - Successfully loaded weekly stats: 7 days
2025-05-27 13:55:06.240 - Saved 3 items to cache
2025-05-27 13:55:06.242 - Background data loading completed
2025-05-28 07:05:28.438 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 230.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-28 07:05:28.464 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 230.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-28 07:05:28.487 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 230.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-28 07:05:28.489 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-05-28 07:05:28.490 - Initializing StatsDataProvider
2025-05-28 07:05:28.534 - Loaded 8 items from cache
2025-05-28 07:05:28.535 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-05-28 07:05:28.535 - Starting background data loading thread
2025-05-28 07:05:28.536 - StatsDataProvider initialization completed
2025-05-28 07:05:28.537 - Loading summary statistics
2025-05-28 07:05:28.538 - Created singleton instance of StatsDataProvider on module import
2025-05-28 07:05:28.539 - Attempting to load summary stats from GameStatsIntegration
2025-05-28 07:05:28.539 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-05-28 07:05:28.540 - Data from GameStatsIntegration: {'total_earnings': 230.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-28 07:05:28.540 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:05:28.540 - Successfully loaded summary stats from GameStatsIntegration
2025-05-28 07:05:28.542 - Successfully loaded summary stats: {'total_earnings': 230.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-28 07:05:28.544 - Successfully loaded game history: 10 entries
2025-05-28 07:05:28.545 - Successfully loaded weekly stats: 7 days
2025-05-28 07:05:28.548 - Saved 10 items to cache
2025-05-28 07:05:28.549 - Background data loading completed
2025-05-28 07:07:29.896 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:07:29.929 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:07:29.950 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:07:29.953 - Forcing refresh of all stats data
2025-05-28 07:07:29.955 - Attempting to force refresh via GameStatsIntegration
2025-05-28 07:07:29.969 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:07:29.977 - Force refresh via GameStatsIntegration successful
2025-05-28 07:07:29.980 - Clearing cached data
2025-05-28 07:07:29.981 - Posted refresh_stats event to trigger UI update
2025-05-28 07:07:29.983 - Starting background data reload
2025-05-28 07:07:29.983 - Starting background data loading thread
2025-05-28 07:07:29.984 - Background data loading started
2025-05-28 07:07:30.039 - Loading summary statistics
2025-05-28 07:07:30.063 - Attempting to load summary stats from GameStatsIntegration
2025-05-28 07:07:30.063 - Data from GameStatsIntegration: {'total_earnings': 310.0, 'daily_earnings': 80.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-28 07:07:30.108 - Successfully loaded summary stats from GameStatsIntegration
2025-05-28 07:07:30.108 - Successfully loaded summary stats: {'total_earnings': 310.0, 'daily_earnings': 80.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-28 07:07:30.108 - Successfully loaded game history: 10 entries
2025-05-28 07:07:30.109 - Successfully loaded weekly stats: 7 days
2025-05-28 07:07:30.110 - Saved 3 items to cache
2025-05-28 07:07:30.110 - Background data loading completed
2025-05-28 07:15:38.072 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:23:13.526 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:31:19.320 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:37:37.337 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:47:29.458 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:56:22.233 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:02:27.673 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:08:29.547 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:19:53.445 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:25:51.682 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:36:45.378 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:50:01.265 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:50:01.282 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:50:01.289 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:50:01.291 - Forcing refresh of all stats data
2025-05-28 08:50:01.292 - Attempting to force refresh via GameStatsIntegration
2025-05-28 08:50:01.306 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:50:01.307 - Force refresh via GameStatsIntegration successful
2025-05-28 08:50:01.308 - Clearing cached data
2025-05-28 08:50:01.309 - Posted refresh_stats event to trigger UI update
2025-05-28 08:50:01.310 - Starting background data reload
2025-05-28 08:50:01.312 - Starting background data loading thread
2025-05-28 08:50:01.313 - Background data loading started
2025-05-28 08:50:01.327 - Loading summary statistics
2025-05-28 08:50:01.386 - Attempting to load summary stats from GameStatsIntegration
2025-05-28 08:50:01.389 - Data from GameStatsIntegration: {'total_earnings': 334.0, 'daily_earnings': 104.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-28 08:50:01.390 - Successfully loaded summary stats from GameStatsIntegration
2025-05-28 08:50:01.390 - Successfully loaded summary stats: {'total_earnings': 334.0, 'daily_earnings': 104.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-28 08:50:01.391 - Successfully loaded game history: 10 entries
2025-05-28 08:50:01.392 - Successfully loaded weekly stats: 7 days
2025-05-28 08:50:01.394 - Saved 3 items to cache
2025-05-28 08:50:01.394 - Background data loading completed
2025-05-28 08:50:11.757 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:56:15.523 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:56:15.640 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:56:15.670 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:56:15.680 - Forcing refresh of all stats data
2025-05-28 08:56:15.686 - Attempting to force refresh via GameStatsIntegration
2025-05-28 08:56:15.784 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:56:15.799 - Force refresh via GameStatsIntegration successful
2025-05-28 08:56:15.802 - Clearing cached data
2025-05-28 08:56:15.804 - Posted refresh_stats event to trigger UI update
2025-05-28 08:56:15.812 - Starting background data reload
2025-05-28 08:56:15.835 - Starting background data loading thread
2025-05-28 08:56:15.840 - Background data loading started
2025-05-28 08:56:15.858 - Loading summary statistics
2025-05-28 08:56:15.916 - Attempting to load summary stats from GameStatsIntegration
2025-05-28 08:56:15.943 - Data from GameStatsIntegration: {'total_earnings': 358.0, 'daily_earnings': 128.0, 'daily_games': 3, 'wallet_balance': 0}
2025-05-28 08:56:15.954 - Successfully loaded summary stats from GameStatsIntegration
2025-05-28 08:56:15.989 - Successfully loaded summary stats: {'total_earnings': 358.0, 'daily_earnings': 128.0, 'daily_games': 3, 'wallet_balance': 0}
2025-05-28 08:56:15.992 - Successfully loaded game history: 10 entries
2025-05-28 08:56:15.993 - Successfully loaded weekly stats: 7 days
2025-05-28 08:56:15.998 - Saved 3 items to cache
2025-05-28 08:56:16.007 - Background data loading completed
2025-05-28 08:56:26.521 - get_stats_provider called, returning provider with initialized=True
2025-05-28 09:29:17.970 - get_stats_provider called, returning provider with initialized=True
2025-05-28 09:29:17.987 - get_stats_provider called, returning provider with initialized=True
2025-05-28 09:29:17.990 - get_stats_provider called, returning provider with initialized=True
2025-05-28 09:29:17.991 - Forcing refresh of all stats data
2025-05-28 09:29:17.992 - Attempting to force refresh via GameStatsIntegration
2025-05-28 09:29:18.008 - get_stats_provider called, returning provider with initialized=True
2025-05-28 09:29:18.010 - Force refresh via GameStatsIntegration successful
2025-05-28 09:29:18.011 - Clearing cached data
2025-05-28 09:29:18.012 - Posted refresh_stats event to trigger UI update
2025-05-28 09:29:18.012 - Starting background data reload
2025-05-28 09:29:18.013 - Starting background data loading thread
2025-05-28 09:29:18.017 - Background data loading started
2025-05-28 09:29:18.031 - Loading summary statistics
2025-05-28 09:29:18.073 - Attempting to load summary stats from GameStatsIntegration
2025-05-28 09:29:18.115 - Data from GameStatsIntegration: {'total_earnings': 382.0, 'daily_earnings': 152.0, 'daily_games': 4, 'wallet_balance': 0}
2025-05-28 09:29:18.116 - Successfully loaded summary stats from GameStatsIntegration
2025-05-28 09:29:18.116 - Successfully loaded summary stats: {'total_earnings': 382.0, 'daily_earnings': 152.0, 'daily_games': 4, 'wallet_balance': 0}
2025-05-28 09:29:18.117 - Successfully loaded game history: 10 entries
2025-05-28 09:29:18.118 - Successfully loaded weekly stats: 7 days
2025-05-28 09:29:18.119 - Saved 3 items to cache
2025-05-28 09:29:18.119 - Background data loading completed
2025-05-29 10:36:04.404 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 382.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-29 10:36:04.627 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 382.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-29 10:36:04.644 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 382.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-29 10:36:04.645 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-05-29 10:36:04.646 - Initializing StatsDataProvider
2025-05-29 10:36:04.675 - Loaded 8 items from cache
2025-05-29 10:36:04.679 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-05-29 10:36:04.683 - Starting background data loading thread
2025-05-29 10:36:04.702 - Background data loading started
2025-05-29 10:36:04.702 - StatsDataProvider initialization completed
2025-05-29 10:36:04.705 - Loading summary statistics
2025-05-29 10:36:04.706 - Created singleton instance of StatsDataProvider on module import
2025-05-29 10:36:04.706 - Attempting to load summary stats from GameStatsIntegration
2025-05-29 10:36:04.708 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-05-29 10:36:04.996 - Data from GameStatsIntegration: {'total_earnings': 382.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-29 10:36:04.996 - get_stats_provider called, returning provider with initialized=True
2025-05-29 10:36:04.997 - Successfully loaded summary stats from GameStatsIntegration
2025-05-29 10:36:04.998 - Successfully loaded summary stats: {'total_earnings': 382.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-29 10:36:05.000 - Successfully loaded game history: 10 entries
2025-05-29 10:36:05.003 - Saved 10 items to cache
2025-05-29 10:36:05.005 - Background data loading completed
2025-05-29 10:41:13.769 - get_stats_provider called, returning provider with initialized=True
2025-05-29 10:41:13.811 - get_stats_provider called, returning provider with initialized=True
2025-05-29 10:41:13.831 - get_stats_provider called, returning provider with initialized=True
2025-05-29 10:41:13.832 - Forcing refresh of all stats data
2025-05-29 10:41:13.832 - Attempting to force refresh via GameStatsIntegration
2025-05-29 10:41:13.866 - get_stats_provider called, returning provider with initialized=True
2025-05-29 10:41:13.871 - Force refresh via GameStatsIntegration successful
2025-05-29 10:41:13.874 - Clearing cached data
2025-05-29 10:41:13.875 - Posted refresh_stats event to trigger UI update
2025-05-29 10:41:13.876 - Starting background data reload
2025-05-29 10:41:13.878 - Starting background data loading thread
2025-05-29 10:41:13.958 - Background data loading started
2025-05-29 10:41:14.008 - Loading summary statistics
2025-05-29 10:41:14.010 - Attempting to load summary stats from GameStatsIntegration
2025-05-29 10:41:14.069 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-29 10:41:14.075 - Successfully loaded summary stats from GameStatsIntegration
2025-05-29 10:41:14.076 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-29 10:41:14.078 - Saved 3 items to cache
2025-05-29 10:41:14.078 - Background data loading completed
2025-05-29 11:22:18.323 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 406.0, 'daily_earnings': 24.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-29 11:22:18.352 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 406.0, 'daily_earnings': 24.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-29 11:22:18.395 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 406.0, 'daily_earnings': 24.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-29 11:22:18.397 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-05-29 11:22:18.398 - Initializing StatsDataProvider
2025-05-29 11:22:18.456 - Loaded 8 items from cache
2025-05-29 11:22:18.457 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-05-29 11:22:18.457 - Starting background data loading thread
2025-05-29 11:22:18.460 - StatsDataProvider initialization completed
2025-05-29 11:22:18.460 - Created singleton instance of StatsDataProvider on module import
2025-05-29 11:22:18.460 - Loading summary statistics
2025-05-29 11:22:18.461 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-05-29 11:22:18.461 - Attempting to load summary stats from GameStatsIntegration
2025-05-29 11:22:18.462 - get_stats_provider called, returning provider with initialized=True
2025-05-29 11:22:18.462 - Data from GameStatsIntegration: {'total_earnings': 406.0, 'daily_earnings': 24.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-29 11:22:18.464 - Successfully loaded summary stats from GameStatsIntegration
2025-05-29 11:22:18.464 - Successfully loaded summary stats: {'total_earnings': 406.0, 'daily_earnings': 24.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-29 11:22:18.468 - Saved 10 items to cache
2025-05-29 11:22:18.468 - Background data loading completed
2025-05-29 18:29:03.362 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 406.0, 'daily_earnings': 24.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-29 18:29:03.421 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 406.0, 'daily_earnings': 24.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-29 18:29:03.453 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 406.0, 'daily_earnings': 24.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-29 18:29:03.457 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-05-29 18:29:03.459 - Initializing StatsDataProvider
2025-05-29 18:29:03.465 - Loaded 8 items from cache
2025-05-29 18:29:03.469 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-05-29 18:29:03.479 - Starting background data loading thread
2025-05-29 18:29:03.481 - Background data loading started
2025-05-29 18:29:03.481 - StatsDataProvider initialization completed
2025-05-29 18:29:03.488 - Loading summary statistics
2025-05-29 18:29:03.490 - Created singleton instance of StatsDataProvider on module import
2025-05-29 18:29:03.491 - Attempting to load summary stats from GameStatsIntegration
2025-05-29 18:29:03.492 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-05-29 18:29:03.493 - Data from GameStatsIntegration: {'total_earnings': 406.0, 'daily_earnings': 24.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-29 18:29:03.494 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:29:03.495 - Successfully loaded summary stats from GameStatsIntegration
2025-05-29 18:29:03.496 - Successfully loaded summary stats: {'total_earnings': 406.0, 'daily_earnings': 24.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-29 18:29:03.498 - Successfully loaded game history: 10 entries
2025-05-29 18:29:03.499 - Successfully loaded weekly stats: 7 days
2025-05-29 18:29:03.504 - Saved 10 items to cache
2025-05-29 18:29:03.512 - Background data loading completed
2025-05-29 18:33:25.158 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:33:25.187 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:33:25.193 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:33:25.204 - Forcing refresh of all stats data
2025-05-29 18:33:25.213 - Attempting to force refresh via GameStatsIntegration
2025-05-29 18:33:25.251 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:33:25.254 - Force refresh via GameStatsIntegration successful
2025-05-29 18:33:25.255 - Clearing cached data
2025-05-29 18:33:25.257 - Posted refresh_stats event to trigger UI update
2025-05-29 18:33:25.257 - Starting background data reload
2025-05-29 18:33:25.259 - Starting background data loading thread
2025-05-29 18:33:25.261 - Background data loading started
2025-05-29 18:33:25.290 - Loading summary statistics
2025-05-29 18:33:25.311 - Attempting to load summary stats from GameStatsIntegration
2025-05-29 18:33:25.312 - Data from GameStatsIntegration: {'total_earnings': 438.0, 'daily_earnings': 56.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-29 18:33:25.313 - Successfully loaded summary stats from GameStatsIntegration
2025-05-29 18:33:25.313 - Successfully loaded summary stats: {'total_earnings': 438.0, 'daily_earnings': 56.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-29 18:33:25.314 - Successfully loaded game history: 10 entries
2025-05-29 18:33:25.315 - Successfully loaded weekly stats: 7 days
2025-05-29 18:33:25.347 - Saved 3 items to cache
2025-05-29 18:33:25.348 - Background data loading completed
2025-05-29 18:34:48.668 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:37:19.120 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:37:19.408 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:37:19.441 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:37:19.451 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:37:19.454 - Forcing refresh of all stats data
2025-05-29 18:37:19.456 - Attempting to force refresh via GameStatsIntegration
2025-05-29 18:37:19.524 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:37:19.531 - Force refresh via GameStatsIntegration successful
2025-05-29 18:37:19.536 - Clearing cached data
2025-05-29 18:37:19.538 - Posted refresh_stats event to trigger UI update
2025-05-29 18:37:19.542 - Starting background data reload
2025-05-29 18:37:19.545 - Starting background data loading thread
2025-05-29 18:37:19.550 - Background data loading started
2025-05-29 18:37:19.568 - Loading summary statistics
2025-05-29 18:37:19.595 - Attempting to load summary stats from GameStatsIntegration
2025-05-29 18:37:19.597 - Data from GameStatsIntegration: {'total_earnings': 486.0, 'daily_earnings': 104.0, 'daily_games': 3, 'wallet_balance': 0}
2025-05-29 18:37:19.598 - Successfully loaded summary stats from GameStatsIntegration
2025-05-29 18:37:19.600 - Successfully loaded summary stats: {'total_earnings': 486.0, 'daily_earnings': 104.0, 'daily_games': 3, 'wallet_balance': 0}
2025-05-29 18:37:19.603 - Successfully loaded game history: 10 entries
2025-05-29 18:37:19.604 - Successfully loaded weekly stats: 7 days
2025-05-29 18:37:19.608 - Saved 3 items to cache
2025-05-29 18:37:19.609 - Background data loading completed
2025-05-29 18:38:45.937 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:38:45.956 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:38:45.960 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:38:45.961 - Forcing refresh of all stats data
2025-05-29 18:38:45.965 - Attempting to force refresh via GameStatsIntegration
2025-05-29 18:38:46.021 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:38:46.021 - Force refresh via GameStatsIntegration successful
2025-05-29 18:38:46.024 - Clearing cached data
2025-05-29 18:38:46.024 - Posted refresh_stats event to trigger UI update
2025-05-29 18:38:46.025 - Starting background data reload
2025-05-29 18:38:46.025 - Starting background data loading thread
2025-05-29 18:38:46.028 - Background data loading started
2025-05-29 18:38:46.039 - Loading summary statistics
2025-05-29 18:38:46.073 - Attempting to load summary stats from GameStatsIntegration
2025-05-29 18:38:46.074 - Data from GameStatsIntegration: {'total_earnings': 534.0, 'daily_earnings': 152.0, 'daily_games': 4, 'wallet_balance': 0}
2025-05-29 18:38:46.075 - Successfully loaded summary stats from GameStatsIntegration
2025-05-29 18:38:46.081 - Successfully loaded summary stats: {'total_earnings': 534.0, 'daily_earnings': 152.0, 'daily_games': 4, 'wallet_balance': 0}
2025-05-29 18:38:46.084 - Successfully loaded game history: 10 entries
2025-05-29 18:38:46.085 - Successfully loaded weekly stats: 7 days
2025-05-29 18:38:46.089 - Saved 3 items to cache
2025-05-29 18:38:46.090 - Background data loading completed
2025-05-29 18:40:22.115 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:43:22.953 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:43:22.974 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:43:22.976 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:43:22.977 - Forcing refresh of all stats data
2025-05-29 18:43:22.978 - Attempting to force refresh via GameStatsIntegration
2025-05-29 18:43:22.996 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:43:22.997 - Force refresh via GameStatsIntegration successful
2025-05-29 18:43:22.998 - Clearing cached data
2025-05-29 18:43:23.000 - Posted refresh_stats event to trigger UI update
2025-05-29 18:43:23.001 - Starting background data reload
2025-05-29 18:43:23.004 - Starting background data loading thread
2025-05-29 18:43:23.005 - Background data loading started
2025-05-29 18:43:23.042 - Loading summary statistics
2025-05-29 18:43:23.065 - Attempting to load summary stats from GameStatsIntegration
2025-05-29 18:43:23.067 - Data from GameStatsIntegration: {'total_earnings': 553.2, 'daily_earnings': 171.2, 'daily_games': 5, 'wallet_balance': 0}
2025-05-29 18:43:23.068 - Successfully loaded summary stats from GameStatsIntegration
2025-05-29 18:43:23.069 - Successfully loaded summary stats: {'total_earnings': 553.2, 'daily_earnings': 171.2, 'daily_games': 5, 'wallet_balance': 0}
2025-05-29 18:43:23.070 - Successfully loaded game history: 10 entries
2025-05-29 18:43:23.070 - Successfully loaded weekly stats: 7 days
2025-05-29 18:43:23.074 - Saved 3 items to cache
2025-05-29 18:43:23.075 - Background data loading completed
2025-05-29 18:45:57.336 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:50:22.314 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:50:22.331 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:50:22.333 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:50:22.335 - Forcing refresh of all stats data
2025-05-29 18:50:22.336 - Attempting to force refresh via GameStatsIntegration
2025-05-29 18:50:22.351 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:50:22.352 - Force refresh via GameStatsIntegration successful
2025-05-29 18:50:22.353 - Clearing cached data
2025-05-29 18:50:22.354 - Posted refresh_stats event to trigger UI update
2025-05-29 18:50:22.354 - Starting background data reload
2025-05-29 18:50:22.356 - Starting background data loading thread
2025-05-29 18:50:22.359 - Background data loading started
2025-05-29 18:50:22.370 - Loading summary statistics
2025-05-29 18:50:22.412 - Attempting to load summary stats from GameStatsIntegration
2025-05-29 18:50:22.413 - Data from GameStatsIntegration: {'total_earnings': 572.4, 'daily_earnings': 190.39999999999998, 'daily_games': 6, 'wallet_balance': 0}
2025-05-29 18:50:22.414 - Successfully loaded summary stats from GameStatsIntegration
2025-05-29 18:50:22.426 - Successfully loaded summary stats: {'total_earnings': 572.4, 'daily_earnings': 190.39999999999998, 'daily_games': 6, 'wallet_balance': 0}
2025-05-29 18:50:22.428 - Successfully loaded game history: 10 entries
2025-05-29 18:50:22.429 - Successfully loaded weekly stats: 7 days
2025-05-29 18:50:22.432 - Saved 3 items to cache
2025-05-29 18:50:22.444 - Background data loading completed
2025-05-29 18:51:16.051 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:55:21.151 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:55:21.168 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:55:21.171 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:55:21.172 - Forcing refresh of all stats data
2025-05-29 18:55:21.173 - Attempting to force refresh via GameStatsIntegration
2025-05-29 18:55:21.192 - get_stats_provider called, returning provider with initialized=True
2025-05-29 18:55:21.193 - Force refresh via GameStatsIntegration successful
2025-05-29 18:55:21.193 - Clearing cached data
2025-05-29 18:55:21.194 - Posted refresh_stats event to trigger UI update
2025-05-29 18:55:21.194 - Starting background data reload
2025-05-29 18:55:21.196 - Starting background data loading thread
2025-05-29 18:55:21.199 - Background data loading started
2025-05-29 18:55:21.210 - Loading summary statistics
2025-05-29 18:55:21.244 - Attempting to load summary stats from GameStatsIntegration
2025-05-29 18:55:21.246 - Data from GameStatsIntegration: {'total_earnings': 586.8, 'daily_earnings': 204.79999999999998, 'daily_games': 7, 'wallet_balance': 0}
2025-05-29 18:55:21.246 - Successfully loaded summary stats from GameStatsIntegration
2025-05-29 18:55:21.247 - Successfully loaded summary stats: {'total_earnings': 586.8, 'daily_earnings': 204.79999999999998, 'daily_games': 7, 'wallet_balance': 0}
2025-05-29 18:55:21.247 - Successfully loaded game history: 10 entries
2025-05-29 18:55:21.248 - Successfully loaded weekly stats: 7 days
2025-05-29 18:55:21.250 - Saved 3 items to cache
2025-05-29 18:55:21.251 - Background data loading completed
2025-05-29 18:55:52.471 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:00:25.862 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:00:25.877 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:00:25.880 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:00:25.881 - Forcing refresh of all stats data
2025-05-29 19:00:25.882 - Attempting to force refresh via GameStatsIntegration
2025-05-29 19:00:25.899 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:00:25.900 - Force refresh via GameStatsIntegration successful
2025-05-29 19:00:25.901 - Clearing cached data
2025-05-29 19:00:25.902 - Posted refresh_stats event to trigger UI update
2025-05-29 19:00:25.902 - Starting background data reload
2025-05-29 19:00:25.904 - Starting background data loading thread
2025-05-29 19:00:25.906 - Background data loading started
2025-05-29 19:00:25.924 - Loading summary statistics
2025-05-29 19:00:25.953 - Attempting to load summary stats from GameStatsIntegration
2025-05-29 19:00:25.954 - Data from GameStatsIntegration: {'total_earnings': 601.2, 'daily_earnings': 219.2, 'daily_games': 8, 'wallet_balance': 0}
2025-05-29 19:00:25.955 - Successfully loaded summary stats from GameStatsIntegration
2025-05-29 19:00:25.955 - Successfully loaded summary stats: {'total_earnings': 601.2, 'daily_earnings': 219.2, 'daily_games': 8, 'wallet_balance': 0}
2025-05-29 19:00:25.956 - Successfully loaded game history: 10 entries
2025-05-29 19:00:25.957 - Successfully loaded weekly stats: 7 days
2025-05-29 19:00:25.959 - Saved 3 items to cache
2025-05-29 19:00:25.960 - Background data loading completed
2025-05-29 19:03:14.094 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:05:50.360 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:05:50.378 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:05:50.384 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:05:50.387 - Forcing refresh of all stats data
2025-05-29 19:05:50.388 - Attempting to force refresh via GameStatsIntegration
2025-05-29 19:05:50.404 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:05:50.406 - Force refresh via GameStatsIntegration successful
2025-05-29 19:05:50.407 - Clearing cached data
2025-05-29 19:05:50.408 - Posted refresh_stats event to trigger UI update
2025-05-29 19:05:50.408 - Starting background data reload
2025-05-29 19:05:50.409 - Starting background data loading thread
2025-05-29 19:05:50.412 - Background data loading started
2025-05-29 19:05:50.424 - Loading summary statistics
2025-05-29 19:05:50.471 - Attempting to load summary stats from GameStatsIntegration
2025-05-29 19:05:50.476 - Data from GameStatsIntegration: {'total_earnings': 615.6, 'daily_earnings': 233.6, 'daily_games': 9, 'wallet_balance': 0}
2025-05-29 19:05:50.477 - Successfully loaded summary stats from GameStatsIntegration
2025-05-29 19:05:50.479 - Successfully loaded summary stats: {'total_earnings': 615.6, 'daily_earnings': 233.6, 'daily_games': 9, 'wallet_balance': 0}
2025-05-29 19:05:50.480 - Successfully loaded game history: 10 entries
2025-05-29 19:05:50.480 - Successfully loaded weekly stats: 7 days
2025-05-29 19:05:50.482 - Saved 3 items to cache
2025-05-29 19:05:50.484 - Background data loading completed
2025-05-29 19:09:38.308 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:11:59.074 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:11:59.094 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:11:59.099 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:11:59.100 - Forcing refresh of all stats data
2025-05-29 19:11:59.101 - Attempting to force refresh via GameStatsIntegration
2025-05-29 19:11:59.117 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:11:59.119 - Force refresh via GameStatsIntegration successful
2025-05-29 19:11:59.120 - Clearing cached data
2025-05-29 19:11:59.120 - Posted refresh_stats event to trigger UI update
2025-05-29 19:11:59.121 - Starting background data reload
2025-05-29 19:11:59.122 - Starting background data loading thread
2025-05-29 19:11:59.125 - Background data loading started
2025-05-29 19:11:59.140 - Loading summary statistics
2025-05-29 19:11:59.168 - Attempting to load summary stats from GameStatsIntegration
2025-05-29 19:11:59.168 - Data from GameStatsIntegration: {'total_earnings': 634.8, 'daily_earnings': 252.8, 'daily_games': 10, 'wallet_balance': 0}
2025-05-29 19:11:59.169 - Successfully loaded summary stats from GameStatsIntegration
2025-05-29 19:11:59.178 - Successfully loaded summary stats: {'total_earnings': 634.8, 'daily_earnings': 252.8, 'daily_games': 10, 'wallet_balance': 0}
2025-05-29 19:11:59.179 - Successfully loaded game history: 10 entries
2025-05-29 19:11:59.180 - Successfully loaded weekly stats: 7 days
2025-05-29 19:11:59.182 - Saved 3 items to cache
2025-05-29 19:11:59.182 - Background data loading completed
2025-05-29 19:13:40.253 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:15:42.194 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:15:42.215 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:15:42.224 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:15:42.227 - Forcing refresh of all stats data
2025-05-29 19:15:42.228 - Attempting to force refresh via GameStatsIntegration
2025-05-29 19:15:42.242 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:15:42.243 - Force refresh via GameStatsIntegration successful
2025-05-29 19:15:42.244 - Clearing cached data
2025-05-29 19:15:42.246 - Posted refresh_stats event to trigger UI update
2025-05-29 19:15:42.247 - Starting background data reload
2025-05-29 19:15:42.251 - Starting background data loading thread
2025-05-29 19:15:42.254 - Background data loading started
2025-05-29 19:15:42.280 - Loading summary statistics
2025-05-29 19:15:42.317 - Attempting to load summary stats from GameStatsIntegration
2025-05-29 19:15:42.317 - Data from GameStatsIntegration: {'total_earnings': 654.0, 'daily_earnings': 272.0, 'daily_games': 11, 'wallet_balance': 0}
2025-05-29 19:15:42.318 - Successfully loaded summary stats from GameStatsIntegration
2025-05-29 19:15:42.318 - Successfully loaded summary stats: {'total_earnings': 654.0, 'daily_earnings': 272.0, 'daily_games': 11, 'wallet_balance': 0}
2025-05-29 19:15:42.319 - Successfully loaded game history: 10 entries
2025-05-29 19:15:42.320 - Successfully loaded weekly stats: 7 days
2025-05-29 19:15:42.323 - Saved 3 items to cache
2025-05-29 19:15:42.323 - Background data loading completed
2025-05-29 19:18:02.717 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:18:09.320 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:18:09.338 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:18:09.341 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:18:09.342 - Forcing refresh of all stats data
2025-05-29 19:18:09.343 - Attempting to force refresh via GameStatsIntegration
2025-05-29 19:18:09.430 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:18:09.455 - Force refresh via GameStatsIntegration successful
2025-05-29 19:18:09.459 - Clearing cached data
2025-05-29 19:18:09.462 - Posted refresh_stats event to trigger UI update
2025-05-29 19:18:09.467 - Starting background data reload
2025-05-29 19:18:09.482 - Starting background data loading thread
2025-05-29 19:18:09.510 - Background data loading started
2025-05-29 19:18:09.513 - Loading summary statistics
2025-05-29 19:18:09.547 - Attempting to load summary stats from GameStatsIntegration
2025-05-29 19:18:09.610 - Data from GameStatsIntegration: {'total_earnings': 673.2, 'daily_earnings': 291.2, 'daily_games': 12, 'wallet_balance': 0}
2025-05-29 19:18:09.628 - Successfully loaded summary stats from GameStatsIntegration
2025-05-29 19:18:09.633 - Successfully loaded summary stats: {'total_earnings': 673.2, 'daily_earnings': 291.2, 'daily_games': 12, 'wallet_balance': 0}
2025-05-29 19:18:09.646 - Successfully loaded game history: 10 entries
2025-05-29 19:18:09.650 - Successfully loaded weekly stats: 7 days
2025-05-29 19:18:09.655 - Saved 3 items to cache
2025-05-29 19:18:09.656 - Background data loading completed
2025-05-29 19:19:19.355 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:21:29.083 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:21:29.121 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:21:29.194 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:21:29.202 - Forcing refresh of all stats data
2025-05-29 19:21:29.205 - Attempting to force refresh via GameStatsIntegration
2025-05-29 19:21:29.337 - get_stats_provider called, returning provider with initialized=True
2025-05-29 19:21:29.343 - Force refresh via GameStatsIntegration successful
2025-05-29 19:21:29.386 - Clearing cached data
2025-05-29 19:21:29.391 - Posted refresh_stats event to trigger UI update
2025-05-29 19:21:29.407 - Starting background data reload
2025-05-29 19:21:29.414 - Starting background data loading thread
2025-05-29 19:21:29.423 - Background data loading started
2025-05-29 19:21:29.430 - Loading summary statistics
2025-05-29 19:21:29.453 - Attempting to load summary stats from GameStatsIntegration
2025-05-29 19:21:29.476 - Data from GameStatsIntegration: {'total_earnings': 692.4, 'daily_earnings': 310.4, 'daily_games': 13, 'wallet_balance': 0}
2025-05-29 19:21:29.540 - Successfully loaded summary stats from GameStatsIntegration
2025-05-29 19:21:29.541 - Successfully loaded summary stats: {'total_earnings': 692.4, 'daily_earnings': 310.4, 'daily_games': 13, 'wallet_balance': 0}
2025-05-29 19:21:29.546 - Successfully loaded game history: 10 entries
2025-05-29 19:21:29.556 - Successfully loaded weekly stats: 7 days
2025-05-29 19:21:29.594 - Saved 3 items to cache
2025-05-29 19:21:29.598 - Background data loading completed
2025-05-30 18:47:30.760 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 692.4, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 18:47:30.780 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 692.4, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 18:47:30.810 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 692.4, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 18:47:30.810 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-05-30 18:47:30.811 - Initializing StatsDataProvider
2025-05-30 18:47:30.824 - Loaded 8 items from cache
2025-05-30 18:47:30.825 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-05-30 18:47:30.825 - Starting background data loading thread
2025-05-30 18:47:30.826 - Background data loading started
2025-05-30 18:47:30.826 - StatsDataProvider initialization completed
2025-05-30 18:47:30.826 - Loading summary statistics
2025-05-30 18:47:30.827 - Created singleton instance of StatsDataProvider on module import
2025-05-30 18:47:30.827 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 18:47:30.828 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-05-30 18:47:30.828 - Data from GameStatsIntegration: {'total_earnings': 692.4, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 18:47:30.828 - get_stats_provider called, returning provider with initialized=True
2025-05-30 18:47:30.829 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 18:47:30.829 - Successfully loaded summary stats: {'total_earnings': 692.4, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 18:47:30.831 - Successfully loaded game history: 10 entries
2025-05-30 18:47:30.832 - Successfully loaded weekly stats: 7 days
2025-05-30 18:47:30.836 - Saved 10 items to cache
2025-05-30 18:47:30.838 - Background data loading completed
2025-05-30 18:50:13.439 - get_stats_provider called, returning provider with initialized=True
2025-05-30 18:50:13.465 - get_stats_provider called, returning provider with initialized=True
2025-05-30 18:50:13.470 - get_stats_provider called, returning provider with initialized=True
2025-05-30 18:50:13.471 - Forcing refresh of all stats data
2025-05-30 18:50:13.474 - Attempting to force refresh via GameStatsIntegration
2025-05-30 18:50:13.488 - get_stats_provider called, returning provider with initialized=True
2025-05-30 18:50:13.491 - Force refresh via GameStatsIntegration successful
2025-05-30 18:50:13.492 - Clearing cached data
2025-05-30 18:50:13.492 - Posted refresh_stats event to trigger UI update
2025-05-30 18:50:13.493 - Starting background data reload
2025-05-30 18:50:13.495 - Starting background data loading thread
2025-05-30 18:50:13.497 - Background data loading started
2025-05-30 18:50:13.497 - Loading summary statistics
2025-05-30 18:50:13.547 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 18:50:13.548 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 18:50:13.549 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 18:50:13.549 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 18:50:13.551 - Saved 3 items to cache
2025-05-30 18:50:13.551 - Background data loading completed
2025-05-30 18:52:21.032 - get_stats_provider called, returning provider with initialized=True
2025-05-30 18:55:06.775 - get_stats_provider called, returning provider with initialized=True
2025-05-30 18:55:06.792 - get_stats_provider called, returning provider with initialized=True
2025-05-30 18:55:06.796 - get_stats_provider called, returning provider with initialized=True
2025-05-30 18:55:06.797 - Forcing refresh of all stats data
2025-05-30 18:55:06.800 - Attempting to force refresh via GameStatsIntegration
2025-05-30 18:55:06.812 - get_stats_provider called, returning provider with initialized=True
2025-05-30 18:55:06.813 - Force refresh via GameStatsIntegration successful
2025-05-30 18:55:06.814 - Clearing cached data
2025-05-30 18:55:06.815 - Posted refresh_stats event to trigger UI update
2025-05-30 18:55:06.816 - Starting background data reload
2025-05-30 18:55:06.819 - Starting background data loading thread
2025-05-30 18:55:06.820 - Background data loading started
2025-05-30 18:55:06.831 - Loading summary statistics
2025-05-30 18:55:06.867 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 18:55:06.869 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 18:55:06.869 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 18:55:06.870 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 18:55:06.872 - Saved 3 items to cache
2025-05-30 18:55:06.872 - Background data loading completed
2025-05-30 18:56:40.107 - get_stats_provider called, returning provider with initialized=True
2025-05-30 18:59:27.940 - get_stats_provider called, returning provider with initialized=True
2025-05-30 18:59:27.991 - get_stats_provider called, returning provider with initialized=True
2025-05-30 18:59:28.003 - get_stats_provider called, returning provider with initialized=True
2025-05-30 18:59:28.007 - Forcing refresh of all stats data
2025-05-30 18:59:28.010 - Attempting to force refresh via GameStatsIntegration
2025-05-30 18:59:28.045 - get_stats_provider called, returning provider with initialized=True
2025-05-30 18:59:28.047 - Force refresh via GameStatsIntegration successful
2025-05-30 18:59:28.047 - Clearing cached data
2025-05-30 18:59:28.048 - Posted refresh_stats event to trigger UI update
2025-05-30 18:59:28.049 - Starting background data reload
2025-05-30 18:59:28.051 - Starting background data loading thread
2025-05-30 18:59:28.052 - Background data loading started
2025-05-30 18:59:28.063 - Loading summary statistics
2025-05-30 18:59:28.096 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 18:59:28.098 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 18:59:28.099 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 18:59:28.099 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 18:59:28.136 - Saved 3 items to cache
2025-05-30 18:59:28.136 - Background data loading completed
2025-05-30 19:02:07.168 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:04:42.583 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:04:42.600 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:04:42.603 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:04:42.605 - Forcing refresh of all stats data
2025-05-30 19:04:42.606 - Attempting to force refresh via GameStatsIntegration
2025-05-30 19:04:42.617 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:04:42.619 - Force refresh via GameStatsIntegration successful
2025-05-30 19:04:42.619 - Clearing cached data
2025-05-30 19:04:42.620 - Posted refresh_stats event to trigger UI update
2025-05-30 19:04:42.623 - Starting background data reload
2025-05-30 19:04:42.624 - Starting background data loading thread
2025-05-30 19:04:42.625 - Background data loading started
2025-05-30 19:04:42.636 - Loading summary statistics
2025-05-30 19:04:42.674 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 19:04:42.675 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:04:42.675 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 19:04:42.676 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:04:42.677 - Saved 3 items to cache
2025-05-30 19:04:42.680 - Background data loading completed
2025-05-30 19:09:13.319 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:09:16.541 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:09:16.556 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:09:16.560 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:09:16.560 - Forcing refresh of all stats data
2025-05-30 19:09:16.562 - Attempting to force refresh via GameStatsIntegration
2025-05-30 19:09:16.578 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:09:16.579 - Force refresh via GameStatsIntegration successful
2025-05-30 19:09:16.580 - Clearing cached data
2025-05-30 19:09:16.581 - Posted refresh_stats event to trigger UI update
2025-05-30 19:09:16.581 - Starting background data reload
2025-05-30 19:09:16.582 - Starting background data loading thread
2025-05-30 19:09:16.584 - Background data loading started
2025-05-30 19:09:16.587 - Loading summary statistics
2025-05-30 19:09:16.648 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 19:09:16.649 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:09:16.650 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 19:09:16.650 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:09:16.652 - Saved 3 items to cache
2025-05-30 19:09:16.653 - Background data loading completed
2025-05-30 19:09:35.957 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:13:22.026 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:13:22.043 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:13:22.047 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:13:22.048 - Forcing refresh of all stats data
2025-05-30 19:13:22.050 - Attempting to force refresh via GameStatsIntegration
2025-05-30 19:13:22.061 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:13:22.062 - Force refresh via GameStatsIntegration successful
2025-05-30 19:13:22.064 - Clearing cached data
2025-05-30 19:13:22.064 - Posted refresh_stats event to trigger UI update
2025-05-30 19:13:22.065 - Starting background data reload
2025-05-30 19:13:22.067 - Starting background data loading thread
2025-05-30 19:13:22.068 - Background data loading started
2025-05-30 19:13:22.079 - Loading summary statistics
2025-05-30 19:13:22.117 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 19:13:22.119 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:13:22.119 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 19:13:22.120 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:13:22.122 - Saved 3 items to cache
2025-05-30 19:13:22.122 - Background data loading completed
2025-05-30 19:15:35.919 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:17:21.625 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:17:21.639 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:17:21.642 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:17:21.643 - Forcing refresh of all stats data
2025-05-30 19:17:21.644 - Attempting to force refresh via GameStatsIntegration
2025-05-30 19:17:21.658 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:17:21.659 - Force refresh via GameStatsIntegration successful
2025-05-30 19:17:21.660 - Clearing cached data
2025-05-30 19:17:21.661 - Posted refresh_stats event to trigger UI update
2025-05-30 19:17:21.662 - Starting background data reload
2025-05-30 19:17:21.663 - Starting background data loading thread
2025-05-30 19:17:21.665 - Background data loading started
2025-05-30 19:17:21.676 - Loading summary statistics
2025-05-30 19:17:21.707 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 19:17:21.707 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:17:21.708 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 19:17:21.708 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:17:21.709 - Saved 3 items to cache
2025-05-30 19:17:21.710 - Background data loading completed
2025-05-30 19:19:07.435 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:23:42.801 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:23:42.818 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:23:42.822 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:23:42.823 - Forcing refresh of all stats data
2025-05-30 19:23:42.823 - Attempting to force refresh via GameStatsIntegration
2025-05-30 19:23:42.835 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:23:42.837 - Force refresh via GameStatsIntegration successful
2025-05-30 19:23:42.838 - Clearing cached data
2025-05-30 19:23:42.838 - Posted refresh_stats event to trigger UI update
2025-05-30 19:23:42.839 - Starting background data reload
2025-05-30 19:23:42.840 - Starting background data loading thread
2025-05-30 19:23:42.843 - Background data loading started
2025-05-30 19:23:42.855 - Loading summary statistics
2025-05-30 19:23:42.911 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 19:23:42.913 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:23:42.915 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 19:23:42.916 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:23:42.917 - Saved 3 items to cache
2025-05-30 19:23:42.917 - Background data loading completed
2025-05-30 19:24:35.281 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:30:17.860 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:30:17.873 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:30:17.878 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:30:17.879 - Forcing refresh of all stats data
2025-05-30 19:30:17.880 - Attempting to force refresh via GameStatsIntegration
2025-05-30 19:30:17.903 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:30:17.905 - Force refresh via GameStatsIntegration successful
2025-05-30 19:30:17.906 - Clearing cached data
2025-05-30 19:30:17.907 - Posted refresh_stats event to trigger UI update
2025-05-30 19:30:17.908 - Starting background data reload
2025-05-30 19:30:17.910 - Starting background data loading thread
2025-05-30 19:30:17.911 - Background data loading started
2025-05-30 19:30:17.923 - Loading summary statistics
2025-05-30 19:30:17.960 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 19:30:17.961 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:30:17.961 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 19:30:17.962 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:30:17.963 - Saved 3 items to cache
2025-05-30 19:30:17.963 - Background data loading completed
2025-05-30 19:30:52.292 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:34:18.139 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:34:18.165 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:34:18.169 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:34:18.170 - Forcing refresh of all stats data
2025-05-30 19:34:18.172 - Attempting to force refresh via GameStatsIntegration
2025-05-30 19:34:18.183 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:34:18.186 - Force refresh via GameStatsIntegration successful
2025-05-30 19:34:18.188 - Clearing cached data
2025-05-30 19:34:18.189 - Posted refresh_stats event to trigger UI update
2025-05-30 19:34:18.189 - Starting background data reload
2025-05-30 19:34:18.191 - Starting background data loading thread
2025-05-30 19:34:18.192 - Background data loading started
2025-05-30 19:34:18.202 - Loading summary statistics
2025-05-30 19:34:18.281 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 19:34:18.285 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:34:18.286 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 19:34:18.286 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:34:18.287 - Saved 3 items to cache
2025-05-30 19:34:18.289 - Background data loading completed
2025-05-30 19:36:43.980 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:42:05.473 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:42:05.550 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:42:05.562 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:42:05.563 - Forcing refresh of all stats data
2025-05-30 19:42:05.569 - Attempting to force refresh via GameStatsIntegration
2025-05-30 19:42:05.584 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:42:05.586 - Force refresh via GameStatsIntegration successful
2025-05-30 19:42:05.587 - Clearing cached data
2025-05-30 19:42:05.590 - Posted refresh_stats event to trigger UI update
2025-05-30 19:42:05.591 - Starting background data reload
2025-05-30 19:42:05.592 - Starting background data loading thread
2025-05-30 19:42:05.594 - Background data loading started
2025-05-30 19:42:05.626 - Loading summary statistics
2025-05-30 19:42:05.639 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 19:42:05.640 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:42:05.640 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 19:42:05.641 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:42:05.642 - Saved 3 items to cache
2025-05-30 19:42:05.642 - Background data loading completed
2025-05-30 19:45:24.692 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:47:53.177 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:47:53.192 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:47:53.196 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:47:53.197 - Forcing refresh of all stats data
2025-05-30 19:47:53.198 - Attempting to force refresh via GameStatsIntegration
2025-05-30 19:47:53.212 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:47:53.214 - Force refresh via GameStatsIntegration successful
2025-05-30 19:47:53.214 - Clearing cached data
2025-05-30 19:47:53.215 - Posted refresh_stats event to trigger UI update
2025-05-30 19:47:53.216 - Starting background data reload
2025-05-30 19:47:53.219 - Starting background data loading thread
2025-05-30 19:47:53.220 - Background data loading started
2025-05-30 19:47:53.230 - Loading summary statistics
2025-05-30 19:47:53.268 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 19:47:53.269 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:47:53.270 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 19:47:53.270 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:47:53.271 - Saved 3 items to cache
2025-05-30 19:47:53.272 - Background data loading completed
2025-05-30 19:51:33.258 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:56:34.172 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:56:34.188 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:56:34.191 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:56:34.192 - Forcing refresh of all stats data
2025-05-30 19:56:34.193 - Attempting to force refresh via GameStatsIntegration
2025-05-30 19:56:34.205 - get_stats_provider called, returning provider with initialized=True
2025-05-30 19:56:34.207 - Force refresh via GameStatsIntegration successful
2025-05-30 19:56:34.207 - Clearing cached data
2025-05-30 19:56:34.209 - Posted refresh_stats event to trigger UI update
2025-05-30 19:56:34.209 - Starting background data reload
2025-05-30 19:56:34.211 - Starting background data loading thread
2025-05-30 19:56:34.213 - Background data loading started
2025-05-30 19:56:34.224 - Loading summary statistics
2025-05-30 19:56:34.262 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 19:56:34.263 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:56:34.264 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 19:56:34.264 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 19:56:34.266 - Saved 3 items to cache
2025-05-30 19:56:34.266 - Background data loading completed
2025-05-30 19:58:19.873 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:00:03.903 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:00:03.917 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:00:03.920 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:00:03.921 - Forcing refresh of all stats data
2025-05-30 20:00:03.923 - Attempting to force refresh via GameStatsIntegration
2025-05-30 20:00:03.935 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:00:03.936 - Force refresh via GameStatsIntegration successful
2025-05-30 20:00:03.937 - Clearing cached data
2025-05-30 20:00:03.937 - Posted refresh_stats event to trigger UI update
2025-05-30 20:00:03.938 - Starting background data reload
2025-05-30 20:00:03.939 - Starting background data loading thread
2025-05-30 20:00:03.941 - Background data loading started
2025-05-30 20:00:03.953 - Loading summary statistics
2025-05-30 20:00:03.990 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 20:00:03.991 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 20:00:03.992 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 20:00:03.992 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 20:00:03.994 - Saved 3 items to cache
2025-05-30 20:00:03.994 - Background data loading completed
2025-05-30 20:04:07.362 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:10:55.234 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:10:55.248 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:10:55.251 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:10:55.251 - Forcing refresh of all stats data
2025-05-30 20:10:55.255 - Attempting to force refresh via GameStatsIntegration
2025-05-30 20:10:55.264 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:10:55.266 - Force refresh via GameStatsIntegration successful
2025-05-30 20:10:55.266 - Clearing cached data
2025-05-30 20:10:55.267 - Posted refresh_stats event to trigger UI update
2025-05-30 20:10:55.268 - Starting background data reload
2025-05-30 20:10:55.269 - Starting background data loading thread
2025-05-30 20:10:55.271 - Background data loading started
2025-05-30 20:10:55.287 - Loading summary statistics
2025-05-30 20:10:55.313 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 20:10:55.314 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 20:10:55.315 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 20:10:55.315 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 20:10:55.317 - Saved 3 items to cache
2025-05-30 20:10:55.318 - Background data loading completed
2025-05-30 20:12:04.842 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:12:11.417 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:12:11.430 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:12:11.434 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:12:11.435 - Forcing refresh of all stats data
2025-05-30 20:12:11.438 - Attempting to force refresh via GameStatsIntegration
2025-05-30 20:12:11.447 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:12:11.448 - Force refresh via GameStatsIntegration successful
2025-05-30 20:12:11.449 - Clearing cached data
2025-05-30 20:12:11.450 - Posted refresh_stats event to trigger UI update
2025-05-30 20:12:11.450 - Starting background data reload
2025-05-30 20:12:11.452 - Starting background data loading thread
2025-05-30 20:12:11.454 - Background data loading started
2025-05-30 20:12:11.466 - Loading summary statistics
2025-05-30 20:12:11.506 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 20:12:11.507 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 20:12:11.507 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 20:12:11.507 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 20:12:11.509 - Saved 3 items to cache
2025-05-30 20:12:11.509 - Background data loading completed
2025-05-30 20:12:41.056 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:17:57.950 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:17:57.962 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:17:57.966 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:17:57.968 - Forcing refresh of all stats data
2025-05-30 20:17:57.968 - Attempting to force refresh via GameStatsIntegration
2025-05-30 20:17:57.989 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:17:57.991 - Force refresh via GameStatsIntegration successful
2025-05-30 20:17:57.992 - Clearing cached data
2025-05-30 20:17:57.993 - Posted refresh_stats event to trigger UI update
2025-05-30 20:17:57.994 - Starting background data reload
2025-05-30 20:17:57.994 - Starting background data loading thread
2025-05-30 20:17:57.998 - Background data loading started
2025-05-30 20:17:58.010 - Loading summary statistics
2025-05-30 20:17:58.041 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 20:17:58.042 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 20:17:58.042 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 20:17:58.043 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 20:17:58.044 - Saved 3 items to cache
2025-05-30 20:17:58.044 - Background data loading completed
2025-05-30 20:20:26.580 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:24:39.971 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:24:39.985 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:24:39.988 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:24:39.989 - Forcing refresh of all stats data
2025-05-30 20:24:39.991 - Attempting to force refresh via GameStatsIntegration
2025-05-30 20:24:40.000 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:24:40.002 - Force refresh via GameStatsIntegration successful
2025-05-30 20:24:40.004 - Clearing cached data
2025-05-30 20:24:40.004 - Posted refresh_stats event to trigger UI update
2025-05-30 20:24:40.005 - Starting background data reload
2025-05-30 20:24:40.007 - Starting background data loading thread
2025-05-30 20:24:40.008 - Background data loading started
2025-05-30 20:24:40.018 - Loading summary statistics
2025-05-30 20:24:40.049 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 20:24:40.050 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 20:24:40.050 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 20:24:40.052 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 20:24:40.054 - Saved 3 items to cache
2025-05-30 20:24:40.054 - Background data loading completed
2025-05-30 20:25:32.579 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:29:18.447 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:29:18.463 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:29:18.466 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:29:18.467 - Forcing refresh of all stats data
2025-05-30 20:29:18.467 - Attempting to force refresh via GameStatsIntegration
2025-05-30 20:29:18.478 - get_stats_provider called, returning provider with initialized=True
2025-05-30 20:29:18.480 - Force refresh via GameStatsIntegration successful
2025-05-30 20:29:18.481 - Clearing cached data
2025-05-30 20:29:18.482 - Posted refresh_stats event to trigger UI update
2025-05-30 20:29:18.482 - Starting background data reload
2025-05-30 20:29:18.484 - Starting background data loading thread
2025-05-30 20:29:18.485 - Background data loading started
2025-05-30 20:29:18.497 - Loading summary statistics
2025-05-30 20:29:18.527 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 20:29:18.528 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 20:29:18.529 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 20:29:18.529 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 20:29:18.530 - Saved 3 items to cache
2025-05-30 20:29:18.531 - Background data loading completed
2025-05-30 22:42:05.035 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 22:42:05.036 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 22:42:05.057 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 22:42:05.059 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-05-30 22:42:05.060 - Initializing StatsDataProvider
2025-05-30 22:42:05.079 - Loaded 8 items from cache
2025-05-30 22:42:05.082 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-05-30 22:42:05.087 - Starting background data loading thread
2025-05-30 22:42:05.095 - Background data loading started
2025-05-30 22:42:05.095 - StatsDataProvider initialization completed
2025-05-30 22:42:05.099 - Loading summary statistics
2025-05-30 22:42:05.105 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 22:42:05.105 - Created singleton instance of StatsDataProvider on module import
2025-05-30 22:42:05.106 - Data from GameStatsIntegration: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 22:42:05.108 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-05-30 22:42:05.108 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 22:42:05.109 - get_stats_provider called, returning provider with initialized=True
2025-05-30 22:42:05.109 - Successfully loaded summary stats: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 22:42:05.111 - Successfully loaded game history: 10 entries
2025-05-30 22:42:05.111 - Successfully loaded weekly stats: 7 days
2025-05-30 22:42:05.115 - Saved 10 items to cache
2025-05-30 22:42:05.116 - Background data loading completed
2025-05-30 22:55:29.313 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 22:55:29.318 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 22:55:29.339 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 22:55:29.341 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-05-30 22:55:29.342 - Initializing StatsDataProvider
2025-05-30 22:55:29.360 - Loaded 8 items from cache
2025-05-30 22:55:29.361 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-05-30 22:55:29.368 - Starting background data loading thread
2025-05-30 22:55:29.372 - StatsDataProvider initialization completed
2025-05-30 22:55:29.375 - Created singleton instance of StatsDataProvider on module import
2025-05-30 22:55:29.375 - Loading summary statistics
2025-05-30 22:55:29.382 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-05-30 22:55:29.383 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 22:55:29.385 - get_stats_provider called, returning provider with initialized=True
2025-05-30 22:55:29.385 - Data from GameStatsIntegration: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 22:55:29.398 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 22:55:29.402 - Successfully loaded summary stats: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 22:55:29.404 - Successfully loaded game history: 10 entries
2025-05-30 22:55:29.405 - Successfully loaded weekly stats: 7 days
2025-05-30 22:55:29.410 - Saved 10 items to cache
2025-05-30 22:55:29.412 - Background data loading completed
2025-05-30 23:12:01.629 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 23:12:01.633 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 23:12:01.654 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 23:12:01.655 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-05-30 23:12:01.657 - Initializing StatsDataProvider
2025-05-30 23:12:01.680 - Loaded 8 items from cache
2025-05-30 23:12:01.687 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-05-30 23:12:01.691 - Starting background data loading thread
2025-05-30 23:12:01.695 - Background data loading started
2025-05-30 23:12:01.695 - StatsDataProvider initialization completed
2025-05-30 23:12:01.700 - Loading summary statistics
2025-05-30 23:12:01.701 - Created singleton instance of StatsDataProvider on module import
2025-05-30 23:12:01.703 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 23:12:01.704 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-05-30 23:12:01.704 - Data from GameStatsIntegration: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 23:12:01.706 - get_stats_provider called, returning provider with initialized=True
2025-05-30 23:12:01.706 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 23:12:01.708 - Successfully loaded summary stats: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 23:12:01.712 - Successfully loaded game history: 10 entries
2025-05-30 23:12:01.714 - Successfully loaded weekly stats: 7 days
2025-05-30 23:12:01.718 - Saved 10 items to cache
2025-05-30 23:12:01.719 - Background data loading completed
2025-05-30 23:22:24.290 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 23:22:24.292 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 23:22:24.327 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 23:22:24.328 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-05-30 23:22:24.330 - Initializing StatsDataProvider
2025-05-30 23:22:24.349 - Loaded 8 items from cache
2025-05-30 23:22:24.353 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-05-30 23:22:24.358 - Starting background data loading thread
2025-05-30 23:22:24.364 - Background data loading started
2025-05-30 23:22:24.364 - StatsDataProvider initialization completed
2025-05-30 23:22:24.371 - Loading summary statistics
2025-05-30 23:22:24.375 - Created singleton instance of StatsDataProvider on module import
2025-05-30 23:22:24.376 - Attempting to load summary stats from GameStatsIntegration
2025-05-30 23:22:24.377 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-05-30 23:22:24.379 - get_stats_provider called, returning provider with initialized=True
2025-05-30 23:22:24.382 - Data from GameStatsIntegration: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 23:22:24.391 - Successfully loaded summary stats from GameStatsIntegration
2025-05-30 23:22:24.396 - Successfully loaded summary stats: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 23:22:24.399 - Successfully loaded game history: 10 entries
2025-05-30 23:22:24.400 - Successfully loaded weekly stats: 7 days
2025-05-30 23:22:24.406 - Saved 10 items to cache
2025-05-30 23:22:24.409 - Background data loading completed
