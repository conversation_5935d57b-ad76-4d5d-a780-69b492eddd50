# -*- coding: utf-8 -*-

import sys
import os
import subprocess

exit_code = subprocess.call(
    ['C:\\ProgramData\\anaconda3\\python.exe', '-W', 'ignore', 'C:\\PROGRA~3\\ANACON~1\\Lib\\SITE-P~1\\nuitka\\build\\INLINE~1\\bin\\scons.py', '--quiet', '-f', 'C:\\PROGRA~3\\ANACON~1\\Lib\\SITE-P~1\\nuitka\\build\\BACKEN~1.SCO', '--jobs', '4', '--warn=no-deprecated', '--no-site-dir', 'nuitka_src=C:\\PROGRA~3\\ANACON~1\\Lib\\SITE-P~1\\nuitka\\build', 'python_version=3.9', 'python_prefix=C:\\ProgramData\\anaconda3', 'experimental=', 'debug_modes=', 'deployment=false', 'no_deployment=', 'assume_yes_for_downloads=true', 'clang_mode=true', 'console_mode=disable', 'lto_mode=yes', 'noelf_mode=true', 'anaconda_python=true', 'cpp_defines=_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1', 'target_arch=x86_64', 'module_mode=false', 'dll_mode=true', 'exe_mode=false', 'standalone_mode=true', 'onefile_mode=true', 'onefile_temp_mode=true', 'gil_mode=true', 'source_dir=.', 'nuitka_python=false', 'debug_mode=false', 'debugger_mode=false', 'python_debug=false', 'full_compat=false', 'trace_mode=false', 'file_reference_mode=runtime', 'compiled_module_count=1288', 'result_exe=D:\\GAME PROJECTS\\LAST-GAME_CONCEPT-\\build_optimized\\main.dist\\main.dll', 'frozen_modules=153', 'python_sysflag_no_site=true'],
    env={'ALLUSERSPROFILE': 'C:\\ProgramData','APPDATA': 'C:\\Users\\<USER>\\AppData\\Roaming','BUILD_DIR': 'build_optimized','CHOCOLATEYINSTALL': 'C:\\ProgramData\\chocolatey','CHOCOLATEYLASTPATHUPDATE': '133774651800965011','CHROME_CRASHPAD_PIPE_NAME': '\\\\.\\pipe\\crashpad_3680_GEVEOSLULYQAEHLG','COMMONPROGRAMFILES': 'C:\\Program Files\\Common Files','COMMONPROGRAMFILES(X86)': 'C:\\Program Files (x86)\\Common Files','COMMONPROGRAMW6432': 'C:\\Program Files\\Common Files','COMPUTERNAME': 'DESKTOP-HD6UHPP','COMSPEC': 'C:\\Windows\\system32\\cmd.exe','CONDA_DEFAULT_ENV': 'base','CONDA_EXE': 'C:\\ProgramData\\anaconda3\\Scripts\\conda.exe','CONDA_PREFIX': 'C:\\ProgramData\\anaconda3','CONDA_PROMPT_MODIFIER': '(base) ','CONDA_PYTHON_EXE': 'C:\\ProgramData\\anaconda3\\python.exe','CONDA_SHLVL': '1','CPU_COUNT': '4','CUDA_PATH': 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8','CUDA_PATH_V11_8': 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8','CYAN': '\x1b [36m','DRIVERDATA': 'C:\\Windows\\System32\\Drivers\\DriverData','EFC_1972': '1','ESC': '\x1b ','FPS_BROWSER_APP_PROFILE_STRING': 'Internet Explorer','FPS_BROWSER_USER_PROFILE_STRING': 'Default','GOPATH': 'C:\\Users\\<USER>\\go','GREEN': '\x1b [32m','HOMEDRIVE': 'C:','HOMEPATH': '\\Users\\Abrsh-1','ICON_PATH': 'assets\\app_logo.ico','JAVA_HOME': 'C:\\Program Files\\Eclipse Adoptium\\jdk-*********-hotspot\\','KMP_BLOCKTIME': '0','LOCALAPPDATA': 'C:\\Users\\<USER>\\AppData\\Local','LOGONSERVER': '\\\\DESKTOP-HD6UHPP','MAIN_SCRIPT': 'main.py','NUMBER_OF_PROCESSORS': '4','NVTOOLSEXT_PATH': 'C:\\Program Files\\NVIDIA Corporation\\NvToolsExt\\','OMP_WAIT_POLICY': 'PASSIVE','ONEDRIVE': 'C:\\Users\\<USER>\\OneDrive','OPENSSL_CONF': 'C:\\Program Files\\PostgreSQL\\psqlODBC\\etc\\openssl.cnf','ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined','OS': 'Windows_NT','OUTPUT_NAME': 'WOWBingoGame','PATH': 'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\cv2\\../../x64/vc14/bin;C:\\ProgramData\\anaconda3;C:\\ProgramData\\anaconda3\\Library\\mingw-w64\\bin;C:\\ProgramData\\anaconda3\\Library\\usr\\bin;C:\\ProgramData\\anaconda3\\Library\\bin;C:\\ProgramData\\anaconda3\\Scripts;C:\\ProgramData\\anaconda3\\bin;C:\\ProgramData\\anaconda3\\condabin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Program Files\\Eclipse Adoptium\\jdk-*********-hotspot\\bin;C:\\Program Files\\Python310\\Scripts;C:\\Program Files\\Python310;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\Intel\\WiFi\\bin;C:\\Program Files\\Common Files\\Intel\\WirelessCommon;C:\\Program Files\\dotnet;C:\\ffmpeg\\bin;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0;C:\\Program Files (x86)\\NVIDIA Corporation\\Phy;C:\\Program Files\\Pandoc;C:\\Program Files\\Git\\cmd;C:\\Users\\<USER>\\.bun\\bin;C:\\Program Files\\Void\\bin;C:\\Program Files\\nodejs;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Program Files\\Eclipse Adoptium\\jdk-*********-hotspot\\bin;C:\\Program Files\\Python310\\Scripts;C:\\Program Files\\Python310;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\Intel\\WiFi\\bin;C:\\Program Files\\Common Files\\Intel\\WirelessCommon;C:\\Program Files\\dotnet;C:\\ffmpeg\\bin;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0;C:\\Program Files (x86)\\NVIDIA Corporation\\Phy;C:\\Program Files\\Pandoc;C:\\Program Files\\Git\\cmd;C:\\Users\\<USER>\\.bun\\bin;C:\\Program Files\\Void\\bin;C:\\Program Files\\nodejs;.;C:\\UV_TOOL\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Julia-1.11.5\\bin;C:\\;C:\\adb;.;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts','PATHEXT': '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL','PROCESSOR_ARCHITECTURE': 'AMD64','PROCESSOR_IDENTIFIER': 'Intel64 Family 6 Model 60 Stepping 3, GenuineIntel','PROCESSOR_LEVEL': '6','PROCESSOR_REVISION': '3c03','PROGRAMDATA': 'C:\\ProgramData','PROGRAMFILES': 'C:\\Program Files','PROGRAMFILES(X86)': 'C:\\Program Files (x86)','PROGRAMW6432': 'C:\\Program Files','PROJECT_NAME': 'WOW Bingo Game','PROJECT_VERSION': '1.0.0','PROMPT': '$P$G','PSMODULEPATH': 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules','PUBLIC': 'C:\\Users\\<USER>\x1b [31m','RESET': '\x1b [0m','SESSIONNAME': 'Console','SYNAPROGDIR': 'Synaptics\\SynTP','SYSTEMDRIVE': 'C:','SYSTEMROOT': 'C:\\Windows','TEMP': 'C:\\Users\\<USER>\\AppData\\Local\\Temp','TESSDATA_PREFIX': 'C:\\Program Files\\Tesseract-OCR\\tessdata','TMP': 'C:\\Users\\<USER>\\AppData\\Local\\Temp','USERDOMAIN': 'DESKTOP-HD6UHPP','USERDOMAIN_ROAMINGPROFILE': 'DESKTOP-HD6UHPP','USERNAME': 'WOW-Games','USERPROFILE': 'C:\\Users\\<USER>\\Windows','YELLOW': '\x1b [33m','_CONDA_EXE': 'C:\\ProgramData\\anaconda3\\Scripts\\conda.exe','_CONDA_ROOT': 'C:\\ProgramData\\anaconda3','__PSLOCKDOWNPOLICY': '0','LANG': 'en_US.UTF-8','PYDEVD_DISABLE_FILE_VALIDATION': '1','BUNDLED_DEBUGPY_PATH': 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy','NUITKA_PYTHON_EXE_PATH': 'C:\\ProgramData\\anaconda3\\python.exe','NUITKA_PACKAGE_DIR': 'C:\\ProgramData\\anaconda3\\lib\\site-packages\\nuitka','NUITKA_CACHE_DIR_DOWNLOADS': 'C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1','NUITKA_COMPANY_NAME': 'WOW Bingo Game','NUITKA_PRODUCT_NAME': 'WOW Bingo Game','NUITKA_VERSION_COMBINED': '1.0.0','_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT': '5000','_NUITKA_BUILD_DEFINITIONS_CATALOG': 'NUITKA_COMPANY_NAME,NUITKA_PRODUCT_NAME,NUITKA_VERSION_COMBINED,_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT,_NUITKA_BUILD_DEFINITIONS_CATALOG','NUITKA_QUIET': '0'},
    shell=False
)