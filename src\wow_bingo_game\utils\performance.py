"""
WOW Bingo Game - Performance Monitoring Utilities
=================================================

Performance monitoring and optimization utilities for the application.

Features:
- Real-time performance monitoring
- Memory usage tracking
- Frame rate monitoring
- Performance profiling
- Automatic optimization
- Performance reporting
"""

import time
import threading
from typing import Dict, List, Optional, Callable, Any
from collections import deque
from dataclasses import dataclass, field
import asyncio

from loguru import logger

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure."""

    # Timing metrics
    frame_times: deque = field(default_factory=lambda: deque(maxlen=60))
    avg_frame_time: float = 0.0
    current_fps: float = 0.0
    target_fps: float = 60.0

    # Memory metrics
    memory_usage_mb: float = 0.0
    memory_peak_mb: float = 0.0
    memory_limit_mb: float = 512.0

    # CPU metrics
    cpu_usage_percent: float = 0.0
    cpu_cores_used: int = 0

    # Application metrics
    startup_time: float = 0.0
    total_runtime: float = 0.0

    # Performance flags
    low_performance_mode: bool = False
    optimization_level: str = "medium"

    # Counters
    frame_count: int = 0
    gc_collections: int = 0
    cache_hits: int = 0
    cache_misses: int = 0


class PerformanceMonitor:
    """Real-time performance monitoring system."""

    def __init__(self, target_fps: float = 60.0, memory_limit_mb: float = 512.0):
        """Initialize performance monitor.

        Args:
            target_fps: Target frame rate
            memory_limit_mb: Memory usage limit in MB
        """
        self.metrics = PerformanceMetrics(
            target_fps=target_fps,
            memory_limit_mb=memory_limit_mb
        )

        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.start_time = time.time()
        self.last_frame_time = time.time()

        # Performance callbacks
        self.performance_callbacks: List[Callable[[PerformanceMetrics], None]] = []
        self.warning_callbacks: List[Callable[[str, Any], None]] = []

        # Thresholds for warnings
        self.fps_warning_threshold = 20.0
        self.memory_warning_threshold = 0.8  # 80% of limit
        self.cpu_warning_threshold = 90.0

        logger.info(f"Performance monitor initialized (target: {target_fps} FPS)")

    def start(self) -> None:
        """Start performance monitoring."""
        if self.monitoring:
            return

        self.monitoring = True
        self.start_time = time.time()

        if PSUTIL_AVAILABLE:
            self.monitor_thread = threading.Thread(
                target=self._monitor_loop,
                daemon=True
            )
            self.monitor_thread.start()
            logger.info("Performance monitoring started")
        else:
            logger.warning("Performance monitoring limited (psutil not available)")

    def stop(self) -> None:
        """Stop performance monitoring."""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        logger.info("Performance monitoring stopped")

    def _monitor_loop(self) -> None:
        """Main monitoring loop (runs in separate thread)."""
        while self.monitoring:
            try:
                self._update_system_metrics()
                self._check_performance_warnings()
                time.sleep(1.0)  # Update every second

            except Exception as e:
                logger.error(f"Performance monitoring error: {e}")
                time.sleep(5.0)  # Wait longer on error

    def _update_system_metrics(self) -> None:
        """Update system performance metrics."""
        if not PSUTIL_AVAILABLE:
            return

        try:
            # Memory metrics
            process = psutil.Process()
            memory_info = process.memory_info()
            self.metrics.memory_usage_mb = memory_info.rss / (1024 * 1024)
            self.metrics.memory_peak_mb = max(
                self.metrics.memory_peak_mb,
                self.metrics.memory_usage_mb
            )

            # CPU metrics
            self.metrics.cpu_usage_percent = process.cpu_percent()

            # Runtime
            self.metrics.total_runtime = time.time() - self.start_time

        except Exception as e:
            logger.debug(f"Error updating system metrics: {e}")

    def _check_performance_warnings(self) -> None:
        """Check for performance issues and trigger warnings."""
        # FPS warning
        if self.metrics.current_fps < self.fps_warning_threshold:
            self._trigger_warning(
                "low_fps",
                f"Low FPS detected: {self.metrics.current_fps:.1f}"
            )

        # Memory warning
        memory_usage_ratio = self.metrics.memory_usage_mb / self.metrics.memory_limit_mb
        if memory_usage_ratio > self.memory_warning_threshold:
            self._trigger_warning(
                "high_memory",
                f"High memory usage: {self.metrics.memory_usage_mb:.1f}MB "
                f"({memory_usage_ratio*100:.1f}% of limit)"
            )

        # CPU warning
        if self.metrics.cpu_usage_percent > self.cpu_warning_threshold:
            self._trigger_warning(
                "high_cpu",
                f"High CPU usage: {self.metrics.cpu_usage_percent:.1f}%"
            )

    def _trigger_warning(self, warning_type: str, message: str) -> None:
        """Trigger a performance warning."""
        logger.warning(f"Performance warning ({warning_type}): {message}")

        for callback in self.warning_callbacks:
            try:
                callback(warning_type, message)
            except Exception as e:
                logger.error(f"Warning callback error: {e}")

    def record_frame(self) -> None:
        """Record a frame for FPS calculation."""
        current_time = time.time()
        frame_time = current_time - self.last_frame_time

        self.metrics.frame_times.append(frame_time)
        self.metrics.frame_count += 1
        self.last_frame_time = current_time

        # Calculate average FPS
        if len(self.metrics.frame_times) > 0:
            avg_frame_time = sum(self.metrics.frame_times) / len(self.metrics.frame_times)
            self.metrics.avg_frame_time = avg_frame_time
            self.metrics.current_fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0.0

        # Trigger performance callbacks
        for callback in self.performance_callbacks:
            try:
                callback(self.metrics)
            except Exception as e:
                logger.error(f"Performance callback error: {e}")

    def add_performance_callback(self, callback: Callable[[PerformanceMetrics], None]) -> None:
        """Add a performance monitoring callback.

        Args:
            callback: Function to call with performance metrics
        """
        self.performance_callbacks.append(callback)

    def add_warning_callback(self, callback: Callable[[str, Any], None]) -> None:
        """Add a performance warning callback.

        Args:
            callback: Function to call when performance warnings occur
        """
        self.warning_callbacks.append(callback)

    def apply_hardware_profile(self, hardware_profile: Dict[str, Any]) -> None:
        """Apply hardware-specific optimization profile.

        Args:
            hardware_profile: Hardware optimization profile dictionary
        """
        try:
            logger.info(f"Applying hardware profile: {hardware_profile.get('profile_name', 'Unknown')}")

            # Update target FPS based on hardware capabilities
            self.metrics.target_fps = hardware_profile.get('max_fps', 60.0)

            # Adjust memory limit based on hardware profile
            memory_optimization = hardware_profile.get('memory_optimization', 'balanced')
            if memory_optimization == 'aggressive':
                self.metrics.memory_limit_mb = 256.0
            elif memory_optimization == 'balanced':
                self.metrics.memory_limit_mb = 512.0
            else:  # conservative
                self.metrics.memory_limit_mb = 1024.0

            # Set optimization level based on hardware acceleration
            if hardware_profile.get('gpu_acceleration', False):
                self.metrics.optimization_level = "gpu_accelerated"
                # GPU acceleration allows for higher quality settings
                self.fps_warning_threshold = 30.0  # Higher threshold for GPU systems
                self.memory_warning_threshold = 0.7  # Less aggressive memory management
            else:
                self.metrics.optimization_level = "cpu_optimized"
                # CPU-only systems need more conservative settings
                self.fps_warning_threshold = 20.0
                self.memory_warning_threshold = 0.8

            # Enable/disable performance monitoring based on profile
            if not hardware_profile.get('performance_monitoring', True):
                self.stop()

            logger.info(f"Hardware profile applied: Target FPS={self.metrics.target_fps}, "
                       f"Memory Limit={self.metrics.memory_limit_mb}MB, "
                       f"Optimization={self.metrics.optimization_level}")

        except Exception as e:
            logger.error(f"Failed to apply hardware profile: {e}")

    def get_hardware_optimized_settings(self) -> Dict[str, Any]:
        """Get hardware-optimized settings for the application.

        Returns:
            Dictionary with optimized settings
        """
        return {
            "target_fps": self.metrics.target_fps,
            "memory_limit_mb": self.metrics.memory_limit_mb,
            "optimization_level": self.metrics.optimization_level,
            "low_performance_mode": self.metrics.low_performance_mode,
            "current_fps": self.metrics.current_fps,
            "memory_usage_mb": self.metrics.memory_usage_mb,
            "cpu_usage_percent": self.metrics.cpu_usage_percent
        }

    def enable_low_performance_mode(self) -> None:
        """Enable low performance mode optimizations."""
        self.metrics.low_performance_mode = True
        self.metrics.optimization_level = "low"
        logger.info("Low performance mode enabled")

    def disable_low_performance_mode(self) -> None:
        """Disable low performance mode optimizations."""
        self.metrics.low_performance_mode = False
        self.metrics.optimization_level = "medium"
        logger.info("Low performance mode disabled")

    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report.

        Returns:
            Dictionary with performance statistics
        """
        return {
            "fps": {
                "current": round(self.metrics.current_fps, 2),
                "target": self.metrics.target_fps,
                "average_frame_time_ms": round(self.metrics.avg_frame_time * 1000, 2),
                "frame_count": self.metrics.frame_count
            },
            "memory": {
                "current_mb": round(self.metrics.memory_usage_mb, 2),
                "peak_mb": round(self.metrics.memory_peak_mb, 2),
                "limit_mb": self.metrics.memory_limit_mb,
                "usage_percent": round(
                    (self.metrics.memory_usage_mb / self.metrics.memory_limit_mb) * 100, 1
                )
            },
            "cpu": {
                "usage_percent": round(self.metrics.cpu_usage_percent, 1),
                "cores_used": self.metrics.cpu_cores_used
            },
            "runtime": {
                "total_seconds": round(self.metrics.total_runtime, 2),
                "startup_time": round(self.metrics.startup_time, 2)
            },
            "optimization": {
                "low_performance_mode": self.metrics.low_performance_mode,
                "optimization_level": self.metrics.optimization_level
            },
            "cache": {
                "hits": self.metrics.cache_hits,
                "misses": self.metrics.cache_misses,
                "hit_rate": (
                    self.metrics.cache_hits /
                    max(self.metrics.cache_hits + self.metrics.cache_misses, 1)
                )
            }
        }

    def log_performance_report(self) -> None:
        """Log performance report."""
        report = self.get_performance_report()

        logger.info("=== Performance Report ===")
        logger.info(f"FPS: {report['fps']['current']}/{report['fps']['target']} "
                   f"(avg frame time: {report['fps']['average_frame_time_ms']}ms)")
        logger.info(f"Memory: {report['memory']['current_mb']}MB "
                   f"({report['memory']['usage_percent']}% of limit)")
        logger.info(f"CPU: {report['cpu']['usage_percent']}%")
        logger.info(f"Runtime: {report['runtime']['total_seconds']}s")
        logger.info(f"Cache hit rate: {report['cache']['hit_rate']:.2%}")

        if self.metrics.low_performance_mode:
            logger.info("Low performance mode: ENABLED")


class PerformanceProfiler:
    """Performance profiling utilities."""

    def __init__(self):
        """Initialize performance profiler."""
        self.timers: Dict[str, float] = {}
        self.counters: Dict[str, int] = {}
        self.profiles: Dict[str, List[float]] = {}

    def start_timer(self, name: str) -> None:
        """Start a performance timer.

        Args:
            name: Timer name
        """
        self.timers[name] = time.perf_counter()

    def end_timer(self, name: str) -> float:
        """End a performance timer and return elapsed time.

        Args:
            name: Timer name

        Returns:
            Elapsed time in seconds
        """
        if name not in self.timers:
            logger.warning(f"Timer '{name}' was not started")
            return 0.0

        elapsed = time.perf_counter() - self.timers[name]
        del self.timers[name]

        # Store in profiles
        if name not in self.profiles:
            self.profiles[name] = []
        self.profiles[name].append(elapsed)

        return elapsed

    def increment_counter(self, name: str, amount: int = 1) -> None:
        """Increment a performance counter.

        Args:
            name: Counter name
            amount: Amount to increment
        """
        self.counters[name] = self.counters.get(name, 0) + amount

    def get_profile_stats(self, name: str) -> Dict[str, float]:
        """Get statistics for a profiled operation.

        Args:
            name: Profile name

        Returns:
            Dictionary with statistics
        """
        if name not in self.profiles or not self.profiles[name]:
            return {}

        times = self.profiles[name]
        return {
            "count": len(times),
            "total": sum(times),
            "average": sum(times) / len(times),
            "min": min(times),
            "max": max(times),
            "last": times[-1]
        }

    def clear_profiles(self) -> None:
        """Clear all profiling data."""
        self.timers.clear()
        self.counters.clear()
        self.profiles.clear()


# Global performance monitor instance
_performance_monitor: Optional[PerformanceMonitor] = None


def get_performance_monitor() -> PerformanceMonitor:
    """Get the global performance monitor instance."""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor


# Decorator for performance profiling
def profile_function(name: Optional[str] = None):
    """Decorator to profile function execution time.

    Args:
        name: Profile name (uses function name if None)
    """
    def decorator(func):
        profile_name = name or f"{func.__module__}.{func.__name__}"

        def wrapper(*args, **kwargs):
            profiler = PerformanceProfiler()
            profiler.start_timer(profile_name)
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                elapsed = profiler.end_timer(profile_name)
                logger.debug(f"Function {profile_name} took {elapsed:.4f}s")

        return wrapper
    return decorator


# Async version of the profiling decorator
def profile_async_function(name: Optional[str] = None):
    """Decorator to profile async function execution time.

    Args:
        name: Profile name (uses function name if None)
    """
    def decorator(func):
        profile_name = name or f"{func.__module__}.{func.__name__}"

        async def wrapper(*args, **kwargs):
            profiler = PerformanceProfiler()
            profiler.start_timer(profile_name)
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                elapsed = profiler.end_timer(profile_name)
                logger.debug(f"Async function {profile_name} took {elapsed:.4f}s")

        return wrapper
    return decorator
